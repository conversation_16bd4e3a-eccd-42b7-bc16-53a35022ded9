import 'package:tukxi/features/location_selection/data/models/available_option.dart';

abstract class AvailableProductsState {}

class AvailableProductsInitial extends AvailableProductsState {}

class AvailableProductsLoading extends AvailableProductsState {}

class AvailableProductsSuccess extends AvailableProductsState {
  final AvailableOption? availableOption;

  AvailableProductsSuccess({this.availableOption});
}

class AvailableProductsError extends AvailableProductsState {
  final String message;

  AvailableProductsError(this.message);
}
