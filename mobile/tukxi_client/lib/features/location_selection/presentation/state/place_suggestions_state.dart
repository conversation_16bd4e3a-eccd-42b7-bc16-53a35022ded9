import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

abstract class PlaceSuggestionsState {}

class PlaceSuggestionInitial extends PlaceSuggestionsState {}

class PlaceSuggestionLoading extends PlaceSuggestionsState {}

class PlaceSuggestionSuccess extends PlaceSuggestionsState {
  final List<PlaceSuggestion>? suggestions;
  final LocationParams? placeDetails;
  final LatLng? latLng;

  PlaceSuggestionSuccess({this.suggestions, this.latLng, this.placeDetails});
}

class PlaceSuggestionError extends PlaceSuggestionsState {
  final String message;

  PlaceSuggestionError(this.message);
}
