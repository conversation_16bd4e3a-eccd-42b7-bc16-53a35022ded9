import 'package:flutter/material.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/selected_location_view.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class RideLocationView extends StatefulWidget {
  const RideLocationView({
    super.key,
    required this.stops,
    required this.pickupLocation,
    required this.destinationLocation,
  });

  final List<LocationParams> stops;
  final LocationResponse? pickupLocation;
  final LocationResponse? destinationLocation;

  @override
  State<StatefulWidget> createState() {
    return _RideLocationViewState();
  }
}

class _RideLocationViewState extends State<RideLocationView> {
  bool isCollapsed = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.black10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 15),
            child: SelectedLocationView(
              title: 'Start Location',
              location: widget.pickupLocation?.address ?? '',
            ),
          ),

          // _buildStopLocationsView(),
          const Divider(thickness: 1, color: AppColors.black15),
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: SelectedLocationView(
              title: 'Your Destination',
              location: widget.destinationLocation?.address ?? '',
              isDestination: true,
            ),
          ),
        ],
      ),
    );
  }

  ///MARK: - Widgets
  // Widget _buildStopLocationsView() {
  //   return Column(
  //     mainAxisSize: MainAxisSize.min,
  //     children: [
  //       if (!isCollapsed) const Divider(thickness: 1, color: AppColors.black15),

  //       if (!isCollapsed)
  //         ListView.builder(
  //           padding: EdgeInsets.zero,
  //           shrinkWrap: true,
  //           physics: const NeverScrollableScrollPhysics(),
  //           itemCount: widget.stops.length,
  //           itemBuilder: (context, index) {
  //             final stop = widget.stops[index];
  //             return Padding(
  //               padding: EdgeInsets.only(
  //                 top: 5,
  //                 bottom: index < widget.stops.length - 1 ? 5 : 0,
  //               ),
  //               child: Column(
  //                 mainAxisSize: MainAxisSize.max,
  //                 children: [
  //                   SelectedLocationView(
  //                     isStop: true,
  //                     location: stop.locationName ?? '',
  //                   ),
  //                   if (index < widget.stops.length - 1)
  //                     const Divider(thickness: 1, color: AppColors.black15),
  //                 ],
  //               ),
  //             );
  //           },
  //         ),

  //       Stack(
  //         children: [
  //           Container(
  //             height: 30,
  //             width: double.infinity,
  //             alignment: Alignment.center,
  //             child: const Divider(thickness: 1, color: AppColors.black15),
  //           ),

  //           Positioned(
  //             top: 0,
  //             bottom: 0,
  //             right: 20,
  //             child: IconButton(
  //               onPressed: _displayStops,
  //               style: IconButton.styleFrom(backgroundColor: Colors.white),
  //               padding: EdgeInsets.symmetric(horizontal: 3),
  //               constraints: const BoxConstraints(),
  //               icon: Image.asset(AssetPaths.cash),
  //               // icon: SvgPicture.asset(
  //               //   isCollapsed ? AssetPaths.expand : AssetPaths.collapse,
  //               // ),
  //             ),
  //           ),
  //         ],
  //       ),
  //     ],
  //   );
  // }

  ///MARK: - Other Methods
  // void _displayStops() {
  //   setState(() {
  //     isCollapsed = !isCollapsed;
  //   });
  // }
}
