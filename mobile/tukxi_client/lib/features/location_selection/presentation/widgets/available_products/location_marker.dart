import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class LocationMarker extends StatelessWidget {
  const LocationMarker({super.key, required this.text, required this.maxWidth});

  final String text;
  final double maxWidth;

  static const textLength = 20;
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Container(
        padding: const EdgeInsets.only(left: 4, right: 8, top: 6, bottom: 6),
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(blurRadius: 2, offset: Offset(0, 1), color: Colors.grey),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Icon(Icons.place, size: 16, color: Colors.white),
            const SizedBox(width: 4),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: maxWidth / 2),
              child: Text(
                _formatText(text),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatText(String text) {
    if (text.length > textLength) {
      // Insert a line break after 20 chars
      return text.replaceRange(
        textLength,
        textLength + 1,
        "\n${text[textLength]}",
      );
    }
    return text;
  }
}
