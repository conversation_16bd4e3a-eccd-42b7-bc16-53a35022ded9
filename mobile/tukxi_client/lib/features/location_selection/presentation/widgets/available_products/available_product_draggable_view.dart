import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/drag_handle.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/presentation/state/available_products_state.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/available_products_list.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/rounded_box_icon_and_text.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/home/<USER>/widgets/home_section_title.dart';

class AvailableProductDraggableView extends StatelessWidget {
  const AvailableProductDraggableView({
    super.key,
    required this.rides,
    required this.pickupLocation,
    required this.destinationLocation,
    required this.scrollController,
    required this.selectedOption,
    required this.onAvailableOptionSelected,
    required this.state,
    required this.isRouteAvailable,
    this.isRouteLoading = false,
    this.hasAttemptedRoute = false,
  });

  final AvailableOptionData? selectedOption;
  final List<AvailableOptionData> rides;
  final LocationParams? pickupLocation;
  final LocationParams? destinationLocation;
  final ScrollController scrollController;
  final void Function(AvailableOptionData selectedProduct)
  onAvailableOptionSelected;
  final AvailableProductsState state;
  final bool isRouteAvailable;
  final bool isRouteLoading;
  final bool hasAttemptedRoute;

  @override
  Widget build(BuildContext context) {
    // final stops = [
    //   LocationParams(locationName: 'a'),
    //   LocationParams(locationName: 'b'),
    //   LocationParams(locationName: 'c'),
    //   LocationParams(locationName: 'd'),
    // ];

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 15, right: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
      ),
      child: CustomScrollView(
        controller: scrollController,
        physics: Platform.isAndroid
            ? const ClampingScrollPhysics()
            : const BouncingScrollPhysics(parent: ClampingScrollPhysics()),
        slivers: [
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                DragHandle(),
                const SizedBox(height: 25),
                // ✅ Only show error message after route attempt fails
                if (!isRouteAvailable && hasAttemptedRoute && !isRouteLoading)
                  Column(
                    children: [
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(10),
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          color: AppColors.greyBg,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          'No route found between your pickup and destination. Please try a different location.',
                          textAlign: TextAlign.start,
                          style: GoogleFonts.inter(
                            color: AppColors.red,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      SizedBox(height: 15),
                    ],
                  ),

                Row(
                  children: [
                    //TODO: pickup later
                    RoundedBoxIconAndText(
                      title: 'Me',
                      assetPath: AssetPaths.person,
                    ),

                    const SizedBox(width: 10),

                    RoundedBoxIconAndText(
                      title: 'Now', //'Aug, 09, 04: 24PM',
                      assetPath: AssetPaths.later,
                    ),
                  ],
                ),
                const SizedBox(height: 15),

                // RideLocationView(
                //   stops: [],
                //   pickupLocation: pickupLocation,
                //   destinationLocation: destinationLocation,
                // ),
                //const SizedBox(height: 15),
                SectionTitle(
                  title: 'Available Options',
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
          ),
          if (isRouteLoading ||
              rides.isEmpty && state is AvailableProductsLoading)
            SliverToBoxAdapter(
              child: Column(
                children: [
                  ShimmerLoaders.rideOptionShimmer(),
                  ShimmerLoaders.rideOptionShimmer(),
                  ShimmerLoaders.rideOptionShimmer(),
                ],
              ),
            )
          else if (rides.isNotEmpty)
            AvailableProductsList(
              selectedProduct: selectedOption,
              rides: rides,
              onAvailableOptionSelected: onAvailableOptionSelected,
            )
          else
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 60),
                child: Text(
                  'No rides available for the\n selected route.',
                  textAlign: TextAlign.center,
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w400,
                    color: AppColors.black50,
                  ),
                ),
              ),
            ),

          const SliverToBoxAdapter(
            child: SizedBox(height: UIConstants.kHomeContainerBorderRadius),
          ),
        ],
      ),
    );
  }
}
