import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class SelectedLocationView extends StatelessWidget {
  const SelectedLocationView({
    super.key,
    this.title,
    this.isStop = false,
    this.isDestination = false,
    required this.location,
  });

  final String? title;
  final String location;
  final bool isStop;
  final bool isDestination;

  @override
  Widget build(BuildContext context) {
    // return Padding(
    //   padding: (!isDestination && !isStop)
    //       ? EdgeInsets.only(top: 15)
    //       : EdgeInsets.only(bottom: isStop ? 5 : 15, top: isStop ? 5 : 0),
    //   child:

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(
          AssetPaths.yourLocation,
          color: isDestination ? AppColors.primary : Colors.black,
          height: 16,
          width: 16,
        ),
        const SizedBox(width: 10),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (!isStop)
                Text(
                  title ?? '',
                  style: GoogleFonts.inter(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: AppColors.black50,
                    height: 18 / 10,
                  ),
                ),
              Text(
                location,
                style: GoogleFonts.inter(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
      // ),
    );
  }
}
