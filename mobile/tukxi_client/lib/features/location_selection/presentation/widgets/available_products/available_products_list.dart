import 'package:flutter/material.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/available_product_card.dart';

class AvailableProductsList extends StatefulWidget {
  const AvailableProductsList({
    super.key,
    required this.rides,
    required this.selectedProduct,
    required this.onAvailableOptionSelected,
  });

  final AvailableOptionData? selectedProduct;
  final List<AvailableOptionData> rides;
  final void Function(AvailableOptionData selectedProduct)
  onAvailableOptionSelected;

  @override
  State<StatefulWidget> createState() {
    return _AvailableProductsListState();
  }
}

class _AvailableProductsListState extends State<AvailableProductsList> {
  int _selectedIndex = -1;

  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.rides.indexWhere(
      (ride) => ride.id == widget.selectedProduct?.id,
    );
    print(_selectedIndex);
  }

  @override
  Widget build(BuildContext context) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((context, index) {
        final ride = widget.rides[index];
        return Column(
          children: [
            AvailableProductCard(
              ride: ride,
              index: index,
              selectedIndex: _selectedIndex,
              onAvailableOptionSelected: (selectedIndex) {
                setState(() {
                  _selectedIndex = selectedIndex;
                });
                widget.onAvailableOptionSelected(widget.rides[selectedIndex]);
              },
            ),
            const SizedBox(height: 10),
          ],
        );
      }, childCount: widget.rides.length),
    );
  }
}
