import 'package:flutter/material.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/location_selection/location_selection_card.dart';

class LocationSelectionSection extends StatelessWidget {
  const LocationSelectionSection({
    super.key,
    required this.stops,
    required this.pickupController,
    required this.destinationController,
    required this.pickupFocusNode,
    required this.destinationFocusNode,
    required this.updateStops,
    required this.onPickupTextChanged,
    required this.onDestinationTextChanged,
  });

  final List<String> stops;
  final TextEditingController pickupController;
  final TextEditingController destinationController;

  final FocusNode pickupFocusNode;
  final FocusNode destinationFocusNode;

  final void Function(List<String> stops) updateStops;
  final void Function(String text, bool isCleared) onPickupTextChanged;
  final void Function(String text, bool isCleared) onDestinationTextChanged;

  ///MARK: - build Method
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.black10),
      ),
      child: Column(
        children: [
          LocationSelectionCard(
            hintText: AppConstants.pickupString,
            assetPath: AssetPaths.yourLocation,
            isStop: false,
            controller: pickupController,
            focusNode: pickupFocusNode,
            onTextChanged: onPickupTextChanged,
          ),

          /** 
           * TODO: - Uncomment the code on multi stopcase
            if (widget.stops.isNotEmpty)
             Column(
               children: [*/
          const Divider(
            indent: 16,
            endIndent: 16,
            thickness: 1,
            color: AppColors.black15,
          ),
          /** 
           *   ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: widget.stops.length,
                  itemBuilder: (context, index) {
                    return LocationSelectionCard(
                      hintText: widget.stops[index],
                      assetPath: AssetPaths.yourLocation,
                      isStop: true,
                    );
                  },
                ),
              ],
            ),
          Stack(
            children: [
              Container(
                height: 30,
                width: double.infinity,
                alignment: Alignment.center,
                child: const Divider(
                  indent: 16,
                  endIndent: 16,
                  thickness: 1,
                  color: AppColors.black15,
                ),
              ),

              Positioned(
                top: 0,
                bottom: 0,
                right: 20,
                child: IconButton(
                  onPressed: _updateStops,
                  style: IconButton.styleFrom(backgroundColor: Colors.white),
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  constraints: const BoxConstraints(),
                  icon: SvgPicture.asset(AssetPaths.add),
                ),
              ),
            ],
          ),
           */
          LocationSelectionCard(
            hintText: AppConstants.destinationString,
            assetPath: AssetPaths.destinationLocation,
            isStop: false,
            controller: destinationController,
            focusNode: destinationFocusNode,
            onTextChanged: onDestinationTextChanged,
          ),
        ],
      ),
    );
  }

  ///MARK: - Other Methods

  /** 
           * TODO: - Uncomment the code on multi stopcase
 void _updateStops() {
    final stops = widget.stops + ['Add new stop'];
    widget.updateStops(stops);
  }
  */
}
