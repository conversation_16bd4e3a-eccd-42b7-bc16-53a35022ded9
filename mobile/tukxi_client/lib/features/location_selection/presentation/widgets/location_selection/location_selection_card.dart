import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class LocationSelectionCard extends StatefulWidget {
  const LocationSelectionCard({
    super.key,
    required this.hintText,
    required this.assetPath,
    required this.isStop,
    required this.controller,
    required this.focusNode,
    required this.onTextChanged,
  });

  final String hintText;
  final String assetPath;
  final bool isStop;
  final FocusNode focusNode;
  final TextEditingController controller;
  final void Function(String text, bool isCleared)? onTextChanged;

  @override
  State<LocationSelectionCard> createState() => _LocationSelectionCardState();
}

class _LocationSelectionCardState extends State<LocationSelectionCard> {
  bool _showHint = false;
  bool _showTextClear = false;

  ///MARK: - Init State
  @override
  void initState() {
    super.initState();
    widget.focusNode.addListener(_handleFocusChange);
    widget.controller.addListener(_handleTextChange);
  }

  ///MARK: - Dispose
  @override
  void dispose() {
    widget.focusNode.removeListener(_handleFocusChange);
    widget.controller.removeListener(_handleTextChange);
    super.dispose();
  }

  ///MARK: - Build Methods
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(
        12,
        widget.hintText == AppConstants.pickupString ? 12 : 0,
        12,
        widget.hintText == AppConstants.pickupString ? 0 : 12,
      ),
      color: Colors.white,
      child: Row(
        children: [
          Image.asset(widget.assetPath, height: 14),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                AnimatedSwitcher(
                  duration: Duration(milliseconds: 250),
                  transitionBuilder: (child, animation) {
                    return SizeTransition(
                      sizeFactor: animation,
                      axisAlignment: -1.0,
                      child: child,
                    );
                  },
                  child: _showHint && !widget.isStop
                      ? Text(
                          widget.hintText,
                          key: ValueKey('hint_${widget.hintText}'),
                          textAlign: TextAlign.start,
                          style: GoogleFonts.inter(
                            color: AppColors.black50,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        )
                      : SizedBox.shrink(
                          key: ValueKey('empty_${widget.hintText}'),
                        ),
                ),
                SizedBox(
                  height: 35,
                  child: TextField(
                    autocorrect: false,
                    enableSuggestions: false,
                    keyboardType: TextInputType.text,
                    focusNode: widget.focusNode,
                    controller: widget.controller,
                    decoration: InputDecoration(
                      hintText: widget.hintText,
                      floatingLabelBehavior: FloatingLabelBehavior.never,
                      hintStyle: GoogleFonts.inter(color: AppColors.black50),
                      contentPadding: EdgeInsets.zero,
                      border: InputBorder.none,
                    ),
                    style: GoogleFonts.inter(
                      color: Colors.black87,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    onChanged: (text) {
                      widget.onTextChanged?.call(text, false);
                    },
                  ),
                ),
              ],
            ),
          ),
          if (_showTextClear)
            SizedBox(
              width: 30,
              height: 30,
              child: IconButton(
                onPressed: () {
                  widget.controller.clear();
                  widget.focusNode.requestFocus();
                  widget.onTextChanged?.call(widget.controller.text, true);
                },
                icon: SvgPicture.asset(AssetPaths.textClear, height: 12),
              ),
            ),
        ],
      ),
    );
  }

  ///MARK: - View Methods
  void _handleFocusChange() {
    if (mounted) {
      setState(() {
        _showHint = widget.focusNode.hasFocus;
        _showTextClear =
            widget.focusNode.hasFocus && widget.controller.text.isNotEmpty;
      });
    }
  }

  void _handleTextChange() {
    if (mounted) {
      setState(() {
        _showTextClear =
            widget.focusNode.hasFocus && widget.controller.text.isNotEmpty;
      });
    }
  }
}
