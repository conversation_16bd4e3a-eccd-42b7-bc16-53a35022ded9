import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';

class SelectLocationMapSection extends StatelessWidget {
  const SelectLocationMapSection({
    super.key,
    required this.mapHeight,
    required this.currentLocation,
    required this.onCameraIdle,
    required this.onMapCreated,
    required this.onCameraMove,
  });

  final LatLng? currentLocation;
  final double mapHeight;
  final void Function()? onCameraIdle;
  final void Function(GoogleMapController controller) onMapCreated;
  final void Function(CameraPosition position)? onCameraMove;
  @override
  Widget build(BuildContext context) {
    final pinHeight = 40.0;
    final bottomPadding = 24.0;
    final visibleMapHeight = mapHeight - bottomPadding;
    final mapCenterY = visibleMapHeight / 2;

    // ✅ Different calculations for Android vs iOS
    final pinTopPosition = Platform.isAndroid
        ? mapCenterY -
              (pinHeight * 0.75) // Android: pin anchor point adjustment
        : mapCenterY - pinHeight; //

    return Container(
      color: const Color.fromARGB(255, 227, 227, 229),
      height: mapHeight,
      child: (currentLocation == null)
          ? Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                GoogleMap(
                  padding: EdgeInsets.only(bottom: bottomPadding),
                  initialCameraPosition: CameraPosition(
                    target: currentLocation!,
                    zoom: LocationConstants.zoomLevel,
                  ),
                  myLocationEnabled: true,
                  myLocationButtonEnabled: true,
                  onMapCreated: onMapCreated,
                  onCameraMove: onCameraMove,
                  onCameraIdle: onCameraIdle,
                ),

                Positioned(
                  left: 0,
                  right: 0,
                  // top: ((mapHeight - bottomPadding) / 2) - pinHeight,
                  top: pinTopPosition,
                  child: Center(
                    child: Image.asset(
                      AssetPaths.selectPin,
                      width: pinHeight,
                      height: pinHeight,
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}
