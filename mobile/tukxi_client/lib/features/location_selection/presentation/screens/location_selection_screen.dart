import 'dart:async';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/location_selection/presentation/state/place_suggestions_state.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/location_selection/location_search_list.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/location_selection/location_selection_section.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/rounded_box_icon_and_text.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/home/<USER>/providers/location_provider.dart';
import 'package:tukxi/routes/app_routes.dart';

class LocationSelectionScreen extends ConsumerStatefulWidget {
  const LocationSelectionScreen({
    super.key,
    required this.pickupLocationCoordinate,
  });

  final LatLng? pickupLocationCoordinate;

  @override
  ConsumerState<LocationSelectionScreen> createState() {
    return _LocationSelectionScreenState();
  }
}

class _LocationSelectionScreenState
    extends ConsumerState<LocationSelectionScreen> {
  final _pickupController = TextEditingController(text: '');
  final _destinationController = TextEditingController(text: '');

  final _pickupFocus = FocusNode();
  final _destinationFocus = FocusNode();

  List<PlaceSuggestion> _locations = [];
  List<String> _stops = [];

  Timer? _debounceTimer;
  String searchText = '';

  bool _isPlaceLoading = false;
  bool _isEditingLocation = false;

  LocationType _currentLocationType = LocationType.destination;

  ///MARK: - Init state
  @override
  void initState() {
    super.initState();

    // First-frame pickup text population
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;
      final pickup = ref.read(pickupLocationProvider);
      final locationState = ref.read(locationProvider);
      if ((pickup?.locationName?.isNotEmpty ?? false) &&
          locationState.permissionGranted &&
          locationState.gpsEnabled) {
        _pickupController.text = pickup!.locationName!;
        ref.read(pickupLocationProvider.notifier).state = pickup;
      }
    });

    // Pickup Focus listener
    _pickupFocus.addListener(_handlePickupListner);

    // Destination Focus listener
    _destinationFocus.addListener(_handleDestinationListner);

    Future.microtask(() {
      /// If coordinate already passed, fetch once
      if (!mounted) return;
      _initialPlaceDetailsAndFocusHandling();
    });
  }

  ///MARK: - Dispose
  @override
  void dispose() {
    _debounceTimer?.cancel();

    _pickupController.dispose();
    _pickupFocus.dispose();

    _destinationController.dispose();
    _destinationFocus.dispose();

    super.dispose();
  }

  ///MARK: - Build Method
  @override
  Widget build(BuildContext context) {
    final placeSuggestionsState = ref.watch(placeSuggestionsProvider);

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: TitleBarWithBackButton(
        title: 'Where are you going?',
        titleStyle: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          height: 22 / 20,
        ),
        onBackPressed: () => context.pop(true),
      ),
      body: SafeArea(
        top: true,
        child: Container(
          padding: const EdgeInsets.only(left: 16.0, right: 16, bottom: 8),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (placeSuggestionsState is PlaceSuggestionLoading)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8.0),
                  child: LinearProgressIndicator(
                    trackGap: 10,
                    minHeight: 2.5,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppColors.primary.withValues(alpha: 0.5),
                    ),
                  ),
                ),
              Row(
                children: [
                  //TODO: Pickup later
                  RoundedBoxIconAndText(
                    title: 'Me',
                    assetPath: AssetPaths.person,
                  ),

                  const SizedBox(width: 10),

                  RoundedBoxIconAndText(
                    title: 'Now',
                    assetPath: AssetPaths.now,
                  ),
                ],
              ),

              const SizedBox(height: 10),

              /** 
               *  TODO: - Uncomment the code on multi stopcase
               *   Flexible(
                child: SingleChildScrollView(
                  child: */
              LocationSelectionSection(
                pickupController: _pickupController,
                destinationController: _destinationController,
                stops: _stops,
                pickupFocusNode: _pickupFocus,
                destinationFocusNode: _destinationFocus,
                updateStops: (stops) {
                  setState(() {
                    _stops = stops;
                  });
                },
                onPickupTextChanged: (text, isCleared) =>
                    _onLocationTextChanged(
                      type: LocationType.pickup,
                      value: text,
                      isCleared: isCleared,
                    ),
                onDestinationTextChanged: (text, isCleared) =>
                    _onLocationTextChanged(
                      type: LocationType.destination,
                      value: text,
                      isCleared: isCleared,
                    ),
              ),

              /**
               * * TODO: - Uncomment the code on multi stopcase ),
              ),*/
              Expanded(child: _displayPlaceSuggestions()),

              //const SizedBox(height: 10),
              // _buildSetLocationOnMapButon(),
            ],
          ),
        ),
      ),
    );
  }

  ///MARK: - Focus Listners
  void _handleDestinationListner() {
    if (_destinationFocus.hasFocus) {
      setState(() {
        _locations = [];
        _currentLocationType = LocationType.destination;

        // Update search text and trigger search if not empty
        searchText = _destinationController.text.trim();
      });

      if (searchText.isNotEmpty) {
        _debouncedSearch();
      }

      // Only populate pickup if still empty
      if (_pickupController.text.trim().isEmpty) {
        final pickupLocation = ref.read(pickupLocationProvider);
        _pickupController.text = pickupLocation?.locationName ?? '';
      }
    }
  }

  void _handlePickupListner() {
    if (_pickupFocus.hasFocus) {
      setState(() {
        _locations = [];
        _currentLocationType = LocationType.pickup;
        // Update search text and trigger search if not empty
        searchText = _pickupController.text.trim();
      });

      if (searchText.isNotEmpty) {
        _debouncedSearch();
      }
    }
  }

  ///MARK: - Widgets
  Widget _displayPlaceSuggestions() {
    final placeSuggestionsState = ref.watch(placeSuggestionsProvider);

    // Determine active location type based on focus
    final activeLocationType = _pickupFocus.hasFocus
        ? LocationType.pickup
        : _destinationFocus.hasFocus
        ? LocationType.destination
        : null;

    final shouldShowSuggestionsWithSelectLocationOnMapButton =
        _isEditingLocation &&
        activeLocationType != null &&
        _currentLocationType == activeLocationType;

    final shouldShowNoResults =
        shouldShowSuggestionsWithSelectLocationOnMapButton &&
        _locations.isEmpty &&
        placeSuggestionsState is! PlaceSuggestionLoading &&
        !_isPlaceLoading &&
        searchText.isNotEmpty;

    return Column(
      children: [
        // Show "No results" only when editing, correct type, and no results found
        if (shouldShowNoResults)
          Expanded(
            child: Center(
              child: Text(
                'No results found!',
                style: GoogleFonts.inter(
                  fontSize: 18,
                  fontWeight: FontWeight.w400,
                  color: AppColors.black50,
                ),
              ),
            ),
          ),

        // Show suggestions only if editing AND the suggestions belong to the active field
        Expanded(
          child: LocationSearchList(
            locations: _locations,
            onLocationSelected: (location) {
              _onLocationSelected(location: location);
            },
            setLocationOnMapTapped: _setLocationOnMapTapped,
            saveAddressButtonTapped: _viewSavedaddressButtonTapped,
          ),
        ),
      ],
    );
  }

  ///MARK: - API Call
  ///
  Future<void> _handleApiCall<T>(
    Future<Either<Failure, T>> Function() apiCall, {
    Function(T response)? onSuccess,
    String? errorMessage,
  }) async {
    final result = await apiCall();
    if (!mounted) return;

    result.fold(
      (failure) {
        if (!mounted) return;
        _isPlaceLoading = false;
        handleApiError(
          context: context,
          failure: failure,
          errorMessage: errorMessage,
          onRetry: () async => _handleApiCall(apiCall, onSuccess: onSuccess),
        );
      },
      (response) {
        if (!mounted) return;
        onSuccess?.call(response);
      },
    );
  }

  Future _fetchPlaceDetails({required LatLng? coordinates}) async {
    if (coordinates == null) return;
    final locationState = ref.read(locationProvider);

    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getAccurateLocation(coordinates),
      onSuccess: (placeDetails) {
        if (!locationState.gpsEnabled || !locationState.permissionGranted) {
          return;
        }
        setState(() {
          _pickupController.text = placeDetails.locationName ?? '';
          ref.read(pickupLocationProvider.notifier).state = placeDetails;
        });
        print('success');
      },
      errorMessage: 'Unable to search please try again later',
    );
  }

  Future _fetchPlaceSuggestions(bool isDestination) async {
    final pickup = ref.read(pickupLocationProvider);

    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getPlaceSuggestions(
            query: searchText,
            location:
                (isDestination &&
                    _pickupController.text.isNotEmpty &&
                    pickup?.latLng != null)
                ? pickup?.latLng
                : widget.pickupLocationCoordinate,
          ),
      onSuccess: (locationList) {
        setState(() {
          _locations = locationList;
          _isPlaceLoading = false;
        });
      },
      errorMessage: 'Unable to search please try again later',
    );
  }

  Future _fetchCoordinatesAsynchronously({
    required PlaceSuggestion location,
  }) async {
    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getCoordinatesFromPlaceID(location.placeId),
      onSuccess: (coordinates) {
        if (!mounted || coordinates == null) return;

        _focusAndNavigation(location: location, coordinates: coordinates);
      },
      errorMessage: 'Unable to fetch location',
    );
  }

  ///MARK: - Other Methods

  void _initialPlaceDetailsAndFocusHandling() {
    if (!mounted) return;

    if (widget.pickupLocationCoordinate != null) {
      _currentLocationType = LocationType.destination;
      FocusScope.of(context).requestFocus(_destinationFocus);

      _setInitialPickupNameIfavailable();
    } else {
      _currentLocationType = LocationType.pickup;
      FocusScope.of(context).requestFocus(_pickupFocus);
    }
  }

  void _setInitialPickupNameIfavailable() {
    final pickupPlace = ref.read(pickupLocationProvider);
    if (pickupPlace == null ||
        pickupPlace.locationName == null ||
        pickupPlace.locationName!.isEmpty) {
      _fetchPlaceDetails(coordinates: widget.pickupLocationCoordinate);
    } else {
      _pickupController.text = pickupPlace.locationName ?? '';
    }
  }

  void _onLocationTextChanged({
    required LocationType type,
    required String value,
    required bool isCleared,
  }) {
    setState(() {
      _currentLocationType = type;
      searchText = value.trim();
    });
    _debouncedSearch();
  }

  void _viewSavedaddressButtonTapped() async {
    final result = await context.push<Map<String, dynamic>>(
      AppRoutes.listSavedAddress,
    );
    if (result != null) {
      final address = result['address'] as FavouriteLocation;
      final pickedAddress = LocationParams(
        id: address.id ?? '',
        latLng: LatLng(
          address.location?.latitude ?? 0,
          address.location?.longitude ?? 0,
        ),
        locationAddress: address.meta?.address,
        locationName: address.description,
      );

      if (_currentLocationType == LocationType.pickup) {
        ref.read(pickupLocationProvider.notifier).state = pickedAddress;
        _pickupController.text = pickedAddress.locationName ?? '';
      } else {
        ref.read(destinationLocationProvider.notifier).state = pickedAddress;
        _destinationController.text = pickedAddress.locationName ?? '';
      }
      _checkAndNavigateToAvailableOptions();
    }
  }

  void _setLocationOnMapTapped() async {
    _unfocusAll();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.delayed(const Duration(milliseconds: 50));

      if (!mounted) return;

      final result = await context.push<Map<String, dynamic>>(
        AppRoutes.selectLocationOnMap,
        extra: {
          'locationType': _currentLocationType,
          'pickedLocation': _currentLocationType == LocationType.pickup
              ? ref.read(pickupLocationProvider)
              : ref.read(destinationLocationProvider),
          'userLocation': _pickupController.text.trim().isNotEmpty
              ? widget.pickupLocationCoordinate
              : ref.read(locationProvider).ipLocation,
        },
      );

      if (result == null) return;

      debugPrint(result.toString());
      final locationType = result['locationType'] as LocationType;
      final pickedLocation = result['selectedLocation'] as LocationParams?;

      if (pickedLocation == null) {
        //Location not picked: Means tapped on search from select location on map screen

        if (!mounted) return;
        if (locationType == LocationType.pickup) {
          FocusScope.of(context).requestFocus(_pickupFocus);
        } else {
          FocusScope.of(context).requestFocus(_destinationFocus);
        }
      } else {
        //Location Picked from select location on map screen
        if (locationType == LocationType.pickup) {
          ref.read(pickupLocationProvider.notifier).state = pickedLocation;
          _pickupController.text = pickedLocation.locationName ?? '';
        } else {
          ref.read(destinationLocationProvider.notifier).state = pickedLocation;
          _destinationController.text = pickedLocation.locationName ?? '';
        }

        //Navigate to available option once locations picked
        _checkAndNavigateToAvailableOptions();
      }
    });
  }

  void _focusAndNavigation({
    required PlaceSuggestion location,
    required LatLng coordinates,
  }) {
    final selectedLocation = LocationParams(
      locationName: location.name,
      latLng: coordinates,
      id: location.placeId,
      locationAddress: location.address,
    );

    // Update provider based on field active at selection time
    if (_currentLocationType == LocationType.pickup) {
      ref.read(pickupLocationProvider.notifier).state = selectedLocation;
    } else {
      ref.read(destinationLocationProvider.notifier).state = selectedLocation;
    }

    _checkAndNavigateToAvailableOptions();
  }

  void _checkAndNavigateToAvailableOptions() {
    //If both locations are picked, navigate to available ride option screen
    final pickupSet = _pickupController.text.trim().isNotEmpty;
    final destinationSet = _destinationController.text.trim().isNotEmpty;

    if (pickupSet && !destinationSet) {
      FocusScope.of(context).requestFocus(_destinationFocus);
    } else if (!pickupSet && destinationSet) {
      FocusScope.of(context).requestFocus(_pickupFocus);
    } else if (pickupSet && destinationSet) {
      context.push(
        AppRoutes.availableProducts,
        extra: {
          'pickupLocation': ref.read(pickupLocationProvider),
          'destinationLocation': ref.read(destinationLocationProvider),
          'rideTimeOption': RideTimeOption.now,
          //TODO: change to dynamic ride time option once it is implemented
        },
      );
    }
  }

  void _debouncedSearch() {
    _debounceTimer?.cancel();

    final query = searchText.trim();
    if (query.isEmpty) {
      setState(() {
        _isEditingLocation = false;
        _isPlaceLoading = false;
        _locations = [];
      });
      return;
    }

    // mark as loading immediately
    setState(() {
      _isEditingLocation = true;
      _isPlaceLoading = true;
    });

    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      if (query.isNotEmpty) {
        setState(() => _isEditingLocation = true);

        // Only continue if current field still has focus
        final hasFocus =
            (_currentLocationType == LocationType.pickup &&
                _pickupFocus.hasFocus) ||
            (_currentLocationType == LocationType.destination &&
                _destinationFocus.hasFocus);

        if (!hasFocus) {
          // stop spinner if lost focus
          setState(() => _isPlaceLoading = false);
          return;
        }

        _fetchPlaceSuggestions(
          _currentLocationType == LocationType.destination &&
              _destinationFocus.hasFocus,
        );
      }
    });
  }

  void _onLocationSelected({required PlaceSuggestion location}) {
    _debounceTimer?.cancel();
    setState(() {
      // Immediately update the correct text field

      _isEditingLocation = false;
      _locations.clear();

      if (_currentLocationType == LocationType.pickup) {
        _pickupController.text = location.name;
      } else if (_currentLocationType == LocationType.destination) {
        _destinationController.text = location.name;
      }
    });

    _unfocusAll();

    _fetchCoordinatesAsynchronously(location: location);
  }

  void _unfocusAll() {
    _pickupFocus.unfocus();
    _destinationFocus.unfocus();

    FocusScope.of(context).unfocus();
  }

  /**
    * TODO: - Uncomment on implementing Muli stop concepts
    void _addStop() {
    setState(() {
      _stopControllers.add(TextEditingController());
      _stopFocusNodes.add(FocusNode());
    });
  }

  void _removeStop(int index) {
    setState(() {
      _stopControllers[index].dispose();
      _stopFocusNodes[index].dispose();
      _stopControllers.removeAt(index);
      _stopFocusNodes.removeAt(index);
    });
  }
    */
}
