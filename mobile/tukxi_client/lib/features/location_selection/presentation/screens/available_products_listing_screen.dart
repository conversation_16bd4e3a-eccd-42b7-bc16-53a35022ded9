import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/services/route_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/nav_back_button.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/presentation/providers/available_products_provider.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/available_product_draggable_view.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/bottom_booking_options.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/location_marker.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/map_route_section.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/routes/app_routes.dart';

class AvailableProductsListingScreen extends ConsumerStatefulWidget {
  const AvailableProductsListingScreen({
    super.key,
    required this.pickupLocation,
    required this.destinationLocation,
    required this.rideTimeOption,
  });

  final LocationParams? pickupLocation;
  final LocationParams? destinationLocation;
  final RideTimeOption rideTimeOption;

  @override
  ConsumerState<AvailableProductsListingScreen> createState() {
    return _AvailableProductsListingScreenState();
  }
}

class _AvailableProductsListingScreenState
    extends ConsumerState<AvailableProductsListingScreen> {
  // Map + Route
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Marker> _vehicleMarkers = {};
  List<LatLng> _coordinates = [];
  Polyline? _routePolyline;
  // Polyline? _routeForegroundPolyline;
  Timer? _routeAnimationTimer;

  // Label offsets
  Offset? _pickupOffset;
  Offset? _destinationOffset;
  double _sheetSize = 0.5;

  // Ride options
  List<AvailableOptionData> _products = [];
  AvailableOptionData? _selecetdProduct;
  String? _title;

  // Debounce & projection cache
  Timer? _debounceTimer;
  String? _lastProjectionKey;
  Offset? _lastProjectionOffset;

  // Map padding
  double _mapAreaHeight = 0;

  DateTime? _lastUpdate;

  final _routeService = RouteService();
  final Set<Polyline> _dottedPolylines = {};
  final GlobalKey _bottomOptionsKey = GlobalKey();

  bool _isDragging = false;
  // Route

  // config knobs
  static const _posDebounceMs = 80;
  static const _labelYOffset = 30.0;

  bool _isRouteAvailable = false;
  bool _isRouteLoading = true;
  bool _hasAttemptedRoute = false;

  ///MARK: - Init Method
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      setState(() {
        _isRouteLoading = true;
        _hasAttemptedRoute = false;
      });
      await _drawRoute();

      // ✅ Only fetch products if route is available
      if (_isRouteAvailable) {
        unawaited(_fecthAllAvailableOptions());
      }
      // _updateMapPadding();
      // // (Optional) first nearby vehicles fetch after route settles
      // _refreshNearbyVehicles();
    });
  }

  @override
  void dispose() {
    _mapController?.dispose();
    _routeAnimationTimer?.cancel();
    _debounceTimer?.cancel();
    super.dispose();
  }

  ///MARK: - Build Method
  @override
  Widget build(BuildContext context) {
    final state = ref.read(availableProductsProvider);
    final topInset = MediaQuery.of(context).padding.bottom;

    return Scaffold(
      body: SafeArea(
        top: false,
        bottom: Platform.isAndroid,
        child: Column(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  final maxWidth = constraints.maxWidth;
                  _mapAreaHeight = constraints.maxHeight;
                  return Stack(
                    children: [
                      Positioned.fill(
                        child: ClipRect(
                          child: MapRouteSection(
                            topInset: topInset,
                            markers: {..._markers, ..._vehicleMarkers},
                            pickupLocation: widget.pickupLocation?.latLng,
                            destinationLocation:
                                widget.destinationLocation?.latLng,
                            polylines: {
                              if (_routePolyline != null)
                                _routePolyline!, // Black route
                              // if (_routeForegroundPolyline != null)
                              //   _routeForegroundPolyline!, // Animated grey route
                              ..._dottedPolylines,
                            },
                            onMapCreated: (controller) async {
                              _mapController = controller;
                              _updateMapPadding();
                            },
                            onCameraMove: _onCameraMove,
                            onCameraIdle: _onCameraIdle,
                          ),
                        ),
                      ),

                      //picku plabel
                      if (_pickupOffset != null)
                        Positioned(
                          left: _pickupOffset!.dx,
                          top: _pickupOffset!.dy - _labelYOffset,
                          child: LocationMarker(
                            text:
                                widget.pickupLocation?.locationName ?? "Pickup",
                            maxWidth: maxWidth,
                          ),
                        ),

                      /// Destination label
                      if (_destinationOffset != null)
                        Positioned(
                          left: _destinationOffset!.dx,
                          top: _destinationOffset!.dy - _labelYOffset,
                          child: LocationMarker(
                            text:
                                widget.destinationLocation?.locationName ??
                                "Destination",
                            maxWidth: maxWidth,
                          ),
                        ),

                      Positioned(
                        left: 5,
                        child: SafeArea(
                          child: NavBackButton(onPressed: () => context.pop()),
                        ),
                      ),

                      DraggableScrollableSheet(
                        initialChildSize: UIConstants.sheetInitialSize,
                        maxChildSize: UIConstants.sheetMaxSize,
                        minChildSize: UIConstants.sheetMinSize,
                        builder: (context, scrollController) {
                          return AvailableProductDraggableView(
                            scrollController: scrollController,
                            state: state,
                            rides: _products,
                            selectedOption: _selecetdProduct,
                            pickupLocation: widget.pickupLocation,
                            destinationLocation: widget.destinationLocation,
                            isRouteAvailable: _isRouteAvailable,
                            isRouteLoading: _isRouteLoading,
                            hasAttemptedRoute: _hasAttemptedRoute,
                            onAvailableOptionSelected: (selectedProduct) {
                              setState(() {
                                _selecetdProduct = selectedProduct;
                                _title = selectedProduct.name;
                              });
                            },
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
            ),

            BottomBookingOptions(
              key: _bottomOptionsKey,
              title: _title,
              onBookButtonTapped: _onBookingButtonTapped,
            ),
          ],
        ),
      ),
    );
  }

  //MARK: - Map Helper Methods

  void _onCameraMove(CameraPosition pos) {
    if (!_isDragging) setState(() => _isDragging = true);
    _schedulePositionsUpdate();
  }

  void _onCameraIdle() {
    _debounceTimer?.cancel();
    _updatePositions();
    setState(() => _isDragging = false);

    // // (Future) Also refresh vehicles on idle (throttled inside)
    // _refreshNearbyVehicles();
  }

  void _schedulePositionsUpdate() {
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }
    _debounceTimer = Timer(
      const Duration(milliseconds: _posDebounceMs),
      _updatePositions,
    );
  }

  ///MARK: - Route Helper Methods

  void _setMarkers({
    required LocationParams pickupLocation,
    required LocationParams destinationLocation,
  }) async {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('pickup'),
          position: pickupLocation.latLng!,
          infoWindow: InfoWindow(snippet: pickupLocation.locationName),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
        Marker(
          markerId: const MarkerId('destination'),
          position: destinationLocation.latLng!,
          infoWindow: InfoWindow(title: destinationLocation.locationName),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      };
    });
  }

  /// Creates a dotted polyline between two points.
  /// Works on both Android & iOS (no reliance on patterns).
  Set<Polyline> createDottedPolyline({
    required LatLng start,
    required LatLng end,
    String idPrefix = 'dot',
    Color color = const Color(0xFF6A1B9A),
    double width = 5, // Reduced width for rounder dots
    int segments = 60, // Increased segments for smoother dots
  }) {
    final List<LatLng> points = [];

    // Divide the line into segments
    for (int i = 0; i <= segments; i++) {
      final lat =
          start.latitude + (end.latitude - start.latitude) * (i / segments);
      final lng =
          start.longitude + (end.longitude - start.longitude) * (i / segments);
      points.add(LatLng(lat, lng));
    }

    final Set<Polyline> polylines = {};
    // Draw shorter segments with larger gaps for rounder dots
    for (int i = 0; i < points.length - 1; i += 4) {
      // Increased gap
      if (i + 1 >= points.length) break;
      polylines.add(
        Polyline(
          polylineId: PolylineId('$idPrefix-$i'),
          points: [points[i], points[i + 1]],
          color: AppColors.black50, // Added opacity
          width: width.toInt(),
        ),
      );
    }

    return polylines;
  }

  // void _animateRouteForeground() {
  //   if (_coordinates.isEmpty) return;

  //   _routeAnimationTimer?.cancel();

  //   List<LatLng> animatedPoints = [];
  //   int index = 0;

  //   // Batch size scales with path length (keeps setState lighter)
  //   // Determine step based on total points for smoother animation
  //   final step = (_coordinates.length / 60).ceil().clamp(1, 15);

  //   const duration = Duration(milliseconds: 33);

  //   _routeAnimationTimer = Timer.periodic(duration, (timer) {
  //     if (!mounted) {
  //       timer.cancel();
  //       return;
  //     }

  //     // Add next batch of points
  //     final nextIndex = (index + step).clamp(0, _coordinates.length);
  //     animatedPoints.addAll(_coordinates.sublist(index, nextIndex));
  //     index = nextIndex;

  //     // Update foreground polyline without triggering full rebuild
  //     _routeForegroundPolyline = _routeForegroundPolyline!.copyWith(
  //       pointsParam: List.unmodifiable(animatedPoints),
  //     );
  //     // Use setState just once per batch
  //     setState(() {});

  //     // Stop timer once all points are added
  //     if (index >= _coordinates.length) {
  //       timer.cancel();

  //       // Small delay before repeating
  //       Future.delayed(const Duration(milliseconds: 500), () {
  //         if (!mounted) return;

  //         _routeForegroundPolyline = _routeForegroundPolyline!.copyWith(
  //           pointsParam: const <LatLng>[],
  //         );

  //         _animateRouteForeground();
  //       });
  //     }
  //   });
  // }

  Future _updatePositions() async {
    if (!mounted || _mapController == null) return;

    final pickupLatLng = widget.pickupLocation?.latLng;
    final destinationLatLng = widget.destinationLocation?.latLng;

    if (pickupLatLng == null || destinationLatLng == null) {
      return;
    }

    final pickupOffset = await _project(pickupLatLng);
    final destinationOffset = await _project(destinationLatLng);

    if (!mounted) return;
    setState(() {
      _pickupOffset = pickupOffset;
      _destinationOffset = destinationOffset;
    });
  }

  Future<Offset?> _project(LatLng target) async {
    if (_mapController == null) return null;
    if (!mounted) return null;

    final key = '${target.latitude}-${target.longitude}-$_sheetSize';
    if (_lastProjectionKey == key) return _lastProjectionOffset;
    _lastProjectionKey = key;

    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final screenCoordinate = await _mapController!.getScreenCoordinate(target);

    _lastProjectionOffset = Platform.isAndroid
        ? Offset(
            screenCoordinate.x / devicePixelRatio,
            screenCoordinate.y / devicePixelRatio,
          )
        : Offset(
            screenCoordinate.x.toDouble(),
            (screenCoordinate.y + 20).toDouble(),
          );
    return _lastProjectionOffset;
  }

  void _updateMapPadding() {
    if (!mounted) return;
    if (_mapAreaHeight <= 0) return;

    final sheetHeight = _mapAreaHeight * _sheetSize;
    final newPadding = sheetHeight;

    // ✅ Throttle (only update if >100ms since last update OR big change)
    final now = DateTime.now();
    if (_lastUpdate == null ||
        now.difference(_lastUpdate!) > const Duration(milliseconds: 100) ||
        (ref.read(mapPaddingProvider) - newPadding).abs() > 5) {
      ref.read(mapPaddingProvider.notifier).state = newPadding;

      _lastUpdate = now;
    }
  }

  ///MARK: - API Call
  Future<void> _drawRoute() async {
    if (!mounted ||
        widget.pickupLocation == null ||
        widget.destinationLocation == null) {
      setState(() {
        _isRouteLoading = false;
        _isRouteAvailable = false;
        _hasAttemptedRoute = true;
      });
      return;
    }
    final pickupLatLng = widget.pickupLocation?.latLng;
    final destinationLatLng = widget.destinationLocation?.latLng;

    if (pickupLatLng == null || destinationLatLng == null) {
      setState(() {
        _isRouteLoading = false;
        _isRouteAvailable = false;
        _hasAttemptedRoute = true;
      });
      return;
    }
    try {
      //Set initial markers immediately
      _setMarkers(
        pickupLocation: widget.pickupLocation!,
        destinationLocation: widget.destinationLocation!,
      );

      final snappedPickup = await _routeService.snapToRoad(pickupLatLng);

      final snappedDestination = await _routeService.snapToRoad(
        destinationLatLng,
      );
      final routeInfo = await _routeService.getRoute(
        origin: snappedPickup,
        destination: snappedDestination,
      );

      if (routeInfo == null || !mounted) {
        debugPrint('❌ Route calculation returned null');
        setState(() {
          _isRouteLoading = false;
          _isRouteAvailable = false;
          _hasAttemptedRoute = true;
        });
        return;
      }

      _coordinates = routeInfo.polylineCoordinates;

      if (_coordinates.isEmpty) {
        debugPrint('❌ Route calculation returned empty coordinates');
        setState(() {
          _isRouteLoading = false;
          _isRouteAvailable = false;
          _hasAttemptedRoute = true;
        });
        return;
      }

      if (!_coordinates.contains(snappedPickup)) {
        _coordinates.insert(0, snappedPickup);
      }
      if (!_coordinates.contains(snappedDestination)) {
        _coordinates.add(snappedDestination);
      }
      final bounds = routeInfo.bounds;

      final topSafeInset = MediaQuery.of(context).padding.top;

      // Animate camera to fit bounds first
      await _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          bounds,
          topSafeInset == 0 ? 80 : topSafeInset,
        ),
      );

      // Draw base route (static)
      setState(() {
        _dottedPolylines.clear();

        if (pickupLatLng != snappedPickup) {
          _dottedPolylines.addAll(
            createDottedPolyline(
              start: pickupLatLng,
              end: snappedPickup,
              idPrefix: 'pickup-offroad',
              color: Colors.purple,
            ),
          );
        }

        _routePolyline = Polyline(
          polylineId: const PolylineId('route'),
          points: _coordinates,
          color: const Color.fromARGB(255, 6, 49, 240),
          width: 5,
          startCap: Cap.roundCap,
          endCap: Cap.roundCap,
        );

        if (destinationLatLng != snappedDestination) {
          _dottedPolylines.addAll(
            createDottedPolyline(
              start: snappedDestination,
              end: destinationLatLng,
              idPrefix: 'destination-offroad',
              color: Colors.purple,
            ),
          );
        }
        // ✅ Route successfully calculated and drawn
        _isRouteLoading = false;
        _isRouteAvailable = true;
        _hasAttemptedRoute = true;
      });
    } catch (e) {
      debugPrint('❌ Error drawing route: $e');
      setState(() {
        _isRouteLoading = false;
        _isRouteAvailable = false;
        _hasAttemptedRoute = true;
      });
    }
    // _routeForegroundPolyline = const Polyline(
    //   polylineId: PolylineId('route_foreground'),
    //   points: <LatLng>[],
    //   color: Color.fromARGB(255, 108, 133, 245),
    //   width: 3,
    //   startCap: Cap.roundCap,
    //   endCap: Cap.roundCap,
    // );

    // Animate route foreground (batch updates every 33ms)
    // _animateRouteForeground();
  }

  Future _fecthAllAvailableOptions() async {
    if (widget.pickupLocation == null ||
        widget.pickupLocation?.latLng == null) {
      return;
    }
    if (widget.destinationLocation == null ||
        widget.destinationLocation?.latLng == null) {
      return;
    }

    if (!_isRouteAvailable) {
      debugPrint('❌ Skipping product fetch - no route available');
      setState(() {
        _products.clear();
        _selecetdProduct = null;
        _title = null;
      });
      return;
    }

    final result = await ref
        .read(availableProductsProvider.notifier)
        .fetchAvailableRideOptions(
          pickupLocation: widget.pickupLocation!.latLng!,
          destinationLocation: widget.destinationLocation!.latLng!,
          rideTimeOption: widget.rideTimeOption,
          pickupTime: DateTime.now(),
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _fecthAllAvailableOptions(),
        );
      },
      (response) {
        setState(() {
          _products = response.availableOptions ?? [];
          _selecetdProduct = _products.isNotEmpty ? _products[0] : null;
          _title = _selecetdProduct?.name;
        });
      },
    );
  }

  void _onBookingButtonTapped() {
    if (!_isRouteAvailable || _selecetdProduct == null) {
      SnackbarUtils.showSnackBar(
        context: context,
        message: _selecetdProduct == null
            ? 'Please select an available option'
            : 'No route found between your pickup and destination. Please try a different location.',
        type: SnackBarType.error,
      );
    } else {
      if (widget.pickupLocation == null || widget.destinationLocation == null) {
        SnackbarUtils.showSnackBar(
          context: context,
          message: 'Something went wrong... try again later..',
          type: SnackBarType.error,
        );
        return;
      }
      final rideRequest = RideRequest(
        pickupLoctaion: widget.pickupLocation!,
        destinationLoctaion: widget.destinationLocation!,
        service: _selecetdProduct!,
      );
      context.push(
        AppRoutes.confirmPickup,
        extra: {'rideRequest': rideRequest},
      );
    }
  }
}
