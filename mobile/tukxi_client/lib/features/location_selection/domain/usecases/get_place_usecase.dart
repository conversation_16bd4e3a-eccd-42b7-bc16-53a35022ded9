import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/domain/repositories/google_place_repository.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class GetPlaceUsecase {
  GetPlaceUsecase({required this.repository});

  final GooglePlacesRepository repository;

  Future<Either<Failure, List<PlaceSuggestion>>> executeGetPlaceSuggestion({
    required String query,
    required LatLng? location,
  }) {
    return repository.getSuggestions(query: query, location: location);
  }

  Future<Either<Failure, LatLng?>> executeGetPlaceCoordinates(String placeId) {
    return repository.getPlaceCoordinates(placeId: placeId);
  }

  Future<Either<Failure, LocationParams?>>
  executeGetPlaceDetailsFromCoordinates(LatLng? coordinates) {
    return repository.getGooglePlaceDetails(coordinates);
  }

  Future<Either<Failure, LocationParams>> executeGetAccurateLocation(
    LatLng? coordinates,
  ) {
    return repository.getAccuratePlace(coordinates);
  }
}
