import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/location_selection/data/data_sources/booking_remote_data_souce.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';

class BookingRemoteDataSourceImpl extends BookingRemoteDataSouce {
  final _apiService = ApiService();

  //MARK: - Fetch Available products
  @override
  Future<AvailableOption> fetchAvailableRideOptions({
    required LatLng pickupLocation,
    required LatLng destinationLocation,
    required RideTimeOption rideTimeOption,
    required DateTime pickupTime,
  }) async {
    try {
      final query = {
        'pickup': {
          'lat': pickupLocation.latitude,
          'lng': pickupLocation.longitude,
        },
        'destination': {
          'lat': destinationLocation.latitude,
          'lng': destinationLocation.longitude,
        },
        'type': rideTimeOption.name,
        'pickupTime': pickupTime.toIsoUtcString(),
      };
      final response = await _apiService.post(
        Endpoint.fetchAvailableProducts.value,
        (json) => AvailableOption.fromJson(json),
        body: query,
      );
      return response;
    } catch (error) {
      rethrow;
    }
  }
}
