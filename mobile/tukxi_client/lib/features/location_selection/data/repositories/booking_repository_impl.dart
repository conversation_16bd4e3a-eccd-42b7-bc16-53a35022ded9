import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/location_selection/data/data_sources/booking_remote_data_souce.dart';
import 'package:tukxi/features/location_selection/data/models/available_option.dart';
import 'package:tukxi/features/location_selection/domain/repositories/booking_repository.dart';

class BookingRepositoryImpl extends BookingRepository {
  BookingRepositoryImpl({required this.bookingRemoteDataSouce});

  final BookingRemoteDataSouce bookingRemoteDataSouce;

  @override
  Future<Either<Failure, AvailableOption>> fetchAvailableRideOptions({
    required LatLng pickupLocation,
    required LatLng destinationLocation,
    required RideTimeOption rideTimeOption,
    required DateTime pickupTime,
  }) {
    return handleApiCall(
      () => bookingRemoteDataSouce.fetchAvailableRideOptions(
        pickupLocation: pickupLocation,
        destinationLocation: destinationLocation,
        rideTimeOption: rideTimeOption,
        pickupTime: pickupTime,
      ),
      apiErrorMessage: 'Failed to fetch available products.',
    );
  }
}
