import 'package:dartz/dartz.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/services/google_place_service.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/domain/repositories/google_place_repository.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class GooglePlacesRepositoryImpl implements GooglePlacesRepository {
  final GooglePlacesService service;

  GooglePlacesRepositoryImpl({required this.service});

  @override
  Future<Either<Failure, List<PlaceSuggestion>>> getSuggestions({
    required String query,
    required LatLng? location,
  }) async {
    try {
      final results = await service.getAutocompleteSuggestions(
        input: query,
        location: location,
      );

      return Right(results);
    } catch (e) {
      final apiFailure = e as ApiFailure?;
      return Left(
        ApiFailure(
          message: e.toString(),
          type: apiFailure?.type == ErrorType.noInternet
              ? ErrorType.noInternet
              : ErrorType.locationError,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, LatLng?>> getPlaceCoordinates({
    required String placeId,
  }) async {
    try {
      final coords = await service.getPlaceDetailsFromPlaceID(placeId);
      if (coords != null) {
        return Right(coords);
      } else {
        return Left(
          ApiFailure(
            message: 'Failed to fetch coordinates',
            type: ErrorType.locationError,
          ),
        );
      }
    } catch (e) {
      final apiFailure = e as ApiFailure?;
      return Left(
        ApiFailure(
          message: e.toString(),
          type: apiFailure?.type == ErrorType.noInternet
              ? ErrorType.noInternet
              : ErrorType.locationError,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, LocationParams>> getGooglePlaceDetails(
    LatLng? currentLocation,
  ) async {
    if (currentLocation == null) {
      return Left(
        ApiFailure(message: 'Invalid Lat/Lng', type: ErrorType.locationError),
      );
    }

    try {
      final placeDetails = await service.getAccuratePlace(currentLocation);
      return Right(placeDetails);
    } catch (error) {
      final apiFailure = error as ApiFailure?;
      return Left(
        ApiFailure(
          message: error.toString(),
          type: apiFailure?.type == ErrorType.noInternet
              ? ErrorType.noInternet
              : ErrorType.locationError,
        ),
      );
    }
  }

  @override
  Future<Either<Failure, LocationParams>> getAccuratePlace(
    LatLng? currentLocation,
  ) async {
    if (currentLocation == null) {
      return Left(
        ApiFailure(message: 'Invalid Lat/Lng', type: ErrorType.locationError),
      );
    }

    try {
      final location = await service.getAccuratePlace(currentLocation);
      return Right(location);
    } catch (error) {
      final apiFailure = error as ApiFailure?;
      return Left(
        ApiFailure(
          message: error.toString(),
          type: apiFailure?.type == ErrorType.noInternet
              ? ErrorType.noInternet
              : ErrorType.locationError,
        ),
      );
    }
  }
}
