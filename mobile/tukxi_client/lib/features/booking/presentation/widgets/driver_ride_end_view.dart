import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/driver_image_with_car.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';

class DriverRideEndView extends StatelessWidget {
  const DriverRideEndView({
    super.key,
    required this.rideRequest,
    required this.rideDetails,
    required this.driverDetails,
    required this.onClose,
    // required this.scrollController,
  });

  final RideRequest? rideRequest;
  final Ride? rideDetails;
  final Driver? driverDetails;
  final VoidCallback onClose;
  // final ScrollController scrollController;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      bottom: Platform.isAndroid,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.only(top: 20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(LocationConstants.mapCornerRadius),
                topRight: Radius.circular(LocationConstants.mapCornerRadius),
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.black10,
                  blurRadius: 1,
                  offset: Offset(0, -1),
                ),
              ],
            ),
            child: SingleChildScrollView(
              // controller: scrollController,
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  // Prevent bottom sheet from exceeding screen height
                  maxHeight: constraints.maxHeight,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      "You've arrived at your destination",
                      style: GoogleFonts.inter(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    const SizedBox(height: 15),
                    Container(
                      padding: const EdgeInsets.only(left: 5, right: 5),
                      width: double.infinity,
                      decoration: BoxDecoration(color: AppColors.greyBg),
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              "Please check that you have all your belongings.",
                              style: GoogleFonts.inter(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: AppColors.primary,
                              ),
                              textAlign: TextAlign.left,
                            ),
                          ),

                          IconButton(
                            onPressed: onClose,
                            icon: Image.asset(
                              AssetPaths.locationClose,
                              height: 14,
                            ),
                          ),
                        ],
                      ),
                    ),

                    _buildRideSummary(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRideSummary() {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 20),
          DriverImageWithCar(imageUrl: driverDetails?.profilePic ?? ''),

          Text(
            driverName(),
            style: GoogleFonts.inter(fontWeight: FontWeight.w500, fontSize: 14),
          ),

          Text(
            '${rideDetails?.vehicle?.vehicleType?.name ?? ''} • ${rideDetails?.vehicle?.vehicleNumber ?? ''}',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w500,
              fontSize: 10,
              color: AppColors.black50,
            ),
          ),

          _buildRideDetails(),
          _buildPaymentView(),

          SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildRideDetails() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,

        children: [
          Text(
            'Ride Details',
            textAlign: TextAlign.start,
            style: GoogleFonts.inter(fontWeight: FontWeight.w600, fontSize: 16),
          ),

          SizedBox(height: 20),

          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.black10),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHorizontalTexts(
                  'Date',
                  rideDetails?.createdAt?.toddMMyyyyString ?? ' ',
                ),
                _buildHorizontalTexts(
                  'Pickup Location',
                  rideDetails?.pickupLocation?.address ?? '',
                ),
                _buildHorizontalTexts(
                  'Destination',
                  rideDetails?.destinationLocation?.address ?? '',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHorizontalTexts(String title, String value) {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w500,
              fontSize: 10,
              color: AppColors.black75,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.end,
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w500,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentView() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(15),
      decoration: BoxDecoration(
        color: AppColors.greyBg,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Pay',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: AppColors.black50,
            ),
          ),
          //TO DO: Pricing
          Text(
            '₹ 0.00',
            style: GoogleFonts.inter(fontWeight: FontWeight.w600, fontSize: 24),
          ),

          //const SizedBox(height: 25),

          // LoadingButton(isLoading: false, onPressed: () {}, text: 'Pay Now'),
        ],
      ),
    );
  }

  String driverName() {
    if (driverDetails != null) {
      return '${driverDetails?.firstName ?? ''} ${driverDetails?.lastName ?? ''}';
    } else if (rideDetails != null) {
      return '${rideDetails?.driver?.firstName ?? ''} ${rideDetails?.driver?.lastName ?? ''}';
    } else {
      return 'Unknown';
    }
  }
}
