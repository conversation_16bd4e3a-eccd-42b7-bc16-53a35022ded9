import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class EtaText extends StatelessWidget {
  const EtaText({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const ShapeDecoration(
        color: Colors.white,
        shape: StadiumBorder(side: BorderSide.none),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
      child: Row(
        children: [
          Text(
            'Driver will pick you in',
            style: GoogleFonts.inter(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              height: 22 / 14,
            ),
          ),

          Expanded(
            child: Text(
              '04:39 Mins',
              textAlign: TextAlign.end,
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                fontSize: 14,
                height: 22 / 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
