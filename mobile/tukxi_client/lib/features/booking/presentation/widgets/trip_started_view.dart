import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/circular_image_widget.dart';
import 'package:tukxi/core/widgets/drag_handle.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/presentation/widgets/ride_stops_card.dart';

class TripStartedView extends StatelessWidget {
  const TripStartedView({
    super.key,
    required this.scrollController,
    required this.rideRequest,
    required this.rideDetails,
    required this.driverDetails,
  });

  final RideRequest? rideRequest;
  final Ride? rideDetails;
  final Driver? driverDetails;
  final ScrollController scrollController;

  @override
  Widget build(BuildContext context) {
    final selectedIndex = 0;
    final List<String?> stops = [
      // ('7:53 AM', 'Stop 1'),
      // ('8:00 AM', 'Stop 2'),
      // ('8:05 AM', 'Stop 3'),
    ];

    return Container(
      width: double.infinity,
      // padding: const EdgeInsets.only(left: 15, right: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 1,
            offset: Offset(0, -1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DragHandle(),
          Expanded(
            child: SingleChildScrollView(
              controller: scrollController,
              physics: Platform.isAndroid
                  ? const ClampingScrollPhysics()
                  : const BouncingScrollPhysics(),
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Column(
                children: [
                  const SizedBox(height: 30),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Avatar
                      CircularImageWidget(
                        size: 48,
                        borderRadius: 24,
                        imageUrl: driverDetails?.profilePic,
                      ),

                      const SizedBox(width: 15),

                      Text(
                        '${driverDetails?.firstName ?? ''} ${driverDetails?.lastName ?? ''}',
                        style: GoogleFonts.inter(
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 20),

                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.black10),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    width: double.infinity,
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Heading to',
                                style: GoogleFonts.inter(
                                  fontSize: 14,
                                  color: AppColors.black50,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                rideDetails?.destinationLocation?.address ?? '',
                                style: GoogleFonts.inter(
                                  color: Colors.black,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                        // // More button
                        // SizedBox(
                        //   width: 48,
                        //   height: 48,
                        //   child: FilledButton(
                        //     onPressed: onMoreOptionsTapped,
                        //     style: FilledButton.styleFrom(
                        //       padding: EdgeInsets.symmetric(horizontal: 2),
                        //       backgroundColor: AppColors.greyBg,
                        //       foregroundColor: Colors.black,
                        //     ),
                        //     child: const Icon(Icons.more_horiz, size: 25),
                        //   ),
                        // ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 20),

                  ListView.builder(
                    padding: EdgeInsets.zero,
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: stops.length,
                    itemBuilder: (context, index) {
                      return RideStopsCard(
                        index: index,
                        selectedIndex: selectedIndex,
                        stop: stops[index],
                        isNotLastIndex: index != stops.length - 1,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
