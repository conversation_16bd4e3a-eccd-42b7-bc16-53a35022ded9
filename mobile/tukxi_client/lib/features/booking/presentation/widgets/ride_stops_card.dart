import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/painter/flexible_dotted_line_painter.dart';
import 'package:tukxi/core/theme/app_colors.dart';

class RideStopsCard extends StatelessWidget {
  const RideStopsCard({
    super.key,
    required this.selectedIndex,
    required this.index,
    required this.stop,
    required this.isNotLastIndex,
    this.isPickup = false,
    this.isDestination = false,
    this.isFromTripDetails = false,
  });

  final bool isFromTripDetails;
  final int selectedIndex;
  final int index;
  final bool isNotLastIndex;
  final String? stop;
  final bool isPickup;
  final bool isDestination;

  @override
  Widget build(BuildContext context) {
    const double width = 3.0;
    final asset = isPickup
        ? AssetPaths.pickupStop
        : isDestination
        ? AssetPaths.destStop
        : AssetPaths.dot;
    return IntrinsicHeight(
      child: Row(
        children: [
          Column(
            children: [
              SizedBox(
                width: 14,
                child: Image.asset(
                  asset,
                  height: (isPickup || isDestination)
                      ? 14
                      : (index == selectedIndex)
                      ? 10
                      : 8,
                  color: (isPickup || isDestination)
                      ? null
                      : (index == selectedIndex)
                      ? AppColors.primary
                      : AppColors.black50,
                ),
              ),
              if (isNotLastIndex)
                Expanded(
                  child: Container(
                    width: width,
                    margin: const EdgeInsets.symmetric(vertical: 12),
                    child: CustomPaint(
                      painter: FlexibleDottedLinePainter(
                        color: AppColors.black50,
                        dotRadius: width / 2,
                      ),
                    ),
                  ),
                )
              else
                SizedBox.shrink(),
            ],
          ),

          const SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text(
                //   timeString(),
                //   textAlign: TextAlign.start,
                //   style: GoogleFonts.inter(
                //     color: AppColors.black50,
                //     fontSize: 8,
                //     fontWeight: FontWeight.w500,
                //   ),
                // ),
                Text(
                  stop ?? '',
                  textAlign: TextAlign.start,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (isNotLastIndex) SizedBox(height: 20),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String timeString() {
    if (isPickup) {
      return 'Start location • Time';
    } else if (isDestination) {
      return 'Destination • Time';
    }
    return 'Time';
  }
}
