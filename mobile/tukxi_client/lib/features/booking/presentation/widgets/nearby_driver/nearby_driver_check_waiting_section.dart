import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/custom_linear_progress_indicator.dart';
import 'package:tukxi/core/widgets/platform_dialog.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/data/models/ride_status_event.dart';
import 'package:tukxi/features/booking/presentation/provider/ride_status_provider.dart';
import 'package:tukxi/features/booking/presentation/widgets/nearby_driver/cancel_button.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';

class NearbyDriverCheckWaitingSection extends ConsumerStatefulWidget {
  const NearbyDriverCheckWaitingSection({
    super.key,
    required this.rideDetails,
    required this.rideRequest,
    required this.onRideCancelled,
  });

  final Ride? rideDetails;
  final RideRequest? rideRequest;
  final VoidCallback onRideCancelled;
  @override
  ConsumerState<NearbyDriverCheckWaitingSection> createState() {
    return _NearbyDriverCheckWaitingSectionState();
  }
}

class _NearbyDriverCheckWaitingSectionState
    extends ConsumerState<NearbyDriverCheckWaitingSection> {
  LocationResponse get pickupFromRequest {
    return LocationResponse(
      address: widget.rideRequest?.pickupLoctaion.locationName ?? '',
      latitude: widget.rideRequest?.pickupLoctaion.latLng?.latitude ?? 0,
      longitude: widget.rideRequest?.pickupLoctaion.latLng?.longitude,
    );
  }

  LocationResponse get pickupLocation {
    return (widget.rideRequest == null &&
            widget.rideDetails != null &&
            widget.rideDetails!.pickupLocation != null)
        ? widget.rideDetails!.pickupLocation!
        : pickupFromRequest;
  }

  @override
  Widget build(BuildContext context) {
    final rideStatus = ref.watch(rideStatusProvider);
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.black10,
            blurRadius: 1,
            offset: Offset(0, -1),
          ),
        ],
      ),

      child: Column(
        children: [
          Text(
            'Finding nearby drivers',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              height: 28 / 20,
            ),
          ),

          _buildLoader(rideStatus),
          const SizedBox(height: 10),

          _buildPickupCard(),

          const SizedBox(height: 25),

          CancelRideButton(cancelTapped: () => _onCancelTapped()),
        ],
      ),
    );
  }

  Widget _buildLoader(RideStatusEvent? rideStatus) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 15),
          child: CustomLinearProgressIndicator(
            borderRadius: BorderRadius.circular(4),
            backgroundColor: AppColors.greyBg,
            color: AppColors.primary,
            minHeight: 8,
          ),
        ),
        const SizedBox(height: 5),

        if (rideStatus != null &&
            rideStatus.status != null &&
            rideStatus.status!.isNotEmpty)
          Text(
            _fetchStatusText(rideStatus),
            textAlign: TextAlign.left,
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w400,
              color: AppColors.black60,
            ),
          ),
      ],
    );
  }

  Widget _buildPickupCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: AppColors.black10),
        borderRadius: BorderRadius.circular(16),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pick-up point',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              height: 22 / 14,
              color: AppColors.black50,
            ),
          ),
          Text(
            pickupLocation.address ?? '',
            style: GoogleFonts.inter(fontSize: 14, fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  void _onCancelTapped() {
    PlatformDialog.showConfirmationDialog(
      context: context,
      title: 'Cancel Ride?',
      content: 'Do you want to cancel your ride request?',
      onConfirm: () {
        widget.onRideCancelled();
      },
    );
  }

  String _fetchStatusText(RideStatusEvent rideStatus) {
    final statusString = rideStatus.status;
    if (statusString == null || statusString.isEmpty) {
      return 'Looking for a nearby driver…';
    }
    final status = RideStatusType.fromString(statusString);
    if (status == null) return '';
    switch (status) {
      case RideStatusType.requested:
        return 'Looking for a nearby driver…';
      case RideStatusType.processing:
        return 'Processing your ride request…';

      case RideStatusType.accepted:
        return 'Your driver has accepted the ride and is on the way.';
      case RideStatusType.driverEnroute:
        return 'Your driver is on the way to your pickup location.';
      case RideStatusType.driverArrived:
        return 'Your driver has arrived at your pickup location.';
      case RideStatusType.cancelledByRider:
        return 'You have cancelled the ride request.';
      case RideStatusType.cancelledByDriver:
        return 'Your ride request has been cancelled by the driver.';
      case RideStatusType.cancelled:
      case RideStatusType.failed:
        return 'Something went wrong. Please try booking again.';
      case RideStatusType.started:
        return 'Your ride has started. Enjoy your trip!';
      case RideStatusType.completed:
        return 'Your ride has been completed. Thank you for using our service!';
      case RideStatusType.unassigned:
        return 'No drivers available in your area right now. Please try again later.';
    }
  }
}
