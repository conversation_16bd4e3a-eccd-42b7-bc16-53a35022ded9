import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/services/socket_service.dart';

/// Keep the SocketService alive across screens.
final socketServiceProvider = Provider<SocketService>((ref) {
  final keepAlive = ref.keepAlive();
  final service = SocketService();

  // When no one is listening anymore, cleanup socket
  ref.onDispose(() {
    keepAlive.close();
  });

  return service;
});
