import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/booking/data/data_sources/ride_remote_data_source_impl.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';
import 'package:tukxi/features/booking/data/repositories/ride_repository_impl.dart';
import 'package:tukxi/features/booking/domain/usecases/ride_usecase.dart';
import 'package:tukxi/features/booking/presentation/state/ride_state.dart';

final rideProvider = StateNotifierProvider<RideNotifier, RideState>((ref) {
  final apiService = ApiService();
  final dataSource = RideRemoteDataSourceImpl(apiService);
  final repository = RideRepositoryImpl(dataSource: dataSource);
  final useCase = RideUseCase(repository: repository);
  return RideNotifier(useCase);
});

class RideNotifier extends StateNotifier<RideState> {
  RideNotifier(this.useCase) : super(RideInitial());

  final RideUseCase useCase;

  Future<Either<Failure, RideResponse>> rideRequest(RideRequest request) async {
    state = RideLoading();
    final result = await useCase.executeRideRequest(request);
    result.fold((failure) {
      if (failure.type == ErrorType.noInternet) {
        state = RideError('no_internet');
      } else {
        state = RideError(failure.message);
      }
    }, (ride) => state = RideSuccess(rideResponse: ride, isSuccess: true));
    return result;
  }

  Future<Either<Failure, RideResponse>> cancelRide(String rideId) async {
    state = RideLoading();
    final result = await useCase.executeCancelRide(rideId);
    result.fold((failure) {
      if (failure.type == ErrorType.noInternet) {
        state = RideError('no_internet');
      } else {
        state = RideError(failure.message);
      }
    }, (ride) => state = RideSuccess(rideResponse: ride, isSuccess: true));
    return result;
  }

  Future<Either<Failure, RideListResponse>> fetchAllRides() async {
    state = RideLoading();
    final result = await useCase.executeFetchAllRides();
    result.fold((failure) {
      if (failure.type == ErrorType.noInternet) {
        state = RideError('no_internet');
      } else {
        state = RideError(failure.message);
      }
    }, (rides) => state = RideSuccess(rides: rides, isSuccess: true));
    return result;
  }

  Future<Either<Failure, RideResponse>> fetchRideDetails(String rideId) async {
    state = RideLoading();
    final result = await useCase.executeFetchRideDetails(rideId);
    result.fold((failure) {
      if (failure.type == ErrorType.noInternet) {
        state = RideError('no_internet');
      } else {
        state = RideError(failure.message);
      }
    }, (ride) => state = RideSuccess(rideResponse: ride, isSuccess: true));
    return result;
  }

  Future<Either<Failure, ReviewResponse>> reviewDriver(
    ReviewRequest request,
  ) async {
    state = RideLoading();
    final result = await useCase.executeReviewDriver(request);
    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = RideError('no_internet');
        } else {
          state = RideError(failure.message);
        }
      },
      (response) =>
          state = RideSuccess(reviewResponse: response, isSuccess: true),
    );
    return result;
  }
}
