import 'dart:async';
import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/services/rating_dialog_service.dart';
import 'package:tukxi/core/services/route_service.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/drag_handle.dart';
import 'package:tukxi/core/widgets/nav_back_button.dart';
import 'package:tukxi/core/widgets/platform_dialog.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/booking/presentation/provider/driver_loction_provider.dart';
import 'package:tukxi/features/booking/presentation/provider/ride_provider.dart';
import 'package:tukxi/features/booking/presentation/provider/ride_status_provider.dart';
import 'package:tukxi/features/booking/presentation/provider/socket_service_provider.dart';
import 'package:tukxi/features/booking/presentation/state/ride_state.dart';
import 'package:tukxi/features/booking/presentation/widgets/trip_started_view.dart';
import 'package:tukxi/features/booking/presentation/widgets/driver_accepted_view.dart';
import 'package:tukxi/features/booking/presentation/widgets/driver_ride_end_view.dart';
import 'package:tukxi/features/booking/presentation/widgets/driver_timeout_view.dart';
import 'package:tukxi/features/booking/presentation/widgets/nearby_driver/cancel_button.dart';
import 'package:tukxi/features/booking/presentation/widgets/nearby_driver/nearby_driver_check_waiting_section.dart';
import 'package:tukxi/features/home/<USER>/providers/location_provider.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/features/location_selection/presentation/widgets/available_products/map_route_section.dart';
import 'package:tukxi/main.dart';
import 'package:tukxi/routes/app_routes.dart';

class RideScreen extends ConsumerStatefulWidget {
  const RideScreen({super.key, required this.rideRequest, required this.ride});

  final RideRequest? rideRequest;
  final Ride? ride;
  @override
  ConsumerState<RideScreen> createState() {
    return _RideScreenState();
  }
}

class _RideScreenState extends ConsumerState<RideScreen> {
  final GlobalKey _cancelViewKey = GlobalKey();
  final GlobalKey _etaTextKey = GlobalKey();

  final _prefService = SharedPreferenceService();
  final _routeService = RouteService();

  double _mapAreaHeight = 0;
  double _cancelViewHeight = 0;
  // double _etaTextHeight = 0;
  double _driverViewHeight = 0;

  bool _waitingForDriver = true;
  bool _isDriverAssigned = false;
  bool _isRideStarted = false;
  bool _isRideEnded = false;
  bool _isLoading = false;
  bool _isTimeOut = false;
  bool _isRideCancelled = false;

  DateTime? _lastUpdate;
  Driver? _driverdetails;
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Marker? _driverMarker;
  Ride? _rideDetails;
  List<LatLng> _coordinates = [];
  Polyline? _routePolyline;
  LatLng? _previousDriverLocation;
  double _driverSpeed = 0;

  final Set<Polyline> _dottedPolylines = {};
  final _updateThrottleDuration = const Duration(milliseconds: 100);
  final _significantPaddingChange = 5; // pixels

  // Different thresholds based on movement context
  double getUpdateThreshold() {
    if (_driverSpeed < 30) return 0.02; // 20m for city driving

    if (_driverSpeed > 60) return 0.05; // 50m for highway driving

    return 0.01; // 10m for very slow movement
  }

  LatLng? get pickupLatLng {
    return (widget.rideRequest == null && _rideDetails != null)
        ? LatLng(
            _rideDetails?.pickupLocation?.latitude ?? 0,
            _rideDetails?.pickupLocation?.longitude ?? 0,
          )
        : widget.rideRequest?.pickupLoctaion.latLng ?? LatLng(0, 0);
  }

  LatLng? get destinationLatLng {
    return (widget.rideRequest == null && _rideDetails != null)
        ? LatLng(
            _rideDetails?.destinationLocation?.latitude ?? 0,
            _rideDetails?.destinationLocation?.longitude ?? 0,
          )
        : widget.rideRequest?.destinationLoctaion.latLng ?? LatLng(0, 0);
  }

  // bool _shouldShowETA(RideState state) {
  //   return _isDriverAssigned &&
  //       !_isRideStarted &&
  //       !_isRideEnded &&
  //       state is! RideLoading;
  // }

  bool _rideAccepted(RideState state) {
    return _isDriverAssigned &&
        !_isRideStarted &&
        !_isRideEnded &&
        state is! RideLoading;
  }

  bool _searchingForDriver(RideState state) {
    return _waitingForDriver &&
        !_isDriverAssigned &&
        !_isRideStarted &&
        !_isRideEnded &&
        state is! RideLoading;
  }

  bool get _tripStarted {
    return _isRideStarted && !_isRideEnded;
  }

  //MARK: - Simulation vars

  // Timer? _simulationTimer;
  // List<LatLng> _simulationRoute = [];
  // int _currentSimulationIndex = 0;
  // bool _isSimulationActive = false;

  //MARK: - Init State
  @override
  void initState() {
    super.initState();

    _initializeScreen();
  }

  //MARK: - Dispose
  @override
  void dispose() {
    // _simulationTimer?.cancel(); //simulation
    if (_mapController != null) {
      _mapController!.dispose();
      _mapController = null;
    }
    super.dispose();
  }

  //MARK: - Build
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      // floatingActionButton: _buildSimulationControls(), //Simulation
      body: SafeArea(
        top: false,
        bottom: Platform.isAndroid,
        child: Stack(
          children: [
            _buildMainContent(),
            if (_isLoading) _buildLoadingOverlay(),
          ],
        ),
      ),
    );
  }

  //MARK: - Widgets
  Widget _buildMainContent() {
    final topInset = MediaQuery.of(context).padding.top;
    final state = ref.watch(rideProvider);

    return Column(
      children: [
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraint) {
              _mapAreaHeight = constraint.maxHeight;

              return Stack(
                children: [
                  _buildMapSection(topInset),
                  _buildNavigationButton(topInset),

                  // if (_shouldShowETA(state)) _buildETASection(),
                  if (_rideAccepted(state)) _rideAcceptedByDriverView(),

                  if (_searchingForDriver(state)) _searchingForDriverView(),

                  if (_tripStarted) _tripStartedView(),

                  // if (_isRideEnded) _tripEndedView(),
                  if (_isTimeOut) _tripTimeOutView(),
                ],
              );
            },
          ),
        ),
        const SizedBox(height: 25),
      ],
    );
  }

  void _tripEndedView() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      enableDrag: false,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return DriverRideEndView(
          // scrollController: scrollController,
          rideRequest: widget.rideRequest,
          rideDetails: _rideDetails,
          driverDetails: _driverdetails,
          onClose: () {
            setState(() {
              _isRideEnded = false;
            });

            _displayRatingScreen();
          },
        );
      },
    );
  }

  Widget _tripTimeOutView() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: DriverTimeoutView(onRideCancelled: _cancelTheRide),
    );
  }

  Widget _tripStartedView() {
    return DraggableScrollableSheet(
      initialChildSize: 0.3,
      maxChildSize: 0.5,
      minChildSize: 0.3,

      builder: (context, scrollController) {
        return TripStartedView(
          scrollController: scrollController,
          rideRequest: widget.rideRequest,
          rideDetails: _rideDetails,
          driverDetails: _driverdetails,
        );
      },
    );
  }

  Widget _searchingForDriverView() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: NearbyDriverCheckWaitingSection(
        key: _cancelViewKey,
        rideRequest: widget.rideRequest,
        rideDetails: _rideDetails,
        onRideCancelled: _cancelTheRide,
      ),
    );
  }

  Widget _rideAcceptedByDriverView() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: DriverAcceptedView(
        rideRequest: widget.rideRequest,
        rideDetails: _rideDetails,
        driverDetails: _driverdetails,
        onSize: (height) {
          setState(() {
            _driverViewHeight = height;
          });
          _updateMapPadding();
        },
        onMoreOptionsTapped: _onMoreOptionsTapped,
      ),
    );
  }

  // Widget _buildETASection() {
  //   return Positioned(
  //     left: 5,
  //     right: 5,
  //     bottom: ref.read(mapPaddingProvider) + 15,
  //     child: EtaText(key: _etaTextKey),
  //   );
  // }

  Widget _buildNavigationButton(double topInset) {
    return Positioned(
      left: 5,
      top: topInset,
      child: NavBackButton(onPressed: () => context.pop()),
    );
  }

  Widget _buildMapSection(double topInset) {
    return Positioned.fill(
      child: MapRouteSection(
        key: ValueKey('map_${widget.ride?.id ?? 'new'}'),
        topInset: topInset,
        markers: _markers,
        pickupLocation: pickupLatLng,
        destinationLocation: destinationLatLng,
        polylines: {
          if (_routePolyline != null) _routePolyline!,
          ..._dottedPolylines,
        },
        onMapCreated: _onMapCreated,
        onCameraMove: (CameraPosition position) {},
        onCameraIdle: () {},
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return Positioned.fill(
      child: Container(
        color: Colors.black26,
        child: const Center(child: CircularProgressIndicator()),
      ),
    );
  }

  //MARK: - Bottom Sheet

  void _displayRatingScreen() {
    RatingDialogService.showRatingDialog(
      context: context,
      driverDetails: _driverdetails,
      rideId: _rideDetails?.id ?? '',
    );
  }

  //MARK: - Initialize Screen Methods
  void _initializeScreen() {
    _prefService.activeRideId = widget.ride?.id ?? '';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _measureUIComponents();
      _setupLifecycleCallback();

      _initializeRideData();
    });
  }

  void _initializeRideData() {
    if (!mounted || widget.ride == null || widget.ride?.id == null) return;

    _fetchRideDetails(widget.ride!.id!);
    _initSocket();
  }

  void _setupLifecycleCallback() {
    final lifeCycleService = ref.read(lifeCycleServiceProvider);
    lifeCycleService.onRideScreenResumed = _handleAppResumeRideRefresh;
  }

  void _measureUIComponents() {
    final navContext = _cancelViewKey.currentContext;
    final etaTextContext = _etaTextKey.currentContext;

    if (navContext != null) {
      final navButtonBox = navContext.findRenderObject() as RenderBox;
      _cancelViewHeight = navButtonBox.size.height;
    }

    if (etaTextContext != null) {
      // final etaTextBox = etaTextContext.findRenderObject() as RenderBox;
      // _etaTextHeight = etaTextBox.size.height;
    }

    if (navContext != null || etaTextContext != null) {
      setState(() {});
    }
  }

  //MARK: - Button Actions
  void _onMoreOptionsTapped() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        return SafeArea(
          bottom: Platform.isAndroid,
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 20),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                DragHandle(),
                const SizedBox(height: 30),
                Text(
                  'Manage Your Ride',
                  style: GoogleFonts.inter(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 10),
                Text(
                  'You can manage your ride here. You may cancel anytime, even after a driver has been booked for you.',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppColors.black60,
                  ),
                ),

                const SizedBox(height: 20),

                CancelRideButton(cancelTapped: _onCancelTapped),

                const SizedBox(height: 15),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onCancelTapped() {
    PlatformDialog.showConfirmationDialog(
      context: context,
      title: 'Cancel Ride?',
      content: 'Do you want to cancel your ride request?',
      onConfirm: () {
        context.pop();
        _cancelTheRide();
      },
    );
  }

  //MARK: - Ride Actions
  void _updateRideState(dynamic response) {
    final ride = response.ride;
    final status = ride?.status ?? '';

    final rideStatusType = RideStatusType.fromString(status);

    setState(() {
      _rideDetails = ride;
    });

    if (rideStatusType == null) return;
    _handleRideStatusUpdate(rideStatusType);
  }

  void _handleTerminalStatus(RideStatusType status) {
    final terminalStatuses = [
      RideStatusType.completed,
      RideStatusType.cancelled,
      RideStatusType.cancelledByRider,
      RideStatusType.failed,
    ];
    if (terminalStatuses.contains(status)) {
      debugPrint('🏁 Terminal status detected: $status');
      _prefService.clearActiveRideId();

      if ([
        RideStatusType.cancelled,
        RideStatusType.cancelledByRider,
        RideStatusType.failed,
      ].contains(status)) {
        _navigateToHome(status);
      }
    }
  }

  void _navigateToHome(RideStatusType status) {
    final message = status == RideStatusType.failed
        ? 'Ride failed'
        : 'Ride was cancelled';

    SnackbarUtils.showSnackBar(
      context: context,
      message: message,
      type: SnackBarType.error,
      duration: const Duration(seconds: 2),
    );

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        context.go(AppRoutes.tabbar);
      }
    });
  }

  void _endTheRide() {
    if (_isRideEnded) {
      ref.read(rideRefreshProvider.notifier).state = true;
      _unsubscribeFromRide();
    }
  }

  void _unsubscribeFromRide() {
    final socket = ref.read(socketServiceProvider);
    socket.offRideStatus();
    socket.offDriverLocation();
  }
  //MARK: - API Calls

  Future<void> _handleApiCall<T>(
    Future<Either<Failure, T>> Function() apiCall, {
    Function(T response)? onSuccess,
    String? errorMessage,
  }) async {
    final result = await apiCall();
    if (!mounted) return;

    result.fold(
      (failure) {
        if (!mounted) return;
        setState(() {
          _isLoading = false;
        });
        handleApiError(
          context: context,
          failure: failure,
          errorMessage: errorMessage,
          onRetry: () async => _handleApiCall(apiCall, onSuccess: onSuccess),
        );
      },
      (response) {
        if (!mounted) return;
        onSuccess?.call(response);
      },
    );
  }

  void _fetchRideDetails(String rideId) async {
    await _handleApiCall(
      () => ref.read(rideProvider.notifier).fetchRideDetails(rideId),
      onSuccess: (response) {
        if (!mounted) return;

        _updateRideState(response);
      },
    );
  }

  void _cancelTheRide() async {
    if (!mounted || _rideDetails == null || _rideDetails?.id == null) {
      return;
    }

    setState(() {
      _isLoading = true;
      _waitingForDriver = false;
    });
    await _handleApiCall(
      () => ref.read(rideProvider.notifier).cancelRide(_rideDetails!.id!),

      onSuccess: (response) {
        if (!mounted) return;
        ref.read(rideRefreshProvider.notifier).state = true;

        final navContext = navigatorKey.currentContext;
        if (navContext == null) return;

        _rideCancelledAction(message: response.message);
      },
    );
  }

  void _rideCancelledAction({String? message}) {
    SnackbarUtils.showSnackBar(
      context: context,
      message: message ?? 'Ride cancelled successfully  ',
      duration: Duration(seconds: 1),
      type: SnackBarType.success,
    );

    Future.delayed(Duration(seconds: 2), () {
      if (!mounted) return;
      _unsubscribeFromRide();
      setState(() {
        _isLoading = false;
      });
      context.go(AppRoutes.tabbar);
    });
  }

  //MARK: - Socket Methods
  void _initSocket() async {
    if (!mounted || widget.ride == null || widget.ride!.id == null) return;

    final token = _prefService.token;
    final socket = ref.read(socketServiceProvider);

    await socket.ensureConnected(token);
    socket.subscribeToRide(widget.ride?.id ?? '');

    _setupSocketListeners(socket);
  }

  void _setupSocketListeners(dynamic socket) {
    // ✅ Listen for ride status updates
    socket.listenRideStatus((rideStatus) {
      if (!mounted || rideStatus.rideId != widget.ride!.id) return;

      ref.read(rideStatusProvider.notifier).update(rideStatus);

      final driver = rideStatus.driver;

      if (driver != null) {
        setState(() {
          _driverdetails = driver;
        });
      }

      final status = RideStatusType.fromString(rideStatus.status);
      if (status == null) return;
      _handleRideStatusUpdate(status);
    });

    // ✅ Listen for driver location updates
    socket.listenDriverLocation((locationData) {
      if (!mounted) return;
      _handleDriverLocationUpdate(locationData);
    });
  }

  void _handleDriverLocationUpdate(dynamic locationData) {
    final location = locationData.locationData;
    if (location?.rideId != widget.ride!.id ||
        location?.lat == null ||
        location?.lon == null) {
      return;
    }

    final newLocation = LatLng(location!.lat!, location.lon!);
    final bearing = location.bearing ?? 0.0;
    final speed = location.speed ?? 0.0;

    _driverSpeed = speed;
    // Update provider state
    ref.read(driverLocationProvider.notifier).update(newLocation);

    if (_isDriverAssigned) {
      // Update driver marker with bearing
      _updateDriverLocationWithBearing(newLocation, bearing);
    }
    // Update ETA if needed
    // _updateETABasedOnLocation(newLocation);
    // Update driver marker
    // _updateDriverLocation(newLocation);
  }

  void _handleRideStatusUpdate(RideStatusType rideStatus) {
    setState(() {
      switch (rideStatus) {
        case RideStatusType.accepted:
        case RideStatusType.driverEnroute:
        case RideStatusType.driverArrived:
          _isDriverAssigned = true;
          _waitingForDriver = false;
          _isTimeOut = false;
          _isRideCancelled = false;
          _isRideStarted = false;
        case RideStatusType.started:
          _isRideStarted = true;
          _waitingForDriver = false;
          _isTimeOut = false;
          _isRideCancelled = false;

        case RideStatusType.completed:
          _isRideEnded = true;
          _waitingForDriver = false;
          _isTimeOut = false;
          _isRideCancelled = false;
          _tripEndedView();
          _endTheRide();
        case RideStatusType.processing:
        case RideStatusType.requested:
        case RideStatusType.cancelledByDriver:
          if (_isRideCancelled) {
            return;
          }
          _waitingForDriver = true;
          _isDriverAssigned = false;
          _isRideEnded = false;
          _isRideStarted = false;
          _isTimeOut = false;
        case RideStatusType.cancelled:
        case RideStatusType.failed:
        case RideStatusType.cancelledByRider:
          _isDriverAssigned = false;
          _waitingForDriver = false;
          _isRideEnded = false;
          _isRideStarted = false;
          _isTimeOut = false;
          _isRideCancelled = false;
          _unsubscribeFromRide();
          if (rideStatus == RideStatusType.cancelledByRider) {
            _isRideCancelled = true;
            _rideCancelledAction();
          }
        case RideStatusType.unassigned:
          _isDriverAssigned = false;
          _waitingForDriver = false;
          _isRideEnded = false;
          _isRideStarted = false;
          _isTimeOut = true;
      }
      _handleTerminalStatus(rideStatus);
      _endTheRide();
    });
  }

  //MARK: - Map Related Methods
  void _onMapCreated(GoogleMapController controller) {
    if (_mapController != null) {
      return;
    }
    _mapController = controller;

    _updateMapPadding();
    _drawRoute();
  }

  void _updateMapPadding() {
    if (!mounted || _mapAreaHeight <= 0) return;

    final newPadding = _isDriverAssigned
        ? _driverViewHeight
        : _cancelViewHeight;
    final now = DateTime.now();
    final currentPadding = ref.read(mapPaddingProvider);

    final shouldUpdate =
        _lastUpdate == null ||
        now.difference(_lastUpdate!) > _updateThrottleDuration ||
        (currentPadding - newPadding).abs() > _significantPaddingChange;

    // ✅ Throttle (only update if >100ms since last update OR big change)
    if (shouldUpdate) {
      ref.read(mapPaddingProvider.notifier).state = newPadding;

      _lastUpdate = now;
    }
  }

  Future<void> _drawRoute() async {
    if (!mounted || pickupLatLng == null || destinationLatLng == null) {
      return;
    }

    //Set initial markers immediately
    _setMarkers(pickup: pickupLatLng!, destination: destinationLatLng!);

    // Snap pickup and destination to nearest roads
    final snappedPickup = await _routeService.snapToRoad(pickupLatLng!);

    final snappedDestination = await _routeService.snapToRoad(
      destinationLatLng!,
    );
    final routeInfo = await _routeService.getRoute(
      origin: snappedPickup,
      destination: snappedDestination,
    );

    if (!mounted || routeInfo == null) return;

    _updateRouteDisplay(routeInfo, snappedPickup, snappedDestination);
  }

  void _updateRouteDisplay(
    dynamic routeInfo,
    LatLng snappedPickup,
    LatLng snappedDestination,
  ) {
    _coordinates = routeInfo.polylineCoordinates;

    if (!_coordinates.contains(snappedPickup)) {
      _coordinates.insert(0, snappedPickup);
    }
    if (!_coordinates.contains(snappedDestination)) {
      _coordinates.add(snappedDestination);
    }
    final bounds = routeInfo.bounds;

    final topSafeInset = MediaQuery.of(context).padding.top;

    // Animate camera to fit bounds first
    _mapController!.animateCamera(
      CameraUpdate.newLatLngBounds(
        bounds,
        topSafeInset == 0 ? 80 : topSafeInset,
      ),
    );

    // Draw base route (static)
    setState(() {
      _updatePolylines(snappedPickup, snappedDestination);
    });
  }

  void _updatePolylines(LatLng snappedPickup, LatLng snappedDestination) {
    if (pickupLatLng != snappedPickup) {
      _dottedPolylines.addAll(
        createDottedPolyline(
          start: pickupLatLng!,
          end: snappedPickup,
          idPrefix: 'pickup-offroad',
          color: Colors.purple,
        ),
      );
    }

    _routePolyline = Polyline(
      polylineId: const PolylineId('route'),
      points: _coordinates,
      color: AppColors.primary,
      width: 5,
      startCap: Cap.roundCap,
      endCap: Cap.roundCap,
      jointType: JointType.round,
    );

    if (destinationLatLng != snappedDestination) {
      _dottedPolylines.addAll(
        createDottedPolyline(
          start: snappedDestination,
          end: destinationLatLng!,
          idPrefix: 'destination-offroad',
          color: Colors.purple,
        ),
      );
    }
  }

  /// Creates a dotted polyline between two points.
  /// Works on both Android & iOS (no reliance on patterns).
  Set<Polyline> createDottedPolyline({
    required LatLng start,
    required LatLng end,
    String idPrefix = 'dot',
    Color color = const Color(0xFF6A1B9A),
    double width = 6,
    int segments = 40,
  }) {
    final List<LatLng> points = [];

    // Divide the line into segments
    for (int i = 0; i <= segments; i++) {
      final lat =
          start.latitude + (end.latitude - start.latitude) * (i / segments);
      final lng =
          start.longitude + (end.longitude - start.longitude) * (i / segments);
      points.add(LatLng(lat, lng));
    }

    final Set<Polyline> polylines = {};
    // Draw shorter segments with larger gaps for rounder dots
    for (int i = 0; i < points.length - 1; i += 4) {
      // Increased gap
      if (i + 1 >= points.length) break;
      polylines.add(
        Polyline(
          polylineId: PolylineId('$idPrefix-$i'),
          points: [points[i], points[i + 1]],
          color: AppColors.black50, // Added opacity
          width: width.toInt(),
        ),
      );
    }

    return polylines;
  }

  void _setMarkers({
    required LatLng pickup,
    required LatLng destination,
  }) async {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('pickup'),
          position: pickup,
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
        Marker(
          markerId: const MarkerId('destination'),
          position: destination,
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      };
    });
  }

  void _addDriverMarker(LatLng driverLatLng) async {
    final carIcon = await _loadCarIcon();
    if (!mounted) return;

    setState(() {
      _driverMarker = Marker(
        markerId: const MarkerId('driver'),
        position: driverLatLng,
        icon: carIcon,
        rotation: 0,
        anchor: const Offset(0.5, 0.5),
        flat: true,
        zIndexInt: 2,
      );
      _markers = {..._markers, _driverMarker!};
    });
    _previousDriverLocation = driverLatLng;
  }

  Future<BitmapDescriptor> _loadCarIcon() async {
    try {
      final carImage = await BitmapDescriptor.asset(
        ImageConfiguration(size: const Size(64, 64)),
        'assets/images/booking/map_car.png',
      );
      return carImage;
    } catch (e) {
      debugPrint('Error loading car icon: $e');
      // Fallback to default marker
      return BitmapDescriptor.defaultMarker;
    }
  }

  /// Enhanced driver location update method
  void _updateDriverLocationWithBearing(LatLng newLatLng, double bearing) {
    if (_driverMarker == null) {
      _addDriverMarker(newLatLng);
      return;
    }

    if (_previousDriverLocation != null) {
      final distance = Geolocator.distanceBetween(
        _previousDriverLocation!.latitude,
        _previousDriverLocation!.longitude,
        newLatLng.latitude,
        newLatLng.longitude,
      );

      // ✅ Dynamic threshold based on speed
      final threshold = getUpdateThreshold();
      if (distance < threshold) return;
    }

    // Calculate bearing from previous position if not provided
    if (bearing == 0.0) {
      bearing = _routeService.bearingBetween(
        _driverMarker!.position,
        newLatLng,
      );
    }

    setState(() {
      _driverMarker = _driverMarker!.copyWith(
        positionParam: newLatLng,
        rotationParam: bearing,
      );

      _markers = {
        ..._markers.where((m) => m.markerId != _driverMarker!.markerId),
        _driverMarker!,
      };
    });

    _previousDriverLocation = newLatLng;

    // // Optional: Follow driver smoothly
    // if (_mapController != null) {
    // _mapController!.animateCamera(
    //   CameraUpdate.newCameraPosition(
    //     CameraPosition(
    //       target: newLatLng,
    //       zoom: 16.0,
    //       tilt: 30.0, // 3D effect
    //     ),
    //   ),
    // );
    // }
  }

  // ✅ Handle app resume - reload ride details API and update UI
  Future<void> _handleAppResumeRideRefresh() async {
    if (!mounted) return;

    debugPrint(
      '🔄 App resumed on ride screen - fetching ride details from API',
    );

    final activeRideID = _prefService.activeRideId;
    if (!mounted || activeRideID.isEmpty) return;

    _fetchRideDetails(activeRideID);
    _initSocket();
  }

  // //MARK: - Simulation
  // /// Sample pickup and destination with route points for simulation
  // void _initializeSimulationRoute() {
  //   if (_coordinates.isNotEmpty) {
  //     debugPrint(
  //       '🛣️ Using actual route with ${_coordinates.length} points for simulation',
  //     );

  //     // ✅ Sample points for realistic timing (e.g., 30-50 points max)
  //     const maxSimulationPoints = 40;
  //     _simulationRoute = _sampleRoutePoints(_coordinates, maxSimulationPoints);

  //     debugPrint(
  //       '📍 Sampled to ${_simulationRoute.length} points for simulation',
  //     );
  //   } else {
  //     // Fallback to generated points if route not drawn yet
  //     debugPrint('⚠️ Route not available yet, using generated points');
  //     final pickup =
  //         pickupLatLng ?? LatLng(8.546924203996085, 76.8861423432827);
  //     final destination = destinationLatLng ?? LatLng(8.5128139, 76.9015952);
  //     _simulationRoute = _generateRoutePoints(pickup, destination, 25);
  //   }

  //   _currentSimulationIndex = 0;
  // }

  // /// Update route progress as driver moves along actual route
  // void _updateRouteProgress(int currentIndex) {
  //   if (currentIndex <= 0 || _simulationRoute.isEmpty) return;

  //   // Points the driver has already traveled
  //   final traveledPoints = _simulationRoute.take(currentIndex + 1).toList();

  //   // Points still to travel
  //   final remainingPoints = _simulationRoute.skip(currentIndex).toList();

  //   setState(() {
  //     // Remove old progress polylines
  //     _dottedPolylines.removeWhere(
  //       (p) =>
  //           p.polylineId.value.contains('progress_traveled') ||
  //           p.polylineId.value.contains('progress_remaining'),
  //     );

  //     // Add traveled route (green)
  //     if (traveledPoints.length > 1) {
  //       _dottedPolylines.add(
  //         Polyline(
  //           polylineId: const PolylineId('progress_traveled'),
  //           points: traveledPoints,
  //           color: Colors.green,
  //           width: 6,
  //         ),
  //       );
  //     }

  //     // Add remaining route (gray/lighter)
  //     if (remainingPoints.length > 1) {
  //       _dottedPolylines.add(
  //         Polyline(
  //           polylineId: const PolylineId('progress_remaining'),
  //           points: remainingPoints,
  //           color: Colors.grey.withOpacity(0.5),
  //           width: 4,
  //         ),
  //       );
  //     }
  //   });
  // }

  // /// Generate intermediate points between start and end
  // List<LatLng> _generateRoutePoints(LatLng start, LatLng end, int numPoints) {
  //   List<LatLng> points = [start];

  //   // Generate intermediate points with some randomness for realism
  //   for (int i = 1; i < numPoints; i++) {
  //     final ratio = i / numPoints;

  //     // Add slight random variations to make path more realistic
  //     final randomLat =
  //         (Random().nextDouble() - 0.5) * 0.001; // ~100m variation
  //     final randomLng = (Random().nextDouble() - 0.5) * 0.001;

  //     final lat =
  //         start.latitude + (end.latitude - start.latitude) * ratio + randomLat;
  //     final lng =
  //         start.longitude +
  //         (end.longitude - start.longitude) * ratio +
  //         randomLng;

  //     points.add(LatLng(lat, lng));
  //   }

  //   points.add(end);
  //   return points;
  // }

  // /// Start driver location simulation with route check
  // void _startDriverLocationSimulation() async {
  //   if (_isSimulationActive) return;

  //   // ✅ Wait for route to be drawn if not available
  //   if (_coordinates.isEmpty) {
  //     debugPrint('⏳ Route not ready, drawing route first...');
  //     await _drawRoute();

  //     // Wait a bit for route to be processed
  //     await Future.delayed(Duration(milliseconds: 500));
  //   }

  //   _initializeSimulationRoute();
  //   _isSimulationActive = true;
  //   _currentSimulationIndex = 0;

  //   if (_driverMarker == null && _simulationRoute.isNotEmpty) {
  //     _addDriverMarker(_simulationRoute.first);
  //   }

  //   debugPrint(
  //     '🚗 Starting driver location simulation with ${_simulationRoute.length} points',
  //   );

  //   _scheduleNextLocationUpdate();
  // }

  // /// Stop driver location simulation
  // void _stopDriverLocationSimulation() {
  //   _simulationTimer?.cancel();
  //   _simulationTimer = null;
  //   _isSimulationActive = false;
  //   _currentSimulationIndex = 0;

  //   // ✅ Clean up progress polylines
  //   setState(() {
  //     _dottedPolylines.removeWhere(
  //       (p) =>
  //           p.polylineId.value.contains('progress_traveled') ||
  //           p.polylineId.value.contains('progress_remaining'),
  //     );
  //   });

  //   debugPrint('🛑 Driver location simulation stopped');
  // }

  // /// Schedule next location update with random interval
  // /// Schedule next location update with random interval
  // void _scheduleNextLocationUpdate() {
  //   if (!_isSimulationActive ||
  //       _currentSimulationIndex >= _simulationRoute.length) {
  //     debugPrint('✅ Driver simulation completed - reached destination');
  //     _stopDriverLocationSimulation();
  //     return;
  //   }

  //   // Random interval between 2-8 seconds (realistic driving speed)
  //   final randomSeconds = 2 + Random().nextInt(6);

  //   _simulationTimer = Timer(Duration(seconds: randomSeconds), () {
  //     if (!mounted || !_isSimulationActive) return;

  //     if (_currentSimulationIndex >= _simulationRoute.length) {
  //       _stopDriverLocationSimulation();
  //       return;
  //     }

  //     final currentPoint = _simulationRoute[_currentSimulationIndex];

  //     debugPrint(
  //       '📍 Simulating driver location: ${currentPoint.latitude}, ${currentPoint.longitude} (Point ${_currentSimulationIndex + 1}/${_simulationRoute.length})',
  //     );

  //     // Update driver location on map
  //     _updateDriverLocation(currentPoint);

  //     // ✅ Update route progress visualization
  //     _updateRouteProgress(_currentSimulationIndex);

  //     // Simulate socket emission
  //     _simulateSocketLocationUpdate(currentPoint);

  //     _currentSimulationIndex++;

  //     // Schedule next update
  //     _scheduleNextLocationUpdate();
  //   });
  // }

  // /// Simulate socket location update (for testing socket flow)
  // void _simulateSocketLocationUpdate(LatLng location) {
  //   // Update the provider as if it came from socket
  //   ref.read(driverLocationProvider.notifier).update(location);

  //   // Optional: You can also trigger your socket listener directly
  //   // This simulates what would happen when real socket data arrives
  // }

  // Widget _buildSimulationControls() {
  //   // Only show in debug mode
  //   if (!kDebugMode) return const SizedBox.shrink();

  //   return Column(
  //     mainAxisSize: MainAxisSize.min,
  //     children: [
  //       // Status indicator
  //       if (_isSimulationActive)
  //         Container(
  //           padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
  //           decoration: BoxDecoration(
  //             color: Colors.orange,
  //             borderRadius: BorderRadius.circular(20),
  //           ),
  //           child: Text(
  //             'Simulating ${_currentSimulationIndex}/${_simulationRoute.length}',
  //             style: TextStyle(color: Colors.white, fontSize: 12),
  //           ),
  //         ),

  //       const SizedBox(height: 8),

  //       FloatingActionButton(
  //         heroTag: "start_simulation",
  //         onPressed: _isSimulationActive
  //             ? null
  //             : _startDriverLocationSimulation,
  //         backgroundColor: _isSimulationActive ? Colors.grey : Colors.green,
  //         child: Icon(Icons.play_arrow, color: Colors.white),
  //       ),
  //       const SizedBox(height: 10),
  //       FloatingActionButton(
  //         heroTag: "stop_simulation",
  //         onPressed: _isSimulationActive ? _stopDriverLocationSimulation : null,
  //         backgroundColor: _isSimulationActive ? Colors.red : Colors.grey,
  //         child: Icon(Icons.stop, color: Colors.white),
  //       ),
  //     ],
  //   );
  // }

  // /// Sample points from route for realistic timing
  // List<LatLng> _sampleRoutePoints(List<LatLng> routePoints, int maxPoints) {
  //   if (routePoints.length <= maxPoints) {
  //     return routePoints;
  //   }

  //   List<LatLng> sampledPoints = [routePoints.first]; // Always include start

  //   final step = routePoints.length / (maxPoints - 2); // -2 for start and end

  //   for (int i = 1; i < maxPoints - 1; i++) {
  //     final index = (step * i).round();
  //     if (index < routePoints.length) {
  //       sampledPoints.add(routePoints[index]);
  //     }
  //   }

  //   sampledPoints.add(routePoints.last); // Always include end

  //   return sampledPoints;
  // }

  // /// Test with different city routes
  // void _testDifferentRoutes() {
  //   // Mumbai route
  //   const mumbaiPickup = LatLng(19.0760, 72.8777); // Mumbai Central
  //   const mumbaiDestination = LatLng(19.0896, 72.8656); // Bandra

  //   // Bangalore route
  //   const bangalorePickup = LatLng(12.9716, 77.5946); // MG Road
  //   const bangaloreDestination = LatLng(12.9698, 77.7500); // Whitefield

  //   // You can switch between these for testing
  //   final routes = {
  //     'Mumbai': [mumbaiPickup, mumbaiDestination],
  //     'Bangalore': [bangalorePickup, bangaloreDestination],
  //   };

  //   // Use current route or fallback to Mumbai
  //   final currentRoute = routes['Mumbai']!;
  //   _simulationRoute = _generateRoutePoints(
  //     currentRoute[0],
  //     currentRoute[1],
  //     30,
  //   );
  // }
}
