import 'dart:async';
import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/services/google_place_service.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/cancel_button.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/core/widgets/nav_back_button.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/presentation/provider/ride_provider.dart';
import 'package:tukxi/features/booking/presentation/state/ride_state.dart';
import 'package:tukxi/features/booking/presentation/widgets/confirm_pickup/confirm_pickp_draggable_view.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/favourite_map_section.dart';
import 'package:tukxi/features/favourite_locations/presentation/widgets/search_location_field_button.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';
import 'package:tukxi/features/home/<USER>/providers/location_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/features/location_selection/data/models/place_suggestion.dart';
import 'package:tukxi/features/location_selection/presentation/providers/place_suggestions_provider.dart';
import 'package:tukxi/routes/app_routes.dart';

class ConfirmPickupScreen extends ConsumerStatefulWidget {
  const ConfirmPickupScreen({super.key, required this.rideRequest});

  final RideRequest rideRequest;
  @override
  ConsumerState<ConfirmPickupScreen> createState() {
    return _ConfirmPickupScreenState();
  }
}

class _ConfirmPickupScreenState extends ConsumerState<ConfirmPickupScreen> {
  LatLng? get pickupLocation {
    return widget.rideRequest.pickupLoctaion.latLng;
  }

  double _navButtonHeight = 0;
  double _sheetSize = 0.5;
  double _mapAreaHeight = 0;
  LatLng? _lastFetched;

  bool _isUserLocationEnabled = false;

  GoogleMapController? _mapController;

  LocationParams? _placeDetails;

  LatLng? _pickedlocation;
  LatLng? _dragLocation;

  List<dynamic> _zones = [];

  Timer? _debounce;

  DateTime? _lastUpdate;

  final GlobalKey _navButtonkey = GlobalKey();

  static const double _minDistance = 10;
  bool _isLoading = true;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    _pickedlocation = pickupLocation;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final navContext = _navButtonkey.currentContext;

      if (navContext != null) {
        final navButtonBox = navContext.findRenderObject() as RenderBox;
        setState(() {
          _navButtonHeight = navButtonBox.size.height;
        });
      }

      _fetchPlaceDetails(coordinates: pickupLocation);
    });
  }

  @override
  Widget build(BuildContext context) {
    final locationState = ref.watch(locationProvider);
    _isUserLocationEnabled =
        locationState.gpsEnabled && locationState.permissionGranted;
    final topInset = MediaQuery.of(context).padding.top;

    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: SafeArea(
        top: false,
        bottom: Platform.isAndroid,
        child: Column(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraint) {
                  _mapAreaHeight = constraint.maxHeight;

                  return Stack(
                    children: [
                      Positioned.fill(
                        child: FavouriteMapSection(
                          isFromFav: false,
                          mapHeight: _mapAreaHeight,
                          currentLocation: pickupLocation,
                          isUserLocationEnabled: _isUserLocationEnabled,
                          onCameraIdle: _onCameraIdle,
                          onMapCreated: _onMapCreated,
                          onCameraMove: _onCameraMove,
                          hasOverlayElements: true,
                        ),
                      ),

                      Positioned(
                        left: 5,
                        top: topInset,
                        child: NavBackButton(
                          key: _navButtonkey,
                          onPressed: () => context.pop(),
                        ),
                      ),

                      Positioned(
                        left: 15,
                        right: 15,
                        top: topInset + _navButtonHeight + 10,
                        child: SearchLocationFieldButton(
                          searchTapped: _onSearchTapped,
                        ),
                      ),

                      if (_zones.isNotEmpty)
                        DraggableScrollableSheet(
                          initialChildSize: _sheetSize,
                          maxChildSize: UIConstants.sheetMaxSize,
                          minChildSize: _sheetSize,
                          builder: (context, scrollController) {
                            return ConfirmPickpDraggableView(
                              scrollController: scrollController,
                              zones: _zones,
                            );
                          },
                        ),
                    ],
                  );
                },
              ),
            ),
            _buildBottomView(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomView() {
    final state = ref.watch(rideProvider);

    return Container(
      padding: const EdgeInsets.only(left: 15, right: 15, top: 20, bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(LocationConstants.mapCornerRadius),
          topRight: Radius.circular(LocationConstants.mapCornerRadius),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _isLoading
              ? ShimmerLoaders.titleAndValueShimmer()
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _placeDetails?.locationName ?? '',
                      maxLines: 2,
                      style: GoogleFonts.inter(
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      maxLines: 2,
                      _placeDetails?.locationAddress ?? '',
                      style: GoogleFonts.inter(
                        color: AppColors.black50,
                        fontWeight: FontWeight.w400,
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
          const SizedBox(height: 20),

          LoadingButton(
            isLoading: state is RideLoading,
            onPressed: _requestARide,
            text: 'Confirm Pick-up',
          ),
        ],
      ),
    );
  }

  double _calculateDistance(LatLng point1, LatLng point2) {
    return GooglePlacesService.calculateDistance(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    );
  }

  /// Check if pickup location has changed significantly (>100m)
  bool _isPickupLocationValid() {
    if (pickupLocation == null || _placeDetails == null) {
      return false;
    }

    final distance = _calculateDistance(
      pickupLocation!,
      _placeDetails!.latLng!,
    );
    return distance <= 100.0; // 100 meters threshold
  }

  void _onSearchTapped() async {
    final result = await context.push<Map<String, dynamic>>(
      AppRoutes.searchLocation,
      extra: {'currentLocation': pickupLocation},
    );

    if (result != null) {
      final useCurrentLocation = result['useCurrentLocation'] as bool;

      final place = result['location'] as PlaceSuggestion?;

      if (useCurrentLocation) {
        setState(() {
          _placeDetails = widget.rideRequest.pickupLoctaion;
        });
        if (_placeDetails != null) {
          _mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: _placeDetails!.latLng!,
                zoom: LocationConstants.zoomLevel,
              ),
            ),
          );
        }
      } else if (place != null) {
        _fetchCoordinatesAsynchronously(location: place);
      }
    }
  }

  Future _fetchPlaceDetails({required LatLng? coordinates}) async {
    if (!mounted) return;
    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getPlaceDetailsFromCoordinates(coordinates),
      onSuccess: (response) {
        if (!mounted) return;
        setState(() {
          _placeDetails = response;
          _lastFetched = coordinates;
          _isLoading = false;
        });
      },
    );
  }

  /// Debounced place fetch
  void _onCameraIdleDebounced(LatLng location) {
    if (_pickedlocation == null) return;

    if (_lastFetched != null) {
      final dist = GooglePlacesService.calculateDistance(
        _lastFetched!.latitude,
        _lastFetched!.longitude,
        location.latitude,
        location.longitude,
      );
      if (dist < _minDistance) return;
    }

    _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 400), () {
      if (!mounted) return;

      _fetchPlaceDetails(coordinates: location);
    });
  }

  void _onCameraMove(CameraPosition position) {
    setState(() {
      _isLoading = true;
    });
    _dragLocation = position.target;
  }

  void _onCameraIdle() {
    if (_dragLocation != null) {
      _pickedlocation = _dragLocation;
      _dragLocation = null;
    }
    _onCameraIdleDebounced(_pickedlocation!);
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    _updateMapPadding();

    // if (pickupLocation != null) {
    //   controller.animateCamera(
    //     CameraUpdate.newCameraPosition(
    //       CameraPosition(
    //         target: pickupLocation!,
    //         zoom: LocationConstants.zoomLevel,
    //       ),
    //     ),
    //   );
    // }
  }

  void _updateMapPadding() {
    if (!mounted) return;
    if (_mapAreaHeight <= 0) return;

    final newPadding = _zones.isEmpty ? 0.0 : _mapAreaHeight * _sheetSize;

    // ✅ Throttle (only update if >100ms since last update OR big change)
    final now = DateTime.now();
    if (_lastUpdate == null ||
        now.difference(_lastUpdate!) > const Duration(milliseconds: 100) ||
        (ref.read(mapPaddingProvider) - newPadding).abs() > 5) {
      ref.read(mapPaddingProvider.notifier).state = newPadding;

      _lastUpdate = now;
    }
  }

  Future _fetchCoordinatesAsynchronously({
    required PlaceSuggestion location,
  }) async {
    await _handleApiCall(
      () => ref
          .read(placeSuggestionsProvider.notifier)
          .getCoordinatesFromPlaceID(location.placeId),
      onSuccess: (coordinates) {
        if (!mounted || coordinates == null) return;

        final placeWithLatLng = LocationParams(
          id: location.placeId,
          locationName: location.name,
          latLng: coordinates,
          locationAddress: location.address,
        );
        setState(() {
          _placeDetails = placeWithLatLng;
        });
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: coordinates,
              zoom: LocationConstants.zoomLevel,
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleApiCall<T>(
    Future<Either<Failure, T>> Function() apiCall, {
    Function(T response)? onSuccess,
    String? errorMessage,
  }) async {
    final result = await apiCall();
    if (!mounted) return;

    result.fold(
      (failure) {
        if (!mounted) return;
        handleApiError(
          context: context,
          failure: failure,
          errorMessage: errorMessage,
          onRetry: () async => _handleApiCall(apiCall, onSuccess: onSuccess),
        );
      },
      (response) {
        if (!mounted) return;
        onSuccess?.call(response);
      },
    );
  }

  Future _requestARide() async {
    if (_placeDetails == null || _placeDetails?.latLng == null) {
      return;
    }

    if (!_isPickupLocationValid()) {
      _displayInvalidPickupAlert();

      return;
    }

    await _proceedWithRideRequest();
  }

  Future _proceedWithRideRequest() async {
    final rideRequest = RideRequest(
      pickupLoctaion: _placeDetails!,
      destinationLoctaion: widget.rideRequest.destinationLoctaion,
      service: widget.rideRequest.service,
    );

    await _handleApiCall(
      () => ref.read(rideProvider.notifier).rideRequest(rideRequest),
      onSuccess: (response) {
        if (!mounted || response.ride == null || response.ride!.id == null) {
          return;
        }
        ref.read(rideRefreshProvider.notifier).state = true;

        context.push(
          AppRoutes.ride,
          extra: {
            'rideRequest': RideRequest(
              pickupLoctaion: _placeDetails!,
              destinationLoctaion: widget.rideRequest.destinationLoctaion,
              service: widget.rideRequest.service,
            ),
            'ride': response.ride!,
          },
        );
      },
    );
  }

  void _displayInvalidPickupAlert() {
    showModalBottomSheet(
      context: context,
      builder: (ctx) {
        return SafeArea(
          bottom: Platform.isAndroid,
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Confirm updated fare',
                        style: GoogleFonts.inter(
                          fontSize: 20,
                          color: Colors.black,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 30,
                      width: 30,
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        onPressed: () => context.pop(),
                        alignment: Alignment.centerRight,
                        icon: Image.asset(AssetPaths.popupClose),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
                Text(
                  'Your trip fare has been recalculated due to the change in pickup location. Please confirm the new amount.',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    color: AppColors.black75,
                    fontWeight: FontWeight.w500,
                  ),
                ),

                SizedBox(height: 20),

                Row(
                  children: [
                    CancelButton(
                      onCancelPressed: () {
                        if (!mounted) return;

                        context.pop();
                        // context.pop();
                        // context.pop();
                      },
                      title: 'Cancel',
                    ),
                    SizedBox(width: 15),
                    Expanded(
                      child: SizedBox(
                        height: 45,
                        child: FilledButton(
                          onPressed: () {
                            context.pop();
                            _proceedWithRideRequest();
                          },
                          style: FilledButton.styleFrom(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                8,
                              ), // rounded corners
                            ),
                          ),
                          //TODO: pricing
                          child: Text(
                            'Accept ₹ 0.00',
                            style: GoogleFonts.inter(
                              fontWeight: FontWeight.w500,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 20),
              ],
            ),
          ),
        );
      },
    );
  }
}
