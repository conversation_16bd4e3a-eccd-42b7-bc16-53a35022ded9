import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_pannable_rating_bar/flutter_pannable_rating_bar.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:keyboard_actions/keyboard_actions.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/driver_image_with_car.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/presentation/provider/ride_provider.dart';
import 'package:tukxi/features/booking/presentation/state/ride_state.dart';

class RatingsScreen extends ConsumerStatefulWidget {
  const RatingsScreen({
    super.key,
    required this.rideId,
    required this.driverDetails,
    required this.onClose,
    this.onReviewSubmitted,
    this.isFromTripDetails = false,
  });

  final bool isFromTripDetails;
  final String rideId;
  final Driver? driverDetails;
  final VoidCallback onClose;
  final VoidCallback? onReviewSubmitted;

  @override
  ConsumerState<RatingsScreen> createState() => _RatingsScreenState();
}

class _RatingsScreenState extends ConsumerState<RatingsScreen> {
  // Add these controllers

  final TextEditingController _feedbackController = TextEditingController();
  final FocusNode _feedbackFocusNode = FocusNode();

  Timer? _redirectTimer;
  bool _isSubmit = false;
  double _rating = 0;
  int _countdown = 5;

  KeyboardActionsConfig _buildKeyboardActionsConfig(BuildContext context) {
    return KeyboardActionsConfig(
      keyboardActionsPlatform: KeyboardActionsPlatform.IOS,
      nextFocus: false,
      actions: [
        KeyboardActionsItem(
          focusNode: _feedbackFocusNode,
          displayArrows: false,
          displayDoneButton: true,
          toolbarButtons: [
            (node) => TextButton(
              onPressed: () {
                node.unfocus();
                FocusScope.of(context).unfocus();
              },
              child: Text(
                'Done',
                style: GoogleFonts.inter(
                  color: AppColors.primary,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    _feedbackFocusNode.dispose();
    _redirectTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: SafeArea(
        top: false,
        bottom: Platform.isAndroid,
        child: KeyboardActions(
          config: _buildKeyboardActionsConfig(context),
          child: Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              padding: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(24),
              ),
              child: SingleChildScrollView(
                physics: Platform.isAndroid
                    ? const ClampingScrollPhysics()
                    : const BouncingScrollPhysics(
                        parent: ClampingScrollPhysics(),
                      ),
                child: _isSubmit
                    ? _buildSuccessView()
                    : _buildRatingAndReviewSection(),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSuccessView() {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        children: [
          PannableRatingBar(
            rate: _rating,
            alignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            gestureType: GestureType.tapOnly,
            valueTransformer: halfFractionalValueTransformer,
            items: List.generate(
              5,
              (index) => RatingWidget(
                selectedColor: AppColors.ratingOn,
                unSelectedColor: AppColors.ratingOff,
                child: Container(
                  width: 42,
                  height: 36,
                  padding: const EdgeInsets.all(3),
                  child: Image.asset(
                    AssetPaths.ratingStar,
                    height: 30,
                    width: 30,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: 10),

          Text(
            'Thank you for sharing your feedback!',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 15),

          Text(
            widget.isFromTripDetails
                ? 'Taking you back  in ${_countdown}s...'
                : 'Taking you back to home screen in ${_countdown}s...',
            style: GoogleFonts.inter(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: AppColors.black50,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRatingAndReviewSection() {
    final state = ref.watch(rideProvider);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 10),
        Align(
          alignment: AlignmentGeometry.centerRight,
          child: Container(
            padding: EdgeInsets.zero,
            alignment: AlignmentGeometry.centerRight,
            height: 35,
            width: 35,
            child: IconButton(
              onPressed: widget.onClose,
              alignment: Alignment.centerRight,
              icon: Image.asset(AssetPaths.popupClose),
            ),
          ),
        ),
        DriverImageWithCar(imageUrl: widget.driverDetails?.profilePic ?? ''),

        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
          child: Column(
            children: [
              _buildRatingView(),
              _buildRatingSection(),
              _buildFeedbackView(),

              LoadingButton(
                isLoading: state is RideLoading,
                onPressed: () {
                  _validate();
                },
                text: 'Submit Review',
              ),

              const SizedBox(height: 10),
              _buildBackToHomeButton(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBackToHomeButton() {
    return TextButton(
      onPressed: widget.onClose,
      child: Text(
        "Back to Home",
        style: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildRatingView() {
    return Column(
      children: [
        Text(
          "Your ride has completed",
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 12),

        // Subtitle
        Text(
          "Please take a moment to rate your driver and share your feedback.",
          style: GoogleFonts.inter(
            fontSize: 10,
            fontWeight: FontWeight.w500,
            color: AppColors.black50,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 30),
      ],
    );
  }

  Widget _buildRatingSection() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.greyBg,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Text(
                "How was your ride?",
                style: GoogleFonts.inter(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black,
                ),
              ),

              const SizedBox(height: 20),

              PannableRatingBar(
                rate: _rating,
                alignment: WrapAlignment.center,
                crossAxisAlignment: WrapCrossAlignment.center,
                minRating: 0.5,
                maxRating: 5,
                valueTransformer: halfFractionalValueTransformer,
                items: List.generate(
                  5,
                  (index) => RatingWidget(
                    selectedColor: AppColors.ratingOn,
                    unSelectedColor: AppColors.ratingOff,
                    child: Container(
                      width: 42,
                      height: 36,
                      padding: const EdgeInsets.all(3),
                      child: Image.asset(
                        AssetPaths.ratingStar,
                        height: 30,
                        width: 30,
                      ),
                    ),
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _rating = value;
                  });
                },
              ),
            ],
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildFeedbackView() {
    return Column(
      children: [
        // Feedback section
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            "Share Your Feedback",
            style: GoogleFonts.inter(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: AppColors.black50,
            ),
          ),
        ),

        const SizedBox(height: 12),

        // Feedback text field
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.black10),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            focusNode: _feedbackFocusNode,
            controller: _feedbackController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: "Tell us about your experience...",
              hintStyle: GoogleFonts.inter(
                color: AppColors.black40,
                fontSize: 13,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(10),
            ),
            style: GoogleFonts.inter(fontSize: 14, color: Colors.black),
            onSubmitted: (value) {
              _feedbackFocusNode.unfocus();
            },
          ),
        ),

        const SizedBox(height: 24),
      ],
    );
  }

  void _validate() {
    if (_rating == 0) {
      SnackbarUtils.showSnackBar(
        context: context,
        message: 'Please rate the driver to submit your feedback.',
        type: SnackBarType.error,
      );
      return;
    }

    // Unfocus keyboard
    _feedbackFocusNode.unfocus();
    FocusScope.of(context).unfocus();

    final reviewRequest = ReviewRequest(
      rideId: widget.rideId,
      rating: '$_rating',
      review: _feedbackController.text,
    );

    _feedbackFocusNode.unfocus();

    _reviewDriver(reviewRequest);
  }

  Future<void> _reviewDriver(ReviewRequest reviewRequest) async {
    final result = await ref
        .read(rideProvider.notifier)
        .reviewDriver(reviewRequest);

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _reviewDriver(reviewRequest),
        );
      },
      (response) {
        setState(() {
          _isSubmit = true;
          _countdown = 5;
        });

        _startRedirectTimer();
      },
    );
  }

  void _startRedirectTimer() {
    _redirectTimer = Timer.periodic(Duration(seconds: 1), (timer) {
      setState(() {
        _countdown--;
      });

      if (_countdown <= 0) {
        timer.cancel();
        if (widget.isFromTripDetails) {
          widget.onReviewSubmitted?.call();
        } else {
          widget.onClose();
        }
      }
    });
  }
}
