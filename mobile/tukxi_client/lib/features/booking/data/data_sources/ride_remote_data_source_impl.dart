import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/booking/data/data_sources/ride_remote_data_source.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';

class RideRemoteDataSourceImpl implements RideRemoteDataSource {
  final ApiService _apiService;

  RideRemoteDataSourceImpl(this._apiService);

  @override
  Future<RideResponse> rideRequest(RideRequest request) {
    final stops = request.stops
        ?.map(
          (stop) => {
            'lat': stop.latLng?.latitude,
            'lng': stop.latLng?.longitude,
            'address': '${stop.locationName}, ${stop.locationAddress}',
          },
        )
        .toList();
    Map<String, dynamic> body = {
      'pickup': {
        'lat': request.pickupLoctaion.latLng?.latitude,
        'lng': request.pickupLoctaion.latLng?.longitude,
        'address':
            '${request.pickupLoctaion.locationName}, ${request.pickupLoctaion.locationAddress}',
      },
      'destination': {
        'lat': request.destinationLoctaion.latLng?.latitude,
        'lng': request.destinationLoctaion.latLng?.longitude,
        'address':
            '${request.destinationLoctaion.locationName}, ${request.destinationLoctaion.locationAddress}',
      },
      'stops': stops,
      'productId': request.service.id,
    };

    return _apiService.post(
      '${Endpoint.ride.value}/request',
      (json) => RideResponse.fromJson(json),
      body: body,
    );
  }

  @override
  Future<RideResponse> cancelRide(String rideId) {
    return _apiService.post(
      '${Endpoint.ride.value}/$rideId/cancel/rider',
      (json) => RideResponse.fromJson(json),
    );
  }

  @override
  Future<RideListResponse> fetchAllRides() {
    return _apiService.get(
      '${Endpoint.ride.value}/rider/my-rides',
      (json) => RideListResponse.fromJson(json),
    );
  }

  @override
  Future<RideResponse> fetchRideDetails(String rideId) {
    return _apiService.get(
      '${Endpoint.ride.value}/$rideId/rider',
      (json) => RideResponse.fromJson(json),
    );
  }

  @override
  Future<ReviewResponse> reviewDriver(ReviewRequest request) {
    return _apiService.post(
      Endpoint.reviewDriver.value,
      (json) => ReviewResponse.fromJson(json),
      body: request.toJson(),
    );
  }
}
