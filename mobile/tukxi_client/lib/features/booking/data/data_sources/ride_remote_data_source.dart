import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';

abstract class RideRemoteDataSource {
  Future<RideResponse> rideRequest(RideRequest request);
  Future<RideResponse> cancelRide(String rideId);
  Future<RideListResponse> fetchAllRides();
  Future<RideResponse> fetchRideDetails(String rideId);
  Future<ReviewResponse> reviewDriver(ReviewRequest request);
}
