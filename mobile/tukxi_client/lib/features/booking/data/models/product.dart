import 'package:tukxi/features/booking/domain/entities/ride_entity.dart';

class Product extends ProductEntity {
  Product({
    required super.id,
    required super.name,
    required super.description,
    required super.identifier,
    required super.icon,
    required super.isEnabled,
  });

  factory Product.fromJson(Map<String, dynamic> json) => Product(
    id: json['id'] as String?,
    name: json['name'] as String?,
    description: json['description'] as String?,
    identifier: json['identifier'] as String?,
    icon: json['icon'] as String?,
    isEnabled: json['isEnabled'] as bool? ?? false,
  );

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'identifier': identifier,
    'icon': icon,
    'isEnabled': isEnabled,
  };
}
