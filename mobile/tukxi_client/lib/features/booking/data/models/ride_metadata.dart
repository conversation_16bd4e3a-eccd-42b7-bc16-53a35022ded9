class RideMetadata {
  final int? driversFound;
  final int? batchNumber;
  final int? processingTimeMs;

  RideMetadata({this.driversFound, this.batchNumber, this.processingTimeMs});

  factory RideMetadata.fromJson(Map<String, dynamic> json) {
    return RideMetadata(
      driversFound: json['driversFound'] as int?,
      batchNumber: json['batchNumber'] as int?,
      processingTimeMs: json['processingTimeMs'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'driversFound': driversFound,
      'batchNumber': batchNumber,
      'processingTimeMs': processingTimeMs,
    };
  }
}
