import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/booking/domain/entities/review_response_entity.dart';

class ReviewResponse extends ReviewResponseEntity {
  ReviewResponse({
    required super.success,
    required super.message,
    super.data,
    super.timestamp,
  });

  factory ReviewResponse.fromJson(Map<String, dynamic> json) {
    return ReviewResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? ReviewData.fromJson(json['data']) : null,
      timestamp: json['timestamp'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'timestamp': timestamp,
    };
  }
}

class ReviewData extends ReviewDataEntity {
  ReviewData({
    required super.id,
    required super.riderId,
    required super.driverId,
    required super.rideId,
    required super.reviewById,
    required super.rating,
    super.review,
    required super.createdAt,
    required super.updatedAt,
    super.rider,
    super.driver,
    super.ride,
    super.reviewBy,
  });

  factory ReviewData.fromJson(Map<String, dynamic> json) {
    return ReviewData(
      id: json['id'] ?? '',
      riderId: json['riderId'] ?? '',
      driverId: json['driverId'] ?? '',
      rideId: json['rideId'] ?? '',
      reviewById: json['reviewById'] ?? '',
      rating: json['rating'] ?? '0',
      review: json['review'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      rider: json['rider'] != null ? AuthUser.fromJson(json['rider']) : null,
      driver: json['driver'] != null ? AuthUser.fromJson(json['driver']) : null,
      ride: json['ride'] != null ? Ride.fromJson(json['ride']) : null,
      reviewBy: json['reviewBy'] != null
          ? AuthUser.fromJson(json['reviewBy'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'riderId': riderId,
      'driverId': driverId,
      'rideId': rideId,
      'reviewById': reviewById,
      'rating': rating,
      'review': review,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'rider': rider?.toJson(),
      'driver': driver?.toJson(),
      'ride': ride?.toJson(),
      'reviewBy': reviewBy?.toJson(),
    };
  }
}
