import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/product.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';

class RideLifecycleEntity {
  final String? id;
  final String? status;
  final Map<String, dynamic>? meta;
  final DateTime? createdAt;

  RideLifecycleEntity({
    required this.id,
    required this.status,
    required this.meta,
    required this.createdAt,
  });
}

class RideEntity {
  final String? id;
  final String? driverId;
  final String? riderId;
  final String? productId;
  final String? status;
  final LocationResponse? pickupLocation;
  final LocationResponse? destinationLocation;
  final List<LocationResponse>? stops;
  final int? verificationCode;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? completedAt;
  final Driver? driver;
  final Product? product;
  final List<RideLifecycle>? rideLifecycles;
  final Rider? rider;
  final DriverVehicle? vehicle;

  RideEntity({
    required this.id,
    required this.driverId,
    required this.riderId,
    required this.productId,
    required this.status,
    required this.pickupLocation,
    required this.destinationLocation,
    required this.stops,
    required this.verificationCode,
    required this.createdAt,
    required this.updatedAt,
    this.completedAt,
    required this.driver,
    required this.product,
    required this.rideLifecycles,
    this.rider,
    this.vehicle,
  });
}

class RiderEntity {
  final String? id;
  final String? rideOtp;
  final double? rating;

  RiderEntity({required this.id, required this.rideOtp, required this.rating});
}

class DriverVehicleEntity {
  final String? vehicleNumber;
  final VehicleType? vehicleType;

  DriverVehicleEntity({required this.vehicleNumber, required this.vehicleType});
}

class VehicleTypeEntity {
  final String? name;

  VehicleTypeEntity({required this.name});
}

class RoleEntity {
  final String? id;
  final String? name;

  RoleEntity({required this.id, required this.name});
}

class DriverEntity {
  final String? id;
  final String? firstName;
  final String? lastName;
  final int? verificationCode;
  final String? profilePic;
  final DriverMeta? driverMeta;
  final String? vehicleRegNumber;
  final String? vehicleColor;
  final String? vehicleModel;
  final String? vehicleClass;
  final double? driverRating;

  final AuthUser? user;
  final Role? role;

  DriverEntity({
    this.id,
    this.firstName,
    this.lastName,
    this.verificationCode,
    this.profilePic,
    this.driverMeta,
    this.vehicleRegNumber,
    this.vehicleColor,
    this.vehicleModel,
    this.vehicleClass,
    this.user,
    this.role,
    this.driverRating,
  });
}

class DriverMetaEntity {
  final double? avgRating;

  DriverMetaEntity({this.avgRating});
}

class ProductEntity {
  final String? id;
  final String? name;
  final String? description;
  final String? identifier;
  final String? icon;
  final bool? isEnabled;

  ProductEntity({
    required this.id,
    required this.name,
    required this.description,
    required this.identifier,
    required this.icon,
    required this.isEnabled,
  });
}
