import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';

class ReviewResponseEntity {
  final bool success;
  final String message;
  final ReviewData? data;
  final int? timestamp;

  ReviewResponseEntity({
    required this.success,
    required this.message,
    this.data,
    this.timestamp,
  });
}

class ReviewDataEntity {
  final String id;
  final String riderId;
  final String driverId;
  final String rideId;
  final String reviewById;
  final String rating;
  final String? review;
  final DateTime createdAt;
  final DateTime updatedAt;
  final AuthUser? rider;
  final AuthUser? driver;
  final Ride? ride;
  final AuthUser? reviewBy;

  ReviewDataEntity({
    required this.id,
    required this.riderId,
    required this.driverId,
    required this.rideId,
    required this.reviewById,
    required this.rating,
    this.review,
    required this.createdAt,
    required this.updatedAt,
    this.rider,
    this.driver,
    this.ride,
    this.reviewBy,
  });
}

class RideInfoEntity {
  final String id;
  final String status;
  final DateTime createdAt;
  final DateTime? completedAt;

  RideInfoEntity({
    required this.id,
    required this.status,
    required this.createdAt,
    this.completedAt,
  });
}
