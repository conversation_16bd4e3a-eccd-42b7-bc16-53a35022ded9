import 'package:tukxi/features/booking/data/models/ride.dart';

class RideResponseEntity {
  final bool? success;
  final String? message;
  final Ride? ride;
  final int? timestamp;

  RideResponseEntity({
    required this.success,
    required this.message,
    required this.ride,
    required this.timestamp,
  });
}

class RideListResponseEntity {
  final bool? success;
  final String? message;
  final List<Ride>? rides;
  final int? timestamp;

  RideListResponseEntity({
    required this.success,
    required this.message,
    required this.rides,
    required this.timestamp,
  });
}
