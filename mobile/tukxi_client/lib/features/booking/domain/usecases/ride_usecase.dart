import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/booking/data/models/review_request.dart';
import 'package:tukxi/features/booking/data/models/review_response.dart';
import 'package:tukxi/features/booking/data/models/ride_request.dart';
import 'package:tukxi/features/booking/data/models/ride_response.dart';
import 'package:tukxi/features/booking/domain/repositories/ride_repository.dart';

class RideUseCase {
  final RideRepository repository;

  RideUseCase({required this.repository});

  Future<Either<Failure, RideResponse>> executeRideRequest(
    RideRequest request,
  ) => repository.rideRequest(request);

  Future<Either<Failure, RideResponse>> executeCancelRide(String rideId) =>
      repository.cancelRide(rideId);

  Future<Either<Failure, RideListResponse>> executeFetchAllRides() =>
      repository.fetchAllRides();

  Future<Either<Failure, RideResponse>> executeFetchRideDetails(
    String rideId,
  ) => repository.fetchRideDetails(rideId);

  Future<Either<Failure, ReviewResponse>> executeReviewDriver(
    ReviewRequest request,
  ) => repository.reviewDriver(request);
}
