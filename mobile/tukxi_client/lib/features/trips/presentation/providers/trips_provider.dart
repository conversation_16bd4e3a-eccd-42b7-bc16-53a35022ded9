import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/trips/data/data_sources/trip_remote_data_source_impl.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';
import 'package:tukxi/features/trips/data/repository/trips_repository_impl.dart';
import 'package:tukxi/features/trips/domain/usecases/trip_usecase.dart';
import 'package:tukxi/features/trips/presentation/states/trip_state.dart';

final tripsProvider = StateNotifierProvider<TripsNotifier, TripsState>((ref) {
  final repository = TripsRepositoryImpl(
    tripRemoteDataSource: TripRemoteDataSourceImpl(),
  );

  return TripsNotifier(tripUseCase: TripUseCase(repository: repository));
});

class TripsNotifier extends StateNotifier<TripsState> {
  TripsNotifier({required this.tripUseCase}) : super(TripsInitial());

  final TripUseCase tripUseCase;

  // Pagination state
  List<Trip> _allTrips = [];
  int _currentPage = 1;
  bool _hasMoreData = true;
  String? _currentStatus;
  bool _isLoadingMore = false;

  // Getters
  List<Trip> get allTrips => _allTrips;
  int get currentPage => _currentPage;
  bool get hasMoreData => _hasMoreData;
  String? get currentStatus => _currentStatus;
  bool get isLoadingMore => _isLoadingMore;

  /// Fetch trips with pagination support
  Future<Either<Failure, TripHistory>> fetchTrips({
    int page = 1,
    int limit = 10,
    String? status,
    DateTime? from,
    DateTime? to,
    bool isRefresh = false,
  }) async {
    // Reset pagination if it's a refresh or new filter
    if (isRefresh || status != _currentStatus) {
      _resetPagination();
      _currentStatus = status;
      state = TripsLoading();
    } else if (page == 1) {
      state = TripsLoading();
    } else {
      _isLoadingMore = true;
    }

    final result = await tripUseCase.executeFetchTrips(
      page: page,
      limit: limit,
      status: status,
      from: from,
      to: to,
    );

    result.fold(
      (failure) {
        _isLoadingMore = false;
        if (failure.type == ErrorType.noInternet) {
          state = TripsError('no_internet');
        } else {
          state = TripsError(failure.message);
        }
      },
      (tripHistory) {
        _handleSuccessfulFetch(tripHistory, page);
      },
    );

    return result;
  }

  /// Load more trips (for pagination)
  Future<void> loadMoreTrips() async {
    if (!_hasMoreData || _isLoadingMore) return;

    await fetchTrips(page: _currentPage + 1, status: _currentStatus);
  }

  /// Refresh trips list
  Future<void> refreshTrips({String? status}) async {
    await fetchTrips(page: 1, status: status, isRefresh: true);
  }

  /// Filter trips by status
  Future<void> filterTripsByStatus(String? status) async {
    await fetchTrips(page: 1, status: status, isRefresh: true);
  }

  /// Fetch trip details
  Future<Either<Failure, TripResponse>> fetchTripDetails({
    required String tripId,
  }) async {
    state = TripsLoading();

    final result = await tripUseCase.executeFetchTripDetails(tripId: tripId);

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = TripsError('no_internet');
        } else {
          state = TripsError(failure.message);
        }
      },
      (trip) {
        state = TripsSuccess(tripDetails: trip.trip, isSuccess: true);
      },
    );

    return result;
  }

  /// Get trips by specific status
  Future<void> getCompletedTrips() => filterTripsByStatus('completed');

  Future<void> getCancelledTrips() => filterTripsByStatus('cancelled');

  Future<void> getActiveTrips() => filterTripsByStatus('trip_started');

  Future<void> getAllTrips() => filterTripsByStatus(null);

  /// Search trips (if search functionality is needed)
  Future<void> searchTrips(String query) async {
    if (query.isEmpty) {
      await refreshTrips();
      return;
    }

    // Filter existing trips locally for quick search
    final filteredTrips = _allTrips.where((trip) {
      final pickup = trip.pickupLocation?.address?.toLowerCase() ?? '';
      final destination =
          trip.destinationLocation?.address?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return pickup.contains(searchQuery) || destination.contains(searchQuery);
    }).toList();

    if (filteredTrips.isNotEmpty) {
      state = TripsSuccess(trips: filteredTrips, isSuccess: true);
    } else {
      state = TripsError('No trips found matching your search');
    }
  }

  /// Private methods

  void _handleSuccessfulFetch(TripHistory tripHistory, int page) {
    _isLoadingMore = false;

    if (page == 1) {
      // First page or refresh
      _allTrips = tripHistory.data ?? [];
    } else {
      // Append new trips for pagination
      _allTrips.addAll(tripHistory.data ?? []);
    }

    // Update pagination state
    _currentPage = page;
    _hasMoreData =
        (tripHistory.data?.length ?? 0) >= 10; // Assuming limit of 10

    // Update UI state
    state = TripsSuccess(trips: _allTrips, isSuccess: true);
  }

  void _resetPagination() {
    _allTrips.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _isLoadingMore = false;
  }

  /// Clear state and reset
  void clearTrips() {
    _resetPagination();
    _currentStatus = null;
    state = TripsInitial();
  }

  /// Get trip by ID from cached trips
  Trip? getTripById(String tripId) {
    try {
      return _allTrips.firstWhere((trip) => trip.id == tripId);
    } catch (e) {
      return null;
    }
  }

  /// Update trip in the list (after actions like rating, etc.)
  void updateTripInList(Trip updatedTrip) {
    final index = _allTrips.indexWhere((trip) => trip.id == updatedTrip.id);
    if (index != -1) {
      _allTrips[index] = updatedTrip;
      state = TripsSuccess(trips: _allTrips, isSuccess: true);
    }
  }

  /// Remove trip from list (after cancellation, etc.)
  void removeTripFromList(String tripId) {
    _allTrips.removeWhere((trip) => trip.id == tripId);
    state = TripsSuccess(trips: _allTrips, isSuccess: true);
  }
}
