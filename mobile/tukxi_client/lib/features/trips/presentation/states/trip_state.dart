import 'package:tukxi/features/trips/data/models/trip_history.dart';

abstract class TripsState {}

class TripsInitial extends TripsState {}

class TripsLoading extends TripsState {}

class TripsSuccess extends TripsState {
  final List<Trip>? trips;
  final Trip? tripDetails;
  final bool isSuccess;

  TripsSuccess({this.trips, this.isSuccess = false, this.tripDetails});
}

class TripsError extends TripsState {
  final String message;
  TripsError(this.message);
}
