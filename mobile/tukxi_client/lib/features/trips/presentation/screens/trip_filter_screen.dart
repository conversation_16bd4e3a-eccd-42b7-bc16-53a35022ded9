import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/enums/enum_mappers.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:go_router/go_router.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/features/trips/data/models/trip_filter.dart';
import 'package:tukxi/features/trips/presentation/widgets/date_input_field.dart';

class TripFilterScreen extends StatefulWidget {
  const TripFilterScreen({
    super.key,
    required this.onFilterSelected,
    this.fromDate,
    this.toDate,
    this.selectedFilter,
  });

  final TripFilter? selectedFilter;
  final DateTime? fromDate;
  final DateTime? toDate;
  final void Function(TripFilter? filter, DateTime? fromDate, DateTime? toDate)?
  onFilterSelected;

  @override
  State<TripFilterScreen> createState() => _TripFilterScreenState();
}

class _TripFilterScreenState extends State<TripFilterScreen> {
  TripFilter? _selectedFilter;
  bool _isLoading = false;
  DateTime? _fromDate;
  DateTime? _toDate;

  @override
  void initState() {
    super.initState();

    _selectedFilter = widget.selectedFilter ?? tripFilters.first;
    _fromDate = widget.fromDate?.startOfDay;
    _toDate = widget.toDate?.endOfDay;

    debugPrint(_fromDate.toString());
    debugPrint(_toDate.toString());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      resizeToAvoidBottomInset: true,
      body: Align(
        alignment: Alignment.bottomCenter,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 15,
                  vertical: 15,
                ),
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.black10),
                  color: Colors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(context),

                    const SizedBox(height: 20),
                    _buildStatusSection(),

                    const SizedBox(height: 20),
                    _buildDateSection(context),
                    const SizedBox(height: 24),
                    _buildBottomButtons(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDateSection(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Date',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              // From Date
              DateInputField(
                label: 'From',
                date: _fromDate,
                onDateSelected: (selectedDate) {
                  //TODO: add date range validation
                  setState(() {
                    _fromDate = selectedDate.startOfDay;
                  });
                },
              ),
              const SizedBox(width: 16),
              // To Date
              DateInputField(
                label: 'To',
                date: _toDate,
                onDateSelected: (selectedDate) {
                  //TODO: add date range validation
                  setState(() {
                    _toDate = selectedDate.endOfDay;
                  });
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusSection() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Status',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: tripFilters.map((filter) {
              return _buildStatusChip(filter, filter == _selectedFilter);
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(TripFilter filter, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = filter;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primary : AppColors.black10,
            width: isSelected ? 1.5 : 0.5,
          ),
        ),
        child: Text(
          filter.filter,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isSelected ? AppColors.primary : Colors.grey[700],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            'Filter By',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
          ),
        ),
        IconButton(
          onPressed: () => context.pop(),
          icon: SizedBox(
            height: 30,
            width: 30,
            child: Image.asset(AssetPaths.popupClose),
          ),
          alignment: Alignment.centerRight,
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),
      ],
    );
  }

  Widget _buildBottomButtons() {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: BorderSide(color: AppColors.primary, width: 1),
              padding: const EdgeInsets.symmetric(vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              setState(() {
                _isLoading = true;
              });
              widget.onFilterSelected?.call(tripFilters.first, null, null);
              setState(() {
                _isLoading = false;
              });
              context.pop();
            },
            child: Text(
              'Clear',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                height: 1,
              ),
            ),
          ),
        ),
        const SizedBox(width: 20),
        Expanded(
          child: LoadingButton(
            onPressed: () {
              if (!_validateDateRange()) {
                return;
              }
              setState(() {
                _isLoading = true;
              });
              widget.onFilterSelected?.call(
                _selectedFilter,
                _fromDate,
                _toDate,
              );
              setState(() {
                _isLoading = false;
              });
              context.pop();
            },
            text: 'Apply',
            isLoading: _isLoading,
          ),
        ),
      ],
    );
  }

  bool _validateDateRange() {
    if (_fromDate != null && _toDate != null) {
      if (_toDate!.isBefore(_fromDate!)) {
        // return 'To date should be after from date';
        SnackbarUtils.showSnackBar(
          context: context,
          message: 'To Date must be after From Date.',
          type: SnackBarType.error,
        );
        return false;
      }
    }
    return true;
  }
}
