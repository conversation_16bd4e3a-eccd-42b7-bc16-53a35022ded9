import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/remote_image_widget.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';

class TripCard extends StatelessWidget {
  const TripCard({super.key, required this.tripData, this.onRebook});

  final Trip tripData;
  final void Function()? onRebook;

  @override
  Widget build(BuildContext context) {
    final status = tripData.status ?? RideStatusType.requested;
    final isCompleted = status == RideStatusType.completed;

    return Padding(
      padding: const EdgeInsets.only(left: 16, right: 16, top: 20),
      child: Row(
        children: [
          // Vehicle Image
          Container(
            width: 70,
            height: 70,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.greyBg,
              borderRadius: BorderRadius.circular(16),
            ),
            child: RemoteImageWidget(
              imageUrl: tripData.product?.icon,
              size: 50,
              fit: BoxFit.contain,
              borderRadius: 0,
            ),
          ),

          SizedBox(width: 16),

          // Trip Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tripData.destinationLocation?.address ?? '',
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                SizedBox(height: 2),

                Text(
                  tripData.createdAt?.toddMMdotTimeString ?? '',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.black50,
                  ),
                ),

                SizedBox(height: 2),

                //TODO: Pricing
                Text(
                  '₹ 0.00',
                  // '${tripData.formattedAmount} ',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: AppColors.black50,
                  ),
                ),

                SizedBox(height: 2),
                Text(
                  status.name.toCapitalize(),
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: status.color,
                  ),
                ),
              ],
            ),
          ),

          if (isCompleted) _buildRebook(),
        ],
      ),
    );
  }

  Widget _buildRebook() {
    return Row(
      children: [
        SizedBox(width: 12),
        SizedBox(
          height: 30,
          child: FilledButton(
            onPressed: onRebook,
            style: FilledButton.styleFrom(
              backgroundColor: AppColors.greyBg,
              foregroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 0),
            ),
            child: Row(
              children: [
                Image.asset(AssetPaths.rebook, width: 12),
                SizedBox(width: 4),

                Text(
                  'Rebook',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
