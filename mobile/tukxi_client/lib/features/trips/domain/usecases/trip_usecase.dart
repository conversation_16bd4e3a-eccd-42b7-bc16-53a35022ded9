import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';
import 'package:tukxi/features/trips/domain/repository/trips_repository.dart';

class TripUseCase {
  final TripsRepository repository;

  TripUseCase({required this.repository});

  /// Fetch trip history with pagination and optional status filter
  Future<Either<Failure, TripHistory>> executeFetchTrips({
    int page = 1,
    int limit = 10,
    String? status,
    DateTime? from,
    DateTime? to,
  }) => repository.fetchTrips(
    page: page,
    limit: limit,
    status: status,
    from: from,
    to: to,
  );

  /// Fetch detailed information for a specific trip
  Future<Either<Failure, TripResponse>> executeFetchTripDetails({
    required String tripId,
  }) => repository.fetchTripDetails(tripId: tripId);
}
