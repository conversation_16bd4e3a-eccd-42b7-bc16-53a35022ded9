import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';

abstract class TripsRepository {
  /// Fetch paginated trip history with optional status filter
  Future<Either<Failure, TripHistory>> fetchTrips({
    int page,
    int limit,
    String? status,
    DateTime? from,
    DateTime? to,
  });

  /// Fetch detailed information for a specific trip
  Future<Either<Failure, TripResponse>> fetchTripDetails({
    required String tripId,
  });
}
