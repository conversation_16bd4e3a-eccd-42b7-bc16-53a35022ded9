import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/features/trips/data/data_sources/trip_remote_data_source.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';
import 'package:tukxi/features/trips/domain/repository/trips_repository.dart';

class TripsRepositoryImpl implements TripsRepository {
  TripsRepositoryImpl({required this.tripRemoteDataSource});

  final TripRemoteDataSource tripRemoteDataSource;

  @override
  Future<Either<Failure, TripHistory>> fetchTrips({
    int page = 1,
    int limit = 10,
    String? status,
    DateTime? from,
    DateTime? to,
  }) async {
    return handleApiCall(
      () => tripRemoteDataSource.fetchTrips(
        page: page,
        limit: limit,
        status: status,
        from: from,
        to: to,
      ),
      apiErrorMessage: 'Failed to fetch trip history.',
    );
  }

  @override
  Future<Either<Failure, TripResponse>> fetchTripDetails({
    required String tripId,
  }) async {
    return handleApiCall(
      () => tripRemoteDataSource.fetchTripDetails(tripId: tripId),
      apiErrorMessage: 'Failed to fetch trip details.',
    );
  }
}
