import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/features/booking/data/models/driver.dart';
import 'package:tukxi/features/booking/data/models/product.dart';
import 'package:tukxi/features/booking/data/models/ride.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';
import 'package:tukxi/features/trips/domain/entity/trip_history_entity.dart';

class TripHistory extends TripHistoryEntity {
  const TripHistory({
    super.success,
    super.message,
    super.data,
    super.meta,
    super.timestamp,
  });

  factory TripHistory.fromJson(Map<String, dynamic> json) {
    return TripHistory(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: (json['data'] as List<dynamic>)
          .map((e) => Trip.fromJson(e))
          .toList(),
      meta: TripMeta.fromJson(json['meta']),
      timestamp: json['timestamp'] as int,
    );
  }
}

class TripResponse extends TripResponseEntity {
  const TripResponse({
    required super.success,
    required super.message,
    required super.trip,
    required super.timestamp,
  });

  factory TripResponse.fromJson(Map<String, dynamic> json) {
    return TripResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      trip: json['data'] == null ? null : Trip.fromJson(json['data']),
      timestamp: json['timestamp'] as int,
    );
  }
}

class Trip extends TripEntity {
  const Trip({
    required super.id,
    super.status,
    super.rating,
    super.pickupLocation,
    super.destinationLocation,
    super.stops,
    super.createdAt,
    super.completedAt,
    super.product,
    super.driver,
    super.driverVehicle,
  });

  factory Trip.fromJson(Map<String, dynamic> json) {
    return Trip(
      id: json['id'] as String,
      status: json['status'] == null
          ? RideStatusType.requested
          : RideStatusType.fromString(json['status'] as String),
      rating: json['rating'] != null
          ? RideRating.fromJson(json['rating'])
          : null,
      pickupLocation: json['pickupLocation'] != null
          ? LocationResponse.fromJson(json['pickupLocation'])
          : null,
      destinationLocation: json['destinationLocation'] != null
          ? LocationResponse.fromJson(json['destinationLocation'])
          : null,
      stops: json['stops'] == null
          ? []
          : (json['stops'] as List<dynamic>?)
                ?.map((e) => LocationResponse.fromJson(e))
                .toList(),
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'])
          : null,
      product: json['product'] != null
          ? Product.fromJson(json['product'])
          : null,
      driver: json['driver'] != null ? Driver.fromJson(json['driver']) : null,
      driverVehicle: json['driverVehicle'] != null
          ? DriverVehicle.fromJson(json['driverVehicle'])
          : null,
    );
  }
}

class TripMeta extends TripMetaEntity {
  const TripMeta({
    required super.page,
    required super.limit,
    required super.total,
    required super.totalPages,
  });

  factory TripMeta.fromJson(Map<String, dynamic> json) {
    return TripMeta(
      page: json['page'] as int? ?? 1,
      limit: json['limit'] as int? ?? 10,
      total: json['total'] as int? ?? 0,
      totalPages: json['totalPages'] as int? ?? 0,
    );
  }
}

class RideRating extends RideRatingEntity {
  const RideRating({
    super.id,
    super.rideId,
    super.driverId,
    super.riderId,
    super.reviewById,
    super.rating,
    super.review,
    super.createdAt,
    super.updatedAt,
    super.deletedAt,
  });

  factory RideRating.fromJson(Map<String, dynamic> json) {
    return RideRating(
      id: json['id'] as String?,
      rideId: json['rideId'] as String?,
      driverId: json['driverId'] as String?,
      riderId: json['riderId'] as String?,
      reviewById: json['reviewById'] as String?,
      rating: json['rating'] as String?,
      review: json['review'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : null,
      deletedAt: json['deletedAt'] != null
          ? DateTime.parse(json['deletedAt'])
          : null,
    );
  }
}
