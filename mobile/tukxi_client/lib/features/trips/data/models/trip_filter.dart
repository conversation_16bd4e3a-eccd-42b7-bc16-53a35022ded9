import 'package:flutter/widgets.dart';

class TripFilter {
  String filter;
  String? status;
  Icon image;

  TripFilter({required this.filter, required this.status, required this.image});

  /// ✅ Override equality operator
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TripFilter &&
        other.filter == filter &&
        other.status == status &&
        _iconsAreEqual(other.image, image);
  }

  /// ✅ Override hashCode
  @override
  int get hashCode {
    return Object.hash(filter, status, _getIconHashCode(image));
  }

  /// ✅ Helper method to compare Icon widgets
  bool _iconsAreEqual(Icon icon1, Icon icon2) {
    return icon1.icon == icon2.icon &&
        icon1.color == icon2.color &&
        icon1.size == icon2.size;
  }

  /// ✅ Helper method to get Icon hashCode
  int _getIconHashCode(Icon icon) {
    return Object.hash(icon.icon, icon.color, icon.size);
  }

  /// ✅ Override toString for debugging
  @override
  String toString() {
    return 'TripFilter(filter: $filter, status: $status, icon: ${image.icon})';
  }

  /// ✅ Copy method for creating modified instances
  TripFilter copyWith({String? filter, String? status, Icon? image}) {
    return TripFilter(
      filter: filter ?? this.filter,
      status: status ?? this.status,
      image: image ?? this.image,
    );
  }
}
