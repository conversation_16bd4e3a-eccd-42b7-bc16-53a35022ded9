import 'package:flutter/material.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/network/api_service.dart';
import 'package:tukxi/features/trips/data/data_sources/trip_remote_data_source.dart';
import 'package:tukxi/features/trips/data/models/trip_history.dart';

class TripRemoteDataSourceImpl implements TripRemoteDataSource {
  final _apiService = ApiService();

  ///MARK: - Fetch Trip History
  @override
  Future<TripHistory> fetchTrips({
    int page = 1,
    int limit = 10,
    String? status,
    DateTime? from,
    DateTime? to,
  }) async {
    try {
      Map<String, dynamic> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (status != null && status.isNotEmpty) {
        queryParams['status'] = status;
      }

      if (from != null) {
        queryParams['fromDate'] = from.toIso8601String();
      }
      if (to != null) {
        queryParams['toDate'] = to.toIso8601String();
      }

      debugPrint(
        '📡 Fetching trips - Page: $page, Limit: $limit, Status: $status',
      );

      final response = await _apiService.get(
        Endpoint.rideHistory.value,
        (json) => TripHistory.fromJson(json),
        params: queryParams,
      );

      debugPrint('✅ Trip history fetched successfully');
      return response;
    } catch (error) {
      debugPrint('❌ Error fetching trips: $error');
      rethrow;
    }
  }

  ///MARK: - Fetch Trip Details
  @override
  Future<TripResponse> fetchTripDetails({required String tripId}) async {
    try {
      debugPrint('📡 Fetching trip details for ID: $tripId');

      final response = await _apiService.get(
        '${Endpoint.ride.value}/$tripId/details/rider',
        (json) => TripResponse.fromJson(json),
      );

      debugPrint('✅ Trip details fetched successfully');
      return response;
    } catch (error) {
      debugPrint('❌ Error fetching trip details: $error');
      rethrow;
    }
  }
}
