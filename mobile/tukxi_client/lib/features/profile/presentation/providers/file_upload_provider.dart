import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/profile/data/data_sources/profile_remote_data_source_impl.dart';
import 'package:tukxi/features/profile/data/models/file_upload_response.dart';
import 'package:tukxi/features/profile/data/repositories/profile_repository_impl.dart';
import 'package:tukxi/features/profile/domain/usecases/file_upload_usecase.dart';
import 'package:tukxi/features/profile/presentation/state/file_upload_state.dart';

final fileUploadProvider =
    StateNotifierProvider<FileUploadNotifier, FileUploadState>((ref) {
      final dataSource = ProfileRemoteDataSourceImpl();
      final repository = ProfileRepositoryImpl(remoteDataSource: dataSource);
      final useCase = FileUploadUseCase(repository: repository);
      return FileUploadNotifier(useCase);
    });

class FileUploadNotifier extends StateNotifier<FileUploadState> {
  FileUploadNotifier(this.useCase) : super(FileUploadInitial());

  final FileUploadUseCase useCase;

  Future<Either<Failure, FileUploadResponse>> uploadProfileFile({
    required File file,
  }) async {
    state = FileUploadLoading();
    final result = await useCase.uploadProfileFile(file: file);
    result.fold(
      (failure) {
        state = FileUploadError(failure.message);
      },
      (response) {
        state = FileUploadSuccess(response.data?.url ?? '');
      },
    );
    return result;
  }
}
