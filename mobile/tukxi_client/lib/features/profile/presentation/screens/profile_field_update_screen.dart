import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/constants/date_constants.dart';
import 'package:tukxi/core/constants/ui_consants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/extensions/date_extensions.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/formatters/input_formatters.dart';
import 'package:tukxi/core/notifiers/notifiers.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/widgets/custom_textfield.dart';
import 'package:tukxi/core/widgets/field_mandatory_title.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/auth/domain/params/auth_screen_params.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';
import 'package:tukxi/features/profile/presentation/widgets/gender_selector.dart';
import 'package:tukxi/routes/app_routes.dart';

class ProfileFieldUpdateScreen extends ConsumerStatefulWidget {
  const ProfileFieldUpdateScreen({
    super.key,
    required this.field,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.gender,
    this.dateOfBirth,
  });

  final ProfileEditableField field;
  final String firstName;
  final String lastName;
  final String email;
  final Gender? gender;
  final String? dateOfBirth;

  @override
  ConsumerState<ProfileFieldUpdateScreen> createState() =>
      _NameUpdateScreenState();
}

class _NameUpdateScreenState extends ConsumerState<ProfileFieldUpdateScreen> {
  final TextEditingController _firstNameController = TextEditingController();
  final TextEditingController _lastNameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();

  final _formKey = GlobalKey<FormState>();
  final yesterday = DateTime.now().yesterday;

  String? _errorMessage;
  Gender? _selectedGender;
  DateTime? _dob;

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _firstNameController.text = widget.firstName;
    _lastNameController.text = widget.lastName;
    _emailController.text = widget.email;
    _selectedGender = widget.gender;
    if (widget.dateOfBirth != null && widget.dateOfBirth!.isNotEmpty) {
      _dob = widget.dateOfBirth!.toDateTime(format: DateConstants.dobFormat);
    } else {
      _dob = yesterday;
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(authProvider);
    final padding = 24.0;
    final totalPadding = padding * 2;

    return Scaffold(
      appBar: TitleBarWithBackButton(
        title: '',
        onBackPressed: () {
          _resetEditField();
          context.pop();
        },
      ),
      body: SafeArea(
        bottom: true,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: padding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle(),

              Expanded(
                child: Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      widget.field == ProfileEditableField.fullName
                          ? _buildNameField()
                          : widget.field == ProfileEditableField.gender
                          ? GenderSelector(
                              selectedGender: _selectedGender,
                              padding: totalPadding,
                              errorMessage: _errorMessage,
                              onChanged: (gender) {
                                setState(() {
                                  _selectedGender = gender;
                                  _errorMessage = null;
                                });
                              },
                            )
                          : widget.field == ProfileEditableField.dateOfBirth
                          ? _buildDatePicker()
                          : _buildEmailField(),
                    ],
                  ),
                ),
              ),

              LoadingButton(
                isLoading: state is AuthLoading,
                onPressed: _onSave,
                text: 'Save changes',
              ),

              if (Platform.isAndroid) const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  //MARK: - Widgets

  Widget _buildNameField() {
    return Column(
      children: [
        _buildFirstNameField(),
        const SizedBox(height: 8),
        _buildLastNameField(),
      ],
    );
  }

  Widget _buildDatePicker() {
    final DateTime initialDate = _dob != null && _dob!.isBefore(yesterday)
        ? _dob!
        : yesterday;
    final DateTime firstDate = DateTime(
      yesterday.year - 100,
      yesterday.month,
      yesterday.day,
    );
    final DateTime lastDate = yesterday;

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.black10),
            borderRadius: BorderRadius.circular(16),
          ),
          child: CalendarDatePicker(
            initialDate: initialDate,
            firstDate: firstDate,
            lastDate: lastDate,
            onDateChanged: (selectedDate) {
              setState(() {
                _dob = selectedDate;
              });
            },
          ),
        ),
        if (_errorMessage != null) ...[
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              _errorMessage!,
              style: GoogleFonts.inter(
                fontSize: 12,
                color: AppColors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          widget.field.title,
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          widget.field.hintText,
          style: GoogleFonts.inter(
            fontSize: 14,
            color: Colors.black54,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildEmailField() {
    return CustomFormTextField(
      hint: FieldMandatoryHint(title: 'Email Address'),
      label: FieldMandatoryHint(title: 'Email Address'),
      controller: _emailController,
      autocorrect: false,
      keyboardType: TextInputType.emailAddress,
      inputFormatters: [InputFormatters.emailFormatter()],
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter your email address';
        } else if (!value.isValidEmail) {
          return 'Please enter a valid email address';
        }
        return null;
      },
    );
  }

  Widget _buildFirstNameField() {
    return CustomFormTextField(
      hint: FieldMandatoryHint(title: 'First Name'),
      label: FieldMandatoryHint(title: 'First Name'),
      controller: _firstNameController,
      autocorrect: false,
      textCapitalization: TextCapitalization.words,
      inputFormatters: [
        InputFormatters.capitalizeFirstLetter(),
        InputFormatters.englishWithSpaceFormatter(),
      ],
      maxLength: UIConstants.kMaxNameLength,
      keyboardType: TextInputType.name,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter your first name';
        } else if (!value.isEnglishWithSpaces) {
          return AppConstants.englishWithSpaceAllowed;
        } else if (value.length < UIConstants.kMinFirstNameLength) {
          return 'Minimum ${UIConstants.kMinFirstNameLength} characters required';
        }
        return null;
      },
    );
  }

  Widget _buildLastNameField() {
    return CustomFormTextField(
      hint: FieldMandatoryHint(title: 'Last Name'),
      label: FieldMandatoryHint(title: 'Last Name'),
      autocorrect: false,
      controller: _lastNameController,
      textCapitalization: TextCapitalization.words,
      inputFormatters: [
        InputFormatters.capitalizeFirstLetter(),
        InputFormatters.englishWithSpaceFormatter(),
      ],

      keyboardType: TextInputType.name,
      maxLength: UIConstants.kMaxNameLength,
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'Please enter your last name';
        } else if (!value.isEnglishWithSpaces) {
          return AppConstants.englishWithSpaceAllowed;
        } else if (value.length < UIConstants.kMinLastNameLength) {
          return 'Minimum ${UIConstants.kMinLastNameLength} characters required';
        }
        return null;
      },
    );
  }

  //MARK: - Button actions
  void _onSave() {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState?.save();

    _updateUserProfile();
  }

  ///MARK: - Methods
  String fullName(String firstName, String lastName) {
    if (firstName.isEmpty && lastName.isEmpty) {
      return '';
    } else if (firstName.isEmpty) {
      return lastName.trim();
    } else if (lastName.isEmpty) {
      return firstName.trim();
    } else {
      return '$firstName $lastName'.trim();
    }
  }

  void _resetEditField() {
    resetProfileEditFieldNotifier.value = false;
    resetProfileEditFieldNotifier.value = true;
  }

  //MARK: - API call

  Future _updateUserProfile() async {
    if (!mounted) return;

    final firstName = _firstNameController.text.trim();
    final lastName = _lastNameController.text.trim();
    final email = _emailController.text.trim();
    final previousDate = widget.dateOfBirth?.toDateTime(
      format: DateConstants.dobFormat,
    );

    if (_selectedGender == null) {
      setState(() {
        _errorMessage = 'Please select your gender';
      });
      return;
    }

    if (_dob == null) {
      if (widget.field == ProfileEditableField.dateOfBirth) {
        setState(() {
          _errorMessage = 'Please select your date of birth';
        });
        return;
      }
    }

    if (firstName == widget.firstName &&
        lastName == widget.lastName &&
        email == widget.email &&
        _selectedGender == widget.gender &&
        _dob == previousDate) {
      _resetEditField();
      context.pop();
      return;
    }

    final state = ref.read(authProvider);

    if (state is AuthLoading) return;
    final result = await ref
        .read(authProvider.notifier)
        .updateUserProfile(
          firstName: firstName,
          lastName: lastName,
          email: email,
          gender: _selectedGender?.value,
          dob: _dob?.toDateString(outputFormat: 'yyyy-MM-dd'),
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _updateUserProfile(),
        );
      },
      (response) {
        if (!mounted) return;
        profileReloadNotifier.value = true;
        _resetEditField();

        if (widget.field == ProfileEditableField.email) {
          context.push(
            AppRoutes.otp,
            extra: AuthScreenParams(
              email: email,
              authType: AuthType.email,
              isFromProfileEmailUpdate: true,
            ),
          );
        } else {
          accountReloadNotifier.value = true;
          context.pop();
        }
      },
    );
  }
}
