import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/routes/app_routes.dart';

class SecurityPinScreen extends StatefulWidget {
  const SecurityPinScreen({super.key, required this.user});

  final AuthUser user;

  @override
  State<SecurityPinScreen> createState() => _SecurityPinScreenState();
}

class _SecurityPinScreenState extends State<SecurityPinScreen> {
  String securityPin = '0000';

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted || widget.user.rideOtp == null) return;

      setState(() {
        securityPin = widget.user.rideOtp!;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: TitleBarWithBackButton(
        title: '',
        trailing: _buildChangePinButton(),
        onBackPressed: () => context.pop(),
      ),

      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),

            _buildTitle(),
            const SizedBox(height: 40),

            _builPinSection(),
          ],
        ),
      ),
    );
  }

  ///MARK: - Widgets
  Widget _buildTitle() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Security PIN',
          style: GoogleFonts.inter(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),

        const SizedBox(height: 12),

        Text(
          AppConstants.managePinWarning,
          style: GoogleFonts.inter(
            fontSize: 14,
            color: AppColors.black50,
            fontWeight: FontWeight.w500,
            height: 22 / 14,
          ),
        ),
      ],
    );
  }

  Widget _builPinSection() {
    const double boxWidth = 56;
    const double boxSpacing = 20;
    final int pinLength = securityPin.length;
    final double pinRowWidth =
        (boxWidth * pinLength) + (boxSpacing * (pinLength - 1));

    return Center(
      child: SizedBox(
        width: pinRowWidth,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSecurityPin(
              boxWidth: boxWidth,
              boxSpacing: boxSpacing,
              pinLength: pinLength,
            ),

            const SizedBox(height: 24),
            Text(
              AppConstants.pinShareWarning,
              style: GoogleFonts.inter(
                fontSize: 10,
                color: AppColors.black50,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityPin({
    required double boxWidth,
    required double boxSpacing,
    required int pinLength,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(securityPin.length, (index) {
        final pinDigit = securityPin[index];
        return _buildPinDigitBox(
          pinDigit,
          index,
          boxWidth: boxWidth,
          boxSpacing: boxSpacing,
          pinLength: pinLength,
        );
      }),
    );
  }

  Widget _buildChangePinButton() {
    return Container(
      padding: const EdgeInsets.only(right: 20.0),
      height: 30,
      child: OutlinedButton(
        style: OutlinedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          side: const BorderSide(color: AppColors.black10, width: 0.5),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        ),
        onPressed: () {
          context.push(AppRoutes.updatePin, extra: {'user': widget.user});
        },
        child: Text(
          'Change PIN',
          style: GoogleFonts.inter(
            fontWeight: FontWeight.w500,
            fontSize: 10,
            color: Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildPinDigitBox(
    String digit,
    int index, {
    required double boxWidth,
    required double boxSpacing,
    required int pinLength,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: boxWidth,
          width: boxWidth,
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: AppColors.greyBg,
            borderRadius: BorderRadius.circular(12),
          ),
          alignment: Alignment.center,
          child: Text(
            digit,
            style: GoogleFonts.inter(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
        ),
        if (index < pinLength - 1) SizedBox(width: boxSpacing),
      ],
    );
  }
}
