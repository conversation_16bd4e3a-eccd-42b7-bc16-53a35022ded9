import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/asset_paths.dart';
import 'package:tukxi/core/constants/date_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/extensions/string_extensions.dart';
import 'package:tukxi/core/notifiers/notifiers.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/image_picker_utils.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/circular_image_widget.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';
import 'package:tukxi/features/profile/presentation/providers/file_upload_provider.dart';
import 'package:tukxi/routes/app_routes.dart';

class AccountDetailsScreen extends ConsumerStatefulWidget {
  const AccountDetailsScreen({super.key});

  @override
  ConsumerState<AccountDetailsScreen> createState() =>
      _AccountDetailsScreenState();
}

class _AccountDetailsScreenState extends ConsumerState<AccountDetailsScreen> {
  File? _selectedImage;
  ProfileEditableField? _editingField;
  String _dateString = '';
  Gender? _selectedGender;
  String? _profileImageUrl;
  String _firstName = '';
  String _lastName = '';
  String _phoneNumber = '';
  String _email = '';
  bool _isEmailVerified = false;
  bool _isFirstLoad = true;

  static const _enterEmailText = 'Enter email address';
  static const _selectedGenderText = 'Select Gender';
  static const _dateOfBirthText = DateConstants.dobFormat;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;
      await _fetchUserProfile();
    });
    resetProfileEditFieldNotifier.addListener(_onResetEditingField);
    accountReloadNotifier.addListener(_onProfileReload);
  }

  @override
  void dispose() {
    resetProfileEditFieldNotifier.removeListener(_onResetEditingField);
    accountReloadNotifier.removeListener(_onProfileReload);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(authProvider);
    return Stack(
      children: [
        Scaffold(
          appBar: TitleBarWithBackButton(
            title: 'Profile',
            titleStyle: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            onBackPressed: () {
              context.pop();
            },
          ),
          body: SafeArea(
            bottom: Platform.isAndroid,
            child: (state is AuthLoading && _isFirstLoad)
                ? ShimmerLoaders.accountDetailsSimmer()
                : Column(
                    children: [
                      const Divider(
                        thickness: 1,
                        height: 1,
                        color: AppColors.black10,
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 20),
                          child: SingleChildScrollView(
                            child: Column(
                              children: [
                                const SizedBox(height: 25),
                                _buildProfileImage(),
                                const SizedBox(height: 20),
                                _buildPersonalInfo(),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        ),
        if (state is AuthLoading && !_isFirstLoad)
          Positioned.fill(
            child: Container(
              color: AppColors.black10,
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
      ],
    );
  }

  ///MARK: - Widgets
  Widget _buildProfileImage() {
    final profileIconsize = 128.0;
    final editButtonHeight = 34.0;
    final totalHeight = profileIconsize + (editButtonHeight / 2);
    final halfEditButtonHeight = editButtonHeight / 2;

    return SizedBox(
      height: totalHeight,
      width: 128,
      child: Stack(
        alignment: Alignment.center,
        children: [
          CircularImageWidget(
            size: 128,
            borderRadius: 64,
            imageFile: _selectedImage,
            imageUrl: _profileImageUrl,
          ),

          Positioned(
            bottom: 0,
            child: Container(
              height: editButtonHeight,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(color: AppColors.black10, width: 1),
                borderRadius: BorderRadius.circular(halfEditButtonHeight),
              ),
              child: TextButton(
                onPressed: _displayImagePicker,
                style: TextButton.styleFrom(padding: EdgeInsets.zero),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image.asset(AssetPaths.edit, width: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Edit',
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: GoogleFonts.inter(fontSize: 16, fontWeight: FontWeight.w600),
        ),

        const SizedBox(height: 20),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(color: AppColors.black10),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Form(
            child: Column(
              children: [
                _buildField(
                  label: 'Full name',
                  hint: 'Enter full name',
                  value: fullName(_firstName, _lastName),
                  field: ProfileEditableField.fullName,
                  isEditable: true,
                ),
                _buildField(
                  label: 'Mobile number',
                  hint: 'Enter mobile number',
                  value: _phoneNumber,
                  field: ProfileEditableField.mobileNumber,
                  isEditable: false,
                ),
                _buildField(
                  label: 'Date of Birth',
                  hint: 'Enter date of birth',
                  value: _dateString.isNotEmpty
                      ? _dateString
                      : _dateOfBirthText,
                  field: ProfileEditableField.dateOfBirth,
                  isEditable: true,
                ),
                _buildField(
                  label: 'Gender',
                  hint: 'Select gender',
                  value: _selectedGender?.value ?? _selectedGenderText,
                  field: ProfileEditableField.gender,
                  isEditable: true,
                ),

                _buildField(
                  label: 'Email address',
                  value: _email,
                  hint: 'Enter email address',
                  field: ProfileEditableField.email,
                  isEditable: _email == _enterEmailText,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildField({
    required String label,
    required String hint,
    required String value,
    required ProfileEditableField field,
    bool isEditable = false,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, right: 16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      label,
                      style: GoogleFonts.inter(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: AppColors.black50,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: GoogleFonts.inter(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color:
                            (field == ProfileEditableField.email &&
                                    _email == _enterEmailText ||
                                field == ProfileEditableField.gender &&
                                    value == _selectedGenderText ||
                                field == ProfileEditableField.dateOfBirth &&
                                    value == _dateOfBirthText)
                            ? AppColors.black50
                            : Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
              if (isEditable) _editButton(field),
              if (field == ProfileEditableField.email &&
                  !_isEmailVerified &&
                  _email.isNotEmpty &&
                  _email != _enterEmailText)
                _emailPendingwithArrow(),
            ],
          ),
        ),
        if (field != ProfileEditableField.email)
          const Divider(
            indent: 16,
            endIndent: 16,
            thickness: 1,
            height: 20,
            color: AppColors.black10,
          ),
      ],
    );
  }

  Widget _editButton(ProfileEditableField field) {
    return Container(
      color: Colors.white,
      width: 34,
      height: 34,
      child: IconButton(
        onPressed: () {
          setState(() {
            _editingField = _editingField == field ? null : field;
          });

          context.push(
            AppRoutes.profileFieldUpdate,
            extra: {
              'firstName': _firstName,
              'lastName': _lastName,
              'email': _email != _enterEmailText ? _email : '',
              'gender': _selectedGender?.value != _selectedGenderText
                  ? _selectedGender
                  : null,
              'dateOfBirth': _dateString != _dateOfBirthText
                  ? _dateString
                  : null,
              'field': _editingField,
            },
          );
        },
        icon: Image.asset(AssetPaths.editField, width: 25),
      ),
    );
  }

  Widget _emailPendingwithArrow() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.lightYellow,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Text(
            'Pending',
            style: GoogleFonts.inter(
              color: AppColors.yellow,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),

        SizedBox(
          width: 34,
          height: 34,
          child: IconButton(
            onPressed: () {},
            padding: EdgeInsets.zero,
            icon: Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: AppColors.black50,
            ),
          ),
        ),
      ],
    );
  }

  void _displayImagePicker() {
    ImagePickerUtils.showImagePicker(
      context: context,
      title: 'Choose an option to upload the Profile picture',
      onImageSelected: (pickedImage) async {
        final file = File(pickedImage.path);
        setState(() {
          _selectedImage = file;
        });
        _uploadProfileImage(file);
      },
    );
  }

  //MARK: - API call
  Future _uploadProfileImage(File file) async {
    await _handleApiCall(
      () => ref.read(fileUploadProvider.notifier).uploadProfileFile(file: file),
      onSuccess: (response) {
        // Handle successful upload, e.g., update user profile with new image URL
        debugPrint('File uploaded successfully: ${response.data?.url ?? ''}');

        final profileImageUrl = response.data?.url ?? '';
        if (profileImageUrl.isNotEmpty) {
          _updateUserProfile(profileImageUrl: profileImageUrl);
        }
      },
    );
  }

  Future _updateUserProfile({String? profileImageUrl}) async {
    await _handleApiCall(
      () => ref
          .read(authProvider.notifier)
          .updateUserProfile(
            firstName: _firstName,
            lastName: _lastName,
            profilePictureUrl: profileImageUrl,
          ),
      onSuccess: (response) {
        setState(() {
          _profileImageUrl =
              response.profile?.profilePictureUrl ?? profileImageUrl;
          _firstName = response.profile?.firstName ?? _firstName;
          _lastName = response.profile?.lastName ?? _lastName;
          _editingField = null;
        });
        profileReloadNotifier.value = true;
        SnackbarUtils.showSnackBar(
          context: context,
          message: 'Profile updated successfully.',
          type: SnackBarType.success,
        );
      },
    );
  }

  Future _fetchUserProfile() async {
    await _handleApiCall(
      () => ref.read(authProvider.notifier).fetchUserDetails(),
      onSuccess: (response) {
        final firstName = response.firstName ?? '';
        final lastName = response.lastName ?? '';
        final dob = response.dob ?? '';
        final gender = response.gender ?? '';

        setState(() {
          _isFirstLoad = false;
          _profileImageUrl = response.profilePictureUrl ?? '';
          _firstName = firstName;
          _lastName = lastName;
          _phoneNumber = response.phone ?? '';
          _email = response.email ?? '';
          _isEmailVerified = response.emailVerified;
          _selectedGender = gender.isNotEmpty
              ? Gender.fromString(gender)
              : null;

          _dateString = dob.toDateString(outputFormat: DateConstants.dobFormat);

          if (_email.isEmpty && !_isEmailVerified) {
            _email = _enterEmailText;
          }
        });
      },
    );
  }

  Future<void> _handleApiCall<T>(
    Future<Either<Failure, T>> Function() apiCall, {
    Function(T response)? onSuccess,
    String? errorMessage,
  }) async {
    final result = await apiCall();
    if (!mounted) return;

    result.fold(
      (failure) {
        if (!mounted) return;
        handleApiError(
          context: context,
          failure: failure,
          errorMessage: errorMessage,
          onRetry: () async => _handleApiCall(apiCall, onSuccess: onSuccess),
        );
      },
      (response) {
        if (!mounted) return;
        onSuccess?.call(response);
      },
    );
  }

  ///MARK: - Methods

  String fullName(String firstName, String lastName) {
    if (firstName.isEmpty && lastName.isEmpty) {
      return '';
    } else if (firstName.isEmpty) {
      return lastName.trim();
    } else if (lastName.isEmpty) {
      return firstName.trim();
    } else {
      return '$firstName $lastName'.trim();
    }
  }

  void _onResetEditingField() {
    setState(() {
      _editingField = null;
    });
  }

  void _onProfileReload() {
    if (accountReloadNotifier.value) {
      _fetchUserProfile();
      accountReloadNotifier.value = false;
    }
  }
}
