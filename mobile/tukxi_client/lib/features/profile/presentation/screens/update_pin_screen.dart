import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/app_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/utils/api_helper.dart';
import 'package:tukxi/core/utils/device_utils.dart';
import 'package:tukxi/core/utils/snack_bar_utils.dart';
import 'package:tukxi/core/widgets/loading_button.dart';
import 'package:tukxi/core/widgets/title_bar.dart';
import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/auth/presentation/providers/auth_provider.dart';
import 'package:tukxi/features/auth/presentation/states/auth_state.dart';
import 'package:tukxi/features/auth/presentation/widgets/otp_input_field.dart';
import 'package:tukxi/routes/app_routes.dart';

class SecurityPinUpdateScreen extends ConsumerStatefulWidget {
  const SecurityPinUpdateScreen({super.key, required this.user});

  final AuthUser user;
  @override
  ConsumerState<SecurityPinUpdateScreen> createState() =>
      _SecurityPinUpdateScreenState();
}

class _SecurityPinUpdateScreenState
    extends ConsumerState<SecurityPinUpdateScreen> {
  String _newPin = '';
  String _confirmPin = '';
  bool _isTablet = false;
  List<TextEditingController?> _pinControllers = [];
  List<TextEditingController?> _confirmPinControllers = [];

  // bool _shouldAutoFocusConfirmPin = false;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      if (!mounted) return;

      final result = await DeviceUtils.isTablet(context);
      setState(() {
        _isTablet = result;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(authProvider);
    return Stack(
      children: [
        Scaffold(
          appBar: TitleBarWithBackButton(
            title: '',
            onBackPressed: () => context.pop(),
          ),
          body: SafeArea(
            bottom: true,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          Text(
                            'Update PIN',
                            style: GoogleFonts.inter(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                              height: 28 / 20,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            AppConstants.updatePinInstructions,
                            style: GoogleFonts.inter(
                              fontSize: 14,
                              color: AppColors.black50,
                              fontWeight: FontWeight.w500,
                              height: 22 / 14,
                            ),
                          ),
                          const SizedBox(height: 36),
                          Text(
                            'Enter new PIN',
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 16),

                          OtpInputField(
                            onControllersReady: (controllers) {
                              _pinControllers = controllers;
                            },
                            onOtpSubmitted: (value) {
                              setState(() {
                                _newPin = value.trim();
                                // _shouldAutoFocusConfirmPin = true;
                                _confirmPinControllers.clear();
                              });
                            },
                            onOtpChanged: (value) {
                              // setState(() {
                              //   _shouldAutoFocusConfirmPin = false;
                              // });
                            },
                            isTablet: _isTablet,
                            autoFocus: false,
                          ),

                          const SizedBox(height: 20),
                          Text(
                            'Confirm new PIN',
                            style: GoogleFonts.inter(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 16),
                          OtpInputField(
                            // key: ValueKey(_shouldAutoFocusConfirmPin),
                            onControllersReady: (controllers) {
                              _confirmPinControllers = controllers;
                            },
                            onOtpSubmitted: (value) {
                              setState(() {
                                _confirmPin = value.trim();
                              });
                            },
                            onOtpChanged: (value) {
                              debugPrint(value);
                            },
                            isTablet: _isTablet,
                            autoFocus: false, //_shouldAutoFocusConfirmPin,
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(
                    width: double.infinity,
                    height: 56,
                    child: LoadingButton(
                      isLoading: state is AuthLoading,
                      onPressed: _validateAndSubmitPin,
                      text: 'Update PIN',
                    ),
                  ),

                  if (Platform.isAndroid) const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
        if (state is AuthLoading)
          Positioned.fill(
            child: Container(
              color: Colors.black.withOpacity(0.1),
              child: const Center(child: CircularProgressIndicator()),
            ),
          ),
      ],
    );
  }

  void _validateAndSubmitPin() {
    var message = '';
    // Basic validation: 4 digits, match, not empty
    if (_newPin.isEmpty || _confirmPin.isEmpty) {
      message = 'Please enter and confirm your new PIN.';
    } else if (_newPin.length != 4 || _confirmPin.length != 4) {
      message = 'PIN must be 4 digits.';
    } else if (_newPin != _confirmPin) {
      message = 'PINs do not match.';
    }

    if (message.isNotEmpty) {
      SnackbarUtils.showSnackBar(
        context: context,
        message: message,
        type: SnackBarType.error,
      );
      return;
    }

    _updateUserProfile();
  }

  //MARK: - API call

  Future _updateUserProfile() async {
    if (!mounted) return;
    final state = ref.read(authProvider);

    if (state is AuthLoading) return;
    final result = await ref
        .read(authProvider.notifier)
        .updateUserProfile(
          firstName: widget.user.firstName ?? '',
          lastName: widget.user.lastName ?? '',
          securityPin: _newPin,
        );

    result.fold(
      (failure) {
        if (!mounted) return;

        handleApiError(
          context: context,
          failure: failure,
          onRetry: () async => _updateUserProfile(),
        );
      },
      (response) {
        if (!mounted) return;
        _pinControllers.clear();
        _confirmPinControllers.clear();
        context.push(AppRoutes.pinUpdateSuccess);
      },
    );
  }
}
