import 'package:tukxi/features/profile/data/models/file_upload_response.dart';

class FileUploadEntity {
  final bool? success;
  final String? message;
  final FileUploadData? data;

  FileUploadEntity({this.success, this.message, this.data});
}

class FileUploadDataEntity {
  final String? key;
  final String? url;
  final String? contentType;
  final int? size;

  FileUploadDataEntity({this.key, this.url, this.contentType, this.size});
}
