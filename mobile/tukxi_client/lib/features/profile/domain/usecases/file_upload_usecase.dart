import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/profile/data/models/file_upload_response.dart';
import 'package:tukxi/features/profile/domain/repositories/profile_repository.dart';

class FileUploadUseCase {
  final ProfileRepository repository;

  FileUploadUseCase({required this.repository});

  Future<Either<Failure, FileUploadResponse>> uploadProfileFile({
    required File file,
  }) => repository.uploadProfileFile(file: file);
}
