import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';

abstract class FavouriteRepository {
  Future<Either<Failure, List<FavouriteLocation>>> getFavouriteLocations();

  Future<Either<Failure, FavouriteLocation>> addFavouriteLocation({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  });

  Future<Either<Failure, FavouriteLocation>> editFavouriteLocation({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  });

  Future<Either<Failure, FavoriteSuccessResponse>> deleteFavouriteLocation({
    required String id,
  });
}
