import 'package:tukxi/features/auth/data/models/auth_user.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/data/models/location_response.dart';

class FavouriteLocationEntity {
  const FavouriteLocationEntity({
    this.id,
    this.userProfileId,
    this.name,
    this.description,
    this.location,
    this.meta,
    this.user,
    this.createdAt,
    this.updtaedat,
  });

  final String? id;
  final String? userProfileId;
  final String? name;
  final String? description;
  final LocationResponse? location;
  final LocationMetaResponse? meta;
  final AuthUser? user;
  final DateTime? createdAt;
  final DateTime? updtaedat;
}

class FavouriteLocationListEntity {
  const FavouriteLocationListEntity({
    this.data,
    this.total,
    this.page,
    this.limit,
  });
  final List<FavouriteLocation>? data;
  final int? total;
  final int? page;
  final int? limit;
}
