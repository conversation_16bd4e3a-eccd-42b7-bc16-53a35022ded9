// Edit favourite location
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/domain/repositories/favourite_location_repository.dart';

class EditFavouriteLocationUseCase {
  final FavouriteRepository repository;
  EditFavouriteLocationUseCase(this.repository);

  Future<Either<Failure, FavouriteLocation>> execute({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  }) {
    return repository.editFavouriteLocation(
      id: id,
      name: name,
      description: description,
      latitude: latitude,
      longitude: longitude,
      meta: meta,
    );
  }
}
