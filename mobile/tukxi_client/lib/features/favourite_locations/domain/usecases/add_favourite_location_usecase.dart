// Add a new favourite location
import 'package:dartz/dartz.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/domain/repositories/favourite_location_repository.dart';

class AddFavouriteLocationUseCase {
  final FavouriteRepository repository;
  AddFavouriteLocationUseCase(this.repository);

  Future<Either<Failure, FavouriteLocation>> execute({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  }) {
    return repository.addFavouriteLocation(
      name: name,
      latitude: latitude,
      longitude: longitude,
      meta: meta,
      description: description,
    );
  }
}
