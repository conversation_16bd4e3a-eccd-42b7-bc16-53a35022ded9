import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';

abstract class FavouriteLocationState {}

class FavouriteLocationInitial extends FavouriteLocationState {}

class FavouriteLocationLoading extends FavouriteLocationState {}

class FavouriteLocationSuccess extends FavouriteLocationState {
  final bool? success;
  final List<FavouriteLocation>? favouriteLocations;
  final FavouriteLocation? favouriteLocation;

  FavouriteLocationSuccess({
    this.success,
    this.favouriteLocations,
    this.favouriteLocation,
  });
}

class FavouriteLocationError extends FavouriteLocationState {
  final String message;
  FavouriteLocationError(this.message);
}
