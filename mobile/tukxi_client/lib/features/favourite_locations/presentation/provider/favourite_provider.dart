import 'package:dartz/dartz.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/features/favourite_locations/data/data_sources/favourite_locations_remote_data_source_impl.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location.dart';
import 'package:tukxi/features/favourite_locations/data/models/favourite_location_response.dart';
import 'package:tukxi/features/favourite_locations/data/repositories/favourite_location_repository.dart';
import 'package:tukxi/features/favourite_locations/domain/usecases/add_favourite_location_usecase.dart';
import 'package:tukxi/features/favourite_locations/domain/usecases/delete_favourite_location_usecase.dart';
import 'package:tukxi/features/favourite_locations/domain/usecases/edit_favourite_lcation_usecase.dart';
import 'package:tukxi/features/favourite_locations/domain/usecases/get_favourite_location_usecase.dart';
import 'package:tukxi/features/favourite_locations/presentation/states/favourite_location_state.dart';

final favouriteLocationProvider =
    StateNotifierProvider<FavouriteLocationNotifier, FavouriteLocationState>((
      ref,
    ) {
      final repository = FavouriteRepositoryImpl(
        remoteDataSource: FavouriteRemoteDataSourceImpl(),
      );

      return FavouriteLocationNotifier(
        getUseCase: GetFavouriteLocationsUseCase(repository),
        addUseCase: AddFavouriteLocationUseCase(repository),
        editUseCase: EditFavouriteLocationUseCase(repository),
        deleteUseCase: DeleteFavouriteLocationUseCase(repository),
      );
    });

class FavouriteLocationNotifier extends StateNotifier<FavouriteLocationState> {
  FavouriteLocationNotifier({
    required this.getUseCase,
    required this.addUseCase,
    required this.editUseCase,
    required this.deleteUseCase,
  }) : super(FavouriteLocationInitial());

  final GetFavouriteLocationsUseCase getUseCase;
  final AddFavouriteLocationUseCase addUseCase;
  final EditFavouriteLocationUseCase editUseCase;
  final DeleteFavouriteLocationUseCase deleteUseCase;

  /// Fetch all favourite locations
  Future<Either<Failure, List<FavouriteLocation>>> fetchFavourites() async {
    state = FavouriteLocationLoading();

    final result = await getUseCase.execute();

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = FavouriteLocationError('no_internet');
        } else {
          state = FavouriteLocationError(failure.message);
        }
      },
      (favourites) =>
          state = FavouriteLocationSuccess(favouriteLocations: favourites),
    );

    return result;
  }

  /// Add a new favourite location
  Future<Either<Failure, FavouriteLocation>> addFavourite({
    required String name,
    required double latitude,
    required double longitude,
    required Map<String, dynamic> meta,
    String? description,
  }) async {
    state = FavouriteLocationLoading();

    final result = await addUseCase.execute(
      name: name,
      latitude: latitude,
      longitude: longitude,
      meta: meta,
      description: description,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = FavouriteLocationError('no_internet');
        } else {
          state = FavouriteLocationError(failure.message);
        }
      },
      (favourite) {
        state = FavouriteLocationSuccess(favouriteLocation: favourite);
      },
    );

    return result;
  }

  /// Edit an existing favourite location
  Future<Either<Failure, FavouriteLocation>> editFavourite({
    required String id,
    String? name,
    String? description,
    double? latitude,
    double? longitude,
    Map<String, dynamic>? meta,
  }) async {
    state = FavouriteLocationLoading();

    final result = await editUseCase.execute(
      id: id,
      name: name,
      description: description,
      latitude: latitude,
      longitude: longitude,
      meta: meta,
    );

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = FavouriteLocationError('no_internet');
        } else {
          state = FavouriteLocationError(failure.message);
        }
      },
      (favourite) {
        state = FavouriteLocationSuccess(favouriteLocation: favourite);
      },
    );

    return result;
  }

  /// Delete a favourite location
  Future<Either<Failure, FavoriteSuccessResponse>> deleteFavourite({
    required String id,
  }) async {
    state = FavouriteLocationLoading();

    final result = await deleteUseCase.execute(id: id);

    result.fold(
      (failure) {
        if (failure.type == ErrorType.noInternet) {
          state = FavouriteLocationError('no_internet');
        } else {
          state = FavouriteLocationError(failure.message);
        }
      },
      (response) {
        state = FavouriteLocationSuccess(success: response.success);
      },
    );

    return result;
  }
}
