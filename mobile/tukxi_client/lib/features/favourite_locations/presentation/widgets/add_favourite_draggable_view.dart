import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:tukxi/core/constants/location_constants.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:tukxi/core/widgets/shimmer_widgets.dart';
import 'package:tukxi/features/home/<USER>/params/location_params.dart';

class AddFavouriteDraggableView extends StatelessWidget {
  const AddFavouriteDraggableView({
    super.key,
    required this.placedetails,
    required this.isLoading,
    required this.onConfirmTapped,
  });

  final VoidCallback onConfirmTapped;
  final LocationParams? placedetails;
  final bool isLoading;
  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(left: 15, right: 15),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(LocationConstants.mapCornerRadius),
            topRight: Radius.circular(LocationConstants.mapCornerRadius),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 15),
            if (isLoading)
              ShimmerLoaders.titleAndValueShimmer()
            else
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    placedetails?.locationName ?? '',
                    maxLines: 2,
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    maxLines: 2,
                    placedetails?.locationAddress ?? '',
                    style: GoogleFonts.inter(
                      color: AppColors.black50,
                      fontWeight: FontWeight.w400,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),

            const SizedBox(height: 15),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onConfirmTapped,
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                child: Text(
                  "Confirm Location",
                  style: GoogleFonts.inter(
                    fontWeight: FontWeight.w500,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
