import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:image_picker/image_picker.dart';
import 'package:tukxi/core/theme/app_colors.dart';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'package:path/path.dart' as path;

class ImagePickerUtils {
  static void showImagePicker({
    required BuildContext context,
    required Function(File selectedImage) onImageSelected,
    String? title,
  }) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext ctx) {
        return _displayImagePicker(
          title: title ?? 'Choose an option to add image',
          context: context,
          ctx: ctx,
          onImageSelected: onImageSelected,
        );
      },
    );
  }

  static Widget _displayImagePicker({
    required String title,
    required BuildContext context,
    required BuildContext ctx,
    required Function(File selectedImage) onImageSelected,
  }) {
    return SafeArea(
      bottom: true,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.only(top: 25, left: 20, right: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              textAlign: TextAlign.start,
              style: GoogleFonts.inter(
                color: AppColors.black75,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),

            Divider(
              endIndent: 0,
              indent: 0,
              height: 20,
              color: AppColors.black10,
            ),

            // Take a picture
            TextButton.icon(
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(double.infinity, 50),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                alignment: Alignment.centerLeft,
              ),
              onPressed: () {
                Navigator.pop(ctx);
                _chooseImage(context, true, onImageSelected);
              },
              icon: Icon(Icons.camera_alt, color: AppColors.black50),
              label: Text(
                'Take a picture',
                style: GoogleFonts.inter(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),

            Divider(
              endIndent: 20,
              indent: 20,
              height: 1,
              color: AppColors.black10,
            ),

            // Select from gallery
            TextButton.icon(
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size(double.infinity, 50),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                alignment: Alignment.centerLeft,
              ),
              onPressed: () {
                Navigator.pop(ctx);
                _chooseImage(context, false, onImageSelected);
              },
              icon: Icon(
                Icons.image,
                color: Theme.of(context).colorScheme.secondary,
              ),
              label: Text(
                'Select a picture from photos',
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  static Future<File> renamePickedImage(
    XFile pickedImage,
    String newFileName,
  ) async {
    // Get app's temporary directory
    final tempDir = await path_provider.getTemporaryDirectory();

    // Build new file path with desired name and same extension
    final newPath = path.join(
      tempDir.path,
      '$newFileName${path.extension(pickedImage.path)}',
    );

    // Copy picked file to new location with new name
    final renamedFile = await File(pickedImage.path).copy(newPath);

    return renamedFile;
  }

  static Future<void> _chooseImage(
    BuildContext context,
    bool isCamera,
    Function(File selectedImage) onImageSelected,
  ) async {
    final imagePicker = ImagePicker();
    final ImageSource source = isCamera
        ? ImageSource.camera
        : ImageSource.gallery;

    final pickedImage = await imagePicker.pickImage(
      source: source,
      maxWidth: 150,
      imageQuality: 50,
    );

    if (pickedImage == null) return;

    // File pickedImage = await renamePickedImage(image, 'profile_picture');

    final imageFile = File(pickedImage.path);
    final imageSizeInMB = await imageFile.length();

    File? selectedImage;
    if (imageSizeInMB > 10) {
      final compressedFile = await _compressImage(File(pickedImage.path));
      if (compressedFile != null) {
        selectedImage = File(compressedFile.path);
      }
    } else {
      selectedImage = File(pickedImage.path);
    }
    if (selectedImage != null) {
      onImageSelected(selectedImage);
    }
  }

  static Future<XFile?> _compressImage(File file) async {
    final dir = path.dirname(file.path);
    final filenameWithoutExt = path.basenameWithoutExtension(file.path);
    final filenameWithoutExtCompressed = '${filenameWithoutExt}_compressed';
    final newPath = path.join(dir, '$filenameWithoutExtCompressed.jpg');

    print(newPath);
    final compressedFile = await FlutterImageCompress.compressAndGetFile(
      file.path,
      newPath,
      quality: 30,
      minWidth: 800,
    );

    if (compressedFile == null) {
      print('Compression failed');
      return null;
    }
    print('Compressed file path: ${compressedFile.path}');

    return compressedFile;
  }
}
