// services/socket_service.dart
import 'dart:async';
import 'package:flutter/rendering.dart';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:tukxi/core/config/env_config.dart';
import 'package:tukxi/core/constants/socket_events.dart';
import 'package:tukxi/features/booking/data/models/driver_location_data.dart';
import 'package:tukxi/features/booking/data/models/ride_status_event.dart';

/// A global socket service:
/// - Connects once using JWT
/// - Tracks subscriptions (rideIds)
/// - Auto-resubscribes on reconnect
class SocketService {
  io.Socket? _socket;
  Completer<void>? _connecting;
  String? _currentRideId;
  bool _rideCompleted = false;

  bool get isConnected => _socket?.connected ?? false;

  /// Ensure the socket is connected with the given token.
  /// Reuses the same future if already connecting.
  Future<void> ensureConnected(String token) async {
    if (isConnected) return;
    if (_connecting != null && !(_connecting!.isCompleted)) {
      return _connecting!.future;
    }

    _connecting = Completer<void>();

    _socket = io.io(
      // 🔁 Replace with your server URL. Use HTTPS; transport is "websocket".
      EnvConfig.webSocketUrl,
      io.OptionBuilder()
          .setTransports(['websocket'])
          .setAuth({'token': token})
          .enableReconnection() // auto-reconnect
          .setReconnectionDelay(500) // ms
          .setReconnectionDelayMax(5000) // ms
          .build(),
    );

    _socket!
      ..onConnect((response) {
        // Resubscribe to all rideIds after (re)connect.
        _resubscribeIfNeeded();
        if (!(_connecting?.isCompleted ?? true)) _connecting!.complete();
        debugPrint('✅ Socket connected');
        debugPrint(response.toString());
      })
      ..onConnectError((err) {
        if (!(_connecting?.isCompleted ?? true)) {
          _connecting!.completeError(err ?? 'connect_error');
        }
        debugPrint('❌ Connect error: $err');
      })
      ..onReconnectAttempt((_) {
        debugPrint('↻ Reconnecting...');
      })
      ..onDisconnect((_) {
        debugPrint('🔌 Disconnected');
      });

    // Kick off connection
    _socket!.connect();

    return _connecting!.future;
  }

  /// ✅ Only resubscribe if current ride is not completed
  void _resubscribeIfNeeded() {
    if (_currentRideId != null && !_rideCompleted) {
      debugPrint('🔄 Resubscribing to active ride: $_currentRideId');
      _socket!.emit(SocketEvents.subscribeToRide, {'rideId': _currentRideId});
    } else {
      debugPrint('⚠️ No active ride to resubscribe to');
    }
  }

  /// Subscribe to a ride channel. Will resubscribe automatically on reconnect.
  void subscribeToRide(String rideId) {
    _currentRideId = rideId;
    _rideCompleted = false;

    if (!isConnected) return;
    _socket?.emit(SocketEvents.subscribeToRide, {'rideId': rideId});

    _socket?.on(SocketEvents.subscriptionSuccess, (data) {
      print(data);
    });
    debugPrint('📡 Subscribed to $rideId');
  }

  /// Handlers
  void listenRideStatus(void Function(RideStatusEvent rideStatus) handler) {
    debugPrint('📡 Listening ride status');
    // avoid duplicate bindings
    _socket?.on(SocketEvents.rideStatusUpdated, (data) {
      debugPrint('\n\n\n👏👏👏 Ride Status updated\n\n\n');
      print(data);
      final update = RideStatusEvent.fromJson(Map<String, dynamic>.from(data));
      handler(update);
    });
  }

  void listenDriverLocation(
    void Function(DriverLocationData locationData) handler,
  ) {
    // _socket?.off(SocketEvents.driverLocationUpdate);
    debugPrint('📡 Listening driverlocation');

    _socket?.on(SocketEvents.driverLocationUpdated, (data) {
      debugPrint('\n\n\n👏👏👏 Driver location updated\n\n\n');
      debugPrint(data.toString());
      final location = DriverLocationData.fromJson(
        Map<String, dynamic>.from(data),
      );
      handler(location);
    });
  }

  void offRideStatus() {
    _rideCompleted = true;
    _socket?.off(SocketEvents.rideStatusUpdated);
  }

  void offDriverLocation() => _socket?.off(SocketEvents.driverLocationUpdated);

  /// Call on app logout/quit (NOT on every screen pop).
  void disconnectCompletely() {
    _socket?.disconnect();
    _socket?.destroy();
    _socket?.close();
    _socket = null;
    _connecting = null;
  }
}
