import 'dart:math';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/config/env_config.dart';
import 'package:tukxi/core/constants/url_constants.dart';
import 'package:tukxi/core/enums/enum.dart';
import 'package:tukxi/core/errors/failure.dart';
import 'package:tukxi/core/models/route_info.dart';
import 'package:tukxi/core/network/network_service.dart';

class RouteService {
  static final _googleApiKey = EnvConfig.googleMapAPIKey;

  final Dio _dio = Dio()
    ..options = BaseOptions(
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
    );

  final _networkService = NetworkService();

  Future<RouteInfo?> getRoute({
    required LatLng origin,
    required LatLng destination,
  }) async {
    try {
      await _checkNetworkConnection();

      final url =
          '${UrlConstants.googleDirectionsUrl}?origin=${origin.latitude},${origin.longitude}&destination=${destination.latitude},${destination.longitude}&mode=driving&key=$_googleApiKey';
      debugPrint(url);
      final response = await _dio.get(url);

      debugPrint(response.toString());

      if (response.statusCode != 200) {
        debugPrint("Directions API failed: ${response.statusCode}");
        return null;
      }

      final data = response.data;

      if (data['status'] != 'OK') {
        debugPrint("Directions API error: ${data['status']}");
        return null;
      }

      return _fetchRouteInfo(responseData: data);
    } on DioException catch (e) {
      debugPrint("Dio error: ${e.message}");
      return null;
    } catch (error) {
      debugPrint("Error fetching route: $error");
      return null;
    }
  }

  RouteInfo? _fetchRouteInfo({required dynamic responseData}) {
    final routes = responseData?['routes'] as List?;
    if (routes == null || routes.isEmpty) return null;
    // final leg = route?['legs']?[0];
    final route = routes.first;
    // Polyline decode
    final overviewPolyline = route?['overview_polyline']?['points'] as String?;

    if (overviewPolyline == null || overviewPolyline.isEmpty) return null;

    final decodedPoints = PolylinePoints.decodePolyline(overviewPolyline);

    final polylineCoordinates = decodedPoints
        .map((p) => LatLng(p.latitude, p.longitude))
        .toList();
    final bounds = _boundsFromLatLngList(polylineCoordinates);
    // // Steps (optional, for turn-by-turn)
    // final steps = (leg['steps'] as List).map((s) {
    //   return RouteStep(
    //     instruction: s['html_instructions'] ?? '',
    //     startLocation: LatLng(
    //       s['start_location']['lat'],
    //       s['start_location']['lng'],
    //     ),
    //     endLocation: LatLng(
    //       s['end_location']['lat'],
    //       s['end_location']['lng'],
    //     ),
    //   );
    // }).toList();

    return RouteInfo(
      polylineCoordinates: polylineCoordinates,
      bounds: bounds,
      //   distanceText: distanceText,
      //   distanceValue: distanceValue,
      //   durationText: durationText,
      //   durationValue: durationValue,
      //   steps: steps,
    );
  }

  // Helper to get LatLngBounds for camera
  LatLngBounds _boundsFromLatLngList(List<LatLng> polylinePoints) {
    double minLat = polylinePoints.first.latitude;
    double maxLat = polylinePoints.first.latitude;
    double minLng = polylinePoints.first.longitude;
    double maxLng = polylinePoints.first.longitude;

    for (final point in polylinePoints) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    final bounds = LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );

    return bounds;
  }

  Future<LatLng> snapToRoad(LatLng point) async {
    try {
      await _checkNetworkConnection();

      final url =
          'https://roads.googleapis.com/v1/nearestRoads?points=${point.latitude},${point.longitude}&key=$_googleApiKey';

      final response = await _dio.get(url);

      if (response.statusCode != 200) {
        debugPrint("SnapToRoad failed: ${response.statusCode}");
        return point; // fallback
      }

      final data = response.data;
      final snappedPoints = data['snappedPoints'] as List?;
      if (snappedPoints == null || snappedPoints.isEmpty) {
        debugPrint("No snapped points found");
        return point; // fallback
      }

      final snapped = snappedPoints.first['location'];
      return LatLng(
        snapped['latitude'] as double,
        snapped['longitude'] as double,
      );
    } catch (e) {
      debugPrint("Error in snapToRoad: $e");
      return point; // fallback
    }
  }

  LatLngBounds extendBoundsForLabels(
    LatLngBounds bounds, {
    double latPadding = 0.001,
    double lngPadding = 0.001,
  }) {
    return LatLngBounds(
      southwest: LatLng(
        bounds.southwest.latitude - latPadding,
        bounds.southwest.longitude - lngPadding,
      ),
      northeast: LatLng(
        bounds.northeast.latitude + latPadding,
        bounds.northeast.longitude + lngPadding,
      ),
    );
  }

  // Check network before making any request
  Future<bool> _checkNetworkConnection() async {
    final hasConnection = await _networkService.checkConnection();
    if (!hasConnection) {
      throw ApiFailure(message: 'no_internet', type: ErrorType.noInternet);
    }
    return true;
  }

  double bearingBetween(LatLng from, LatLng to) {
    final double lat1 = from.latitude * pi / 180.0;
    final double lon1 = from.longitude * pi / 180.0;
    final double lat2 = to.latitude * pi / 180.0;
    final double lon2 = to.longitude * pi / 180.0;

    final double dLon = lon2 - lon1;

    final double y = sin(dLon) * cos(lat2);
    final double x = cos(lat1) * sin(lat2) - sin(lat1) * cos(lat2) * cos(dLon);

    double bearing = atan2(y, x);

    // Convert from radians to degrees
    bearing = bearing * 180.0 / pi;

    // Normalize to 0–360
    return (bearing + 360.0) % 360.0;
  }
}
