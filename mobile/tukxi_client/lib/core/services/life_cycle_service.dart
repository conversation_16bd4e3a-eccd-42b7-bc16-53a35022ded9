import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:tukxi/core/models/ip_location.dart';
import 'package:tukxi/core/services/location_service.dart';
import 'package:tukxi/core/services/shared_preference_service.dart';
import 'package:tukxi/features/home/<USER>/providers/location_provider.dart';
import 'package:tukxi/main.dart';

class LifeCycleService {
  StreamSubscription<Position>? _gpsStream;
  bool _initialized = false; // initialization only once
  bool _requestingPermission = false;
  final _prefs = SharedPreferenceService();

  void Function()? onRideScreenResumed;

  Future<void> start(WidgetRef ref) async {
    if (_initialized) return; // avoid multiple calls
    _initialized = true;

    await _initLocation(ref); // IP fallback or GPS
    await _startGpsStream(ref); // subscribe only if permission granted

    // Add lifecycle observer only once
    WidgetsBinding.instance.addObserver(AppLifecycleObserver(ref, this));
  }

  // ✅ Clear ride screen callback
  void clearRideScreenCallback() {
    debugPrint('🗑️ Clearing ride screen callback');
    onRideScreenResumed = null;
  }

  //Refresh on Resume
  Future<void> refreshLocation(WidgetRef ref) async {
    final gpsEnabled = await LocationService.isGpsEnabled();
    final permissionGranted = await LocationService.hasLocationPermission();

    final notifier = ref.read(locationProvider.notifier);
    notifier.updateGpsEnabled(gpsEnabled);
    notifier.updatePermissionGranted(permissionGranted);

    if (!gpsEnabled || !permissionGranted) {
      notifier.updateGpsLocationStatus(
        gpsEnabled: gpsEnabled,
        permissionGranted: permissionGranted,
        gpsLocation: null,
      );
      return;
    }

    final gpsPosition = await _fetchGpsLocation(ref);
    if (gpsPosition != null) {
      final newGps = LatLng(gpsPosition.latitude, gpsPosition.longitude);
      final currentGps = ref.read(locationProvider).gpsLocation;

      if (currentGps == null || _distanceBetween(currentGps, newGps) > 5) {
        notifier.updateGpsLocationStatus(
          gpsEnabled: gpsEnabled,
          permissionGranted: permissionGranted,
          gpsLocation: newGps,
        );

        final countryCode = await LocationService.getCountryCodeFromPosition(
          newGps,
        );
        notifier.updateCountryCode(countryCode);
      }
    }
  }

  Future<void> _initLocation(WidgetRef ref) async {
    if (_requestingPermission) return;
    _requestingPermission = true;

    try {
      final notifier = ref.read(locationProvider.notifier);

      // 1️⃣ Try cached IP location first
      final ipLoc = await _getCachedIpLocation();

      if (ipLoc != null) {
        final position = LatLng(ipLoc.latitude, ipLoc.longitude);
        notifier.updateIpLocation(position);
        notifier.updateCountryCode(ipLoc.countryCode);
      } else {
        // No cache → fetch from API
        final newIpLoc = await LocationService.getIpLocation();
        if (newIpLoc != null) {
          notifier.updateIpLocation(
            LatLng(newIpLoc.latitude, newIpLoc.longitude),
          );

          notifier.updateCountryCode(newIpLoc.countryCode);
          await _cacheIpLocation(newIpLoc);
        }
      }

      // 2️⃣ GPS service enabled?
      final gpsEnabled = await LocationService.isGpsEnabled();
      notifier.updateGpsEnabled(gpsEnabled);
      debugPrint(_prefs.isLocationPopupShown.toString());
      // 3️⃣ Permission check
      bool permissionGranted = await LocationService.hasLocationPermission();
      if (!_prefs.isLocationPopupShown || !permissionGranted) {
        permissionGranted = await LocationService.requestLocationPermission();
        await markPopupShown();
      }
      notifier.updatePermissionGranted(permissionGranted);

      // 🔹 Clear GPS location if GPS disabled or permission denied
      if (!gpsEnabled || !permissionGranted) {
        notifier.updateGpsLocationStatus(
          gpsEnabled: gpsEnabled,
          permissionGranted: permissionGranted,
          gpsLocation: null,
        );
        return; // fallback to IP location
      }

      // 4️⃣ Fetch GPS if possible
      if (gpsEnabled && permissionGranted) {
        await refreshLocation(ref);
      }
    } catch (e) {
      debugPrint('[LocationManagerService] Location fetch error: $e');
    } finally {
      _requestingPermission = false; // 🔹 reset here
    }
  }

  Future<IpLocation?> _getCachedIpLocation() async {
    final prefService = SharedPreferenceService();
    final timeStamp = prefService.ipCachedAt;
    final ipLat = prefService.ipLat;
    final ipLng = prefService.ipLng;
    final countryCode = prefService.countryCode;

    final loc = IpLocation(
      latitude: ipLat,
      longitude: ipLng,
      countryCode: countryCode,
    );

    // Expire after 1 day
    final now = DateTime.now().millisecondsSinceEpoch;
    if (timeStamp == 0 || now - timeStamp > Duration(days: 1).inMilliseconds) {
      return null;
    }

    return loc;
  }

  Future<void> _cacheIpLocation(IpLocation ipLocation) async {
    final prefService = SharedPreferenceService();
    prefService.ipLat = ipLocation.latitude;
    prefService.ipLng = ipLocation.longitude;
    prefService.countryCode = ipLocation.countryCode;
    prefService.ipCachedAt = DateTime.now().millisecondsSinceEpoch;
  }

  double _distanceBetween(LatLng a, LatLng b) {
    return Geolocator.distanceBetween(
      a.latitude,
      a.longitude,
      b.latitude,
      b.longitude,
    );
  }

  Future<Position?> _fetchGpsLocation(WidgetRef ref) async {
    final locationState = ref.read(locationProvider);

    if (!locationState.permissionGranted || !locationState.gpsEnabled) {
      // Don't subscribe if no permission or GPS is off
      return null;
    }
    try {
      return await LocationService.getCurrentPosition();
    } catch (_) {
      return null;
    }
  }

  Future<void> _startGpsStream(WidgetRef ref) async {
    final locationState = ref.read(locationProvider);

    if (!locationState.permissionGranted || !locationState.gpsEnabled) {
      // Don't subscribe if no permission or GPS is off
      return;
    }

    if (_gpsStream != null) return; // already subscribed

    _gpsStream =
        Geolocator.getPositionStream(
          locationSettings: LocationService.locationSettings,
        ).listen(
          (pos) async {
            final newLoc = LatLng(pos.latitude, pos.longitude);
            final currentLoc = ref.read(locationProvider).gpsLocation;

            if (currentLoc == null ||
                _distanceBetween(currentLoc, newLoc) > 30) {
              ref
                  .read(locationProvider.notifier)
                  .updateGpsLocationStatus(
                    gpsEnabled: locationState.gpsEnabled,
                    permissionGranted: locationState.permissionGranted,
                    gpsLocation: newLoc,
                  );

              // Update country code for GPS updates
              final countryCode =
                  await LocationService.getCountryCodeFromPosition(newLoc);
              ref
                  .read(locationProvider.notifier)
                  .updateCountryCode(countryCode);
            }
          },
          onError: (error) {
            debugPrint('[GPS Stream] Error: $error');
          },
        );
  }

  Future<void> markPopupShown() async {
    _prefs.isLocationPopupShown = true;
  }

  // ✅ Use GoRouter to check current route
  Future<void> handleRideLogicOnAppResume() async {
    final route = getCurrentRoute(navigatorKey.currentState!.context);

    debugPrint('🛣️ Current route: $route');

    // Check if current route is ride screen
    if (route == 'ride') {
      if (_prefs.hasActiveRideId()) {
        final rideId = _prefs.activeRideId;
        debugPrint(
          '✅ App resumed on ride screen with active ride: $rideId - calling API',
        );
        onRideScreenResumed?.call();
      } else {
        debugPrint('❌ App resumed on ride screen but no active ride ID found');
      }
    } else {
      if (_prefs.hasActiveRideId()) {
        final rideId = _prefs.activeRideId;
        debugPrint('🗑️ Not on ride screen , removing ride ID: $rideId');
        await _prefs.clearActiveRideId();
      }
    }
  }

  String getCurrentRoute(BuildContext context) {
    final GoRouter router = GoRouter.of(context);
    final RouteMatchList routeMatchList =
        router.routerDelegate.currentConfiguration;
    final RouteMatch lastMatch = routeMatchList.last;

    return lastMatch.route.name ?? 'unknown';
  }

  void dispose() {
    _gpsStream?.cancel();
    clearRideScreenCallback();
  }
}

class AppLifecycleObserver extends WidgetsBindingObserver {
  final WidgetRef ref;
  final LifeCycleService service;
  AppLifecycleObserver(this.ref, this.service);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // Re-evaluate location on resume without requesting permission again
      service.refreshLocation(ref);
      service.handleRideLogicOnAppResume();
    }
  }
}
