import { Module } from '@nestjs/common';
import { NotifierService } from './notifier.service';
import { GlobalEventEmitterModule } from '@shared/shared/event-emitter/event-emitter.module';
import { NotificationService } from '@shared/shared/common/notifications/engagespot/engagespot.service';
import { AppConfigModule } from '@shared/shared';
@Module({
  imports: [GlobalEventEmitterModule, AppConfigModule],
  providers: [NotifierService, NotificationService],
  exports: [NotifierService, NotificationService],
})
export class NotifierModule {}
