import { Process, Processor } from '@nestjs/bull';
import { Job } from 'bull';
import { Logger } from '@nestjs/common';
import { MainOrchestratorService } from '@shared/shared/modules/ride-matching/services/main-orchestrator.service';
import { RideData } from '@shared/shared/modules/ride-matching/interfaces';
import { RideMatchingSystemConfig } from '@shared/shared/modules/ride-matching/enhanced';

interface ProcessRideInitialJobData {
  rideData: RideData;
  config: RideMatchingSystemConfig;
  correlationId: string;
}

interface BatchNotificationJobData {
  rideData: RideData;
  driverGroups: any[][];
  batchIndex: number;
  radius: number;
  config: RideMatchingSystemConfig;
  expansionCount: number;
  correlationId: string;
}

interface RadiusExpansionJobData {
  rideData: RideData;
  currentRadius: number;
  expansionCount: number;
  config: RideMatchingSystemConfig;
  correlationId: string;
}

@Processor('ride-processing')
export class RideProcessingQueue {
  private readonly logger = new Logger(RideProcessingQueue.name);

  constructor(private readonly mainOrchestrator: MainOrchestratorService) {}

  /**
   * Process initial ride request job
   */
  @Process('process-ride-initial')
  async processRideInitial(job: Job<ProcessRideInitialJobData>): Promise<void> {
    const { rideData, config, correlationId } = job.data;

    this.logger.log(
      `Processing initial ride job ${job.id} for ride ${rideData.rideId} ` +
        `(correlationId: ${correlationId})`,
    );

    try {
      // Ensure config is defined with fallback to default
      const validConfig = config || this.getDefaultRideMatchingConfig();

      await this.mainOrchestrator.processInitialRideRequest(
        rideData,
        validConfig,
        correlationId,
      );

      this.logger.log(
        `Successfully completed initial ride processing job ${job.id} for ride ${rideData.rideId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process initial ride request for ride ${rideData.rideId} in job ${job.id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Process batch notification job
   */
  @Process('batch-notification')
  async processBatchNotification(
    job: Job<BatchNotificationJobData>,
  ): Promise<void> {
    const {
      rideData,
      driverGroups,
      batchIndex,
      radius,
      config,
      expansionCount,
      correlationId,
    } = job.data;

    this.logger.log(
      `Processing batch notification job ${job.id} for ride ${rideData.rideId} ` +
        `batch ${batchIndex + 1}/${driverGroups.length} ` +
        `(correlationId: ${correlationId})`,
    );

    try {
      // Ensure config is defined with fallback to default
      const validConfig = config || this.getDefaultRideMatchingConfig();

      await this.mainOrchestrator.processBatchNotification(
        rideData,
        driverGroups,
        batchIndex,
        radius,
        validConfig,
        expansionCount,
        correlationId,
      );

      this.logger.log(
        `Successfully completed batch notification job ${job.id} for ride ${rideData.rideId} ` +
          `batch ${batchIndex + 1}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process batch notification for ride ${rideData.rideId} ` +
          `batch ${batchIndex + 1} in job ${job.id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Process radius expansion job
   */
  @Process('radius-expansion')
  async processRadiusExpansion(
    job: Job<RadiusExpansionJobData>,
  ): Promise<void> {
    const { rideData, currentRadius, expansionCount, config, correlationId } =
      job.data;

    this.logger.log(
      `Processing radius expansion job ${job.id} for ride ${rideData.rideId} ` +
        `from ${currentRadius}km (expansion ${expansionCount}) ` +
        `(correlationId: ${correlationId})`,
    );

    try {
      // Ensure config is defined with fallback to default
      const validConfig = config || this.getDefaultRideMatchingConfig();

      await this.mainOrchestrator.processRadiusExpansion(
        rideData,
        currentRadius,
        expansionCount,
        validConfig,
        correlationId,
      );

      this.logger.log(
        `Successfully completed radius expansion job ${job.id} for ride ${rideData.rideId} ` +
          `from ${currentRadius}km`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process radius expansion for ride ${rideData.rideId} ` +
          `from ${currentRadius}km in job ${job.id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Legacy batch processing job (kept for backward compatibility)
   */
  @Process('process-batch')
  async processBatch(job: Job<any>): Promise<void> {
    const {
      rideData,
      driverGroups,
      batchIndex,
      radius,
      config,
      expansionCount,
      correlationId,
    } = job.data;

    this.logger.log(
      `Processing legacy batch job ${job.id} for ride ${rideData.rideId} ` +
        `batch ${batchIndex + 1}/${driverGroups.length} - redirecting to new batch notification`,
    );

    try {
      // Redirect to new batch notification processing
      await this.mainOrchestrator.processBatchNotification(
        rideData,
        driverGroups,
        batchIndex,
        radius,
        config,
        expansionCount,
        correlationId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process legacy batch for ride ${rideData.rideId} in job ${job.id}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get default ride matching system configuration
   */
  private getDefaultRideMatchingConfig(): RideMatchingSystemConfig {
    return {
      driverFinder: {
        enableFixedRadiusMode: true,
        defaultSearchMode: 'fixed',
        batchSizeLimit: 20,
        maxConcurrentSearches: 10,
      },
      radiusExpansion: {
        initialRadius: 2,
        radiusSteps: [2, 4, 6, 8, 10, 12, 15],
        maxRadius: 15,
        stepDelayMs: 10000,
        enableFallbackExpansion: false,
      },
      penalties: {
        batchNonResponsePenalty: 5,
        recentRejectionPenalty: 3,
        cancellationPenalty: 10,
        noShowPenalty: 15,
        penaltyDecayRate: 0.1,
        penaltyTtlHours: 24,
        maxPenaltyScore: 50,
        rejectionTimeWindowHours: 6,
      },
      batchProcessing: {
        batchSize: 10,
        batchTimeoutMs: 10000,
        maxBatches: 5,
        enableRadiusExpansion: true,
        maxRadius: 15,
        batchDelayMs: 0,
        overlapPrevention: true,
        penaltyAwareRanking: true,
        radiusExpansionConfig: {
          initialRadius: 2,
          radiusSteps: [2, 4, 6, 8, 10, 12, 15],
          maxRadius: 15,
          stepDelayMs: 10000,
          enableFallbackExpansion: false,
        },
        penaltyConfig: {
          batchNonResponsePenalty: 5,
          recentRejectionPenalty: 3,
          cancellationPenalty: 10,
          noShowPenalty: 15,
          penaltyDecayRate: 0.1,
          penaltyTtlHours: 24,
          maxPenaltyScore: 50,
          rejectionTimeWindowHours: 6,
        },
        stateManagement: {
          persistState: true,
          stateTtlMs: 3600000,
          enableRecovery: true,
          cleanupExpiredStates: true,
        },
      },
    };
  }
}
