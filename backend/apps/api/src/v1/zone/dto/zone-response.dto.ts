import { ApiProperty } from '@nestjs/swagger';
import {
  ZoneResponseDto,
  ZoneResponseDtoClass,
} from '@shared/shared/modules/zone/zone-response.dto';

export class ZoneListResponseDto {
  @ApiProperty({
    description: 'Array of zones',
    type: [ZoneResponseDtoClass],
  })
  data!: ZoneResponseDto[];

  @ApiProperty({
    description: 'Total number of zones',
    example: 25,
  })
  total!: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit!: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 3,
  })
  totalPages!: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNext!: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrev!: boolean;
}

export class CreateZoneResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone created successfully' })
  message!: string;

  @ApiProperty({ type: ZoneResponseDtoClass })
  data!: ZoneResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class ZoneDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ZoneResponseDtoClass })
  data!: ZoneResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class UpdateZoneResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone updated successfully' })
  message!: string;

  @ApiProperty({ type: ZoneResponseDtoClass })
  data!: ZoneResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class DeleteZoneResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone deleted successfully' })
  message!: string;

  @ApiProperty({ type: ZoneResponseDtoClass })
  data!: ZoneResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class RestoreZoneResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone restored successfully' })
  message!: string;

  @ApiProperty({ type: ZoneResponseDtoClass })
  data!: ZoneResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class ZonePaginatedResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zones retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ZoneListResponseDto })
  data!: ZoneListResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
