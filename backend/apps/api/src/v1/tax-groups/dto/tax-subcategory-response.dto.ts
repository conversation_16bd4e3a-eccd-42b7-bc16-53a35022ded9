import { ApiProperty } from '@nestjs/swagger';

export class TaxSubcategoryResponseDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Unique identifier of the tax subcategory',
  })
  id!: string;

  @ApiProperty({
    example: 'CGST',
    description: 'Name of the tax subcategory',
  })
  name!: string;

  @ApiProperty({
    example: 9.0,
    description: 'Percentage for this subcategory',
  })
  percentage!: number;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Last update timestamp',
  })
  updatedAt!: string;
}
