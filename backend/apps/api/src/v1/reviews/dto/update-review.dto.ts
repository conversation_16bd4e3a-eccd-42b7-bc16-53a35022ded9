import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Length,
} from 'class-validator';

export class UpdateReviewDto {
  @ApiProperty({
    example: 4.5,
    description: 'Updated rating from 1.0 to 5.0',
    minimum: 1,
    maximum: 5,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  rating?: number;

  @ApiProperty({
    example: 'Updated review text.',
    description: 'Updated review text (max 1000 characters)',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Review must be a string' })
  @MaxLength(1000, { message: 'Review text cannot exceed 1000 characters' })
  review?: string;
}
