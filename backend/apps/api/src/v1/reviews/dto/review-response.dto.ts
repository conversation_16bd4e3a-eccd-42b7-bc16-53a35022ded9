import { ApiProperty } from '@nestjs/swagger';

export class UserProfileDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'User profile ID',
  })
  id!: string;

  @ApiProperty({
    example: 'John',
    description: 'User first name',
    nullable: true,
  })
  firstName!: string | null;

  @ApiProperty({
    example: 'Doe',
    description: 'User last name',
    nullable: true,
  })
  lastName!: string | null;

  @ApiProperty({
    example: 'https://example.com/profile.jpg',
    description: 'Profile picture URL',
    nullable: true,
  })
  profilePictureUrl!: string | null;
}

export class RideDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Ride ID',
  })
  id!: string;

  @ApiProperty({
    example: 'trip_completed',
    description: 'Ride status',
  })
  status!: string;

  @ApiProperty({
    example: '2025-01-15T10:30:00.000Z',
    description: 'Ride creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-01-15T11:30:00.000Z',
    description: 'Ride completion timestamp',
    nullable: true,
  })
  completedAt!: string | null;
}

export class ReviewDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Review ID',
  })
  id!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Rider ID',
  })
  riderId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Driver ID',
  })
  driverId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'Ride ID',
  })
  rideId!: string;

  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-426614174000',
    description: 'ID of the user who wrote the review',
  })
  reviewById!: string;

  @ApiProperty({
    example: 4.5,
    description: 'Rating from 1.0 to 5.0',
  })
  rating!: number;

  @ApiProperty({
    example: 'Great experience! Highly recommended.',
    description: 'Review text',
    nullable: true,
  })
  review!: string | null;

  @ApiProperty({
    example: '2025-01-15T12:00:00.000Z',
    description: 'Review creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-01-15T12:00:00.000Z',
    description: 'Review last update timestamp',
  })
  updatedAt!: string;

  @ApiProperty({
    type: UserProfileDto,
    description: 'Rider information',
    required: false,
  })
  rider?: UserProfileDto;

  @ApiProperty({
    type: UserProfileDto,
    description: 'Driver information',
    required: false,
  })
  driver?: UserProfileDto;

  @ApiProperty({
    type: RideDto,
    description: 'Ride information',
    required: false,
  })
  ride?: RideDto;

  @ApiProperty({
    type: UserProfileDto,
    description: 'Review author information',
    required: false,
  })
  reviewBy?: UserProfileDto;
}

export class ReviewStatsDto {
  @ApiProperty({
    example: 25,
    description: 'Total number of reviews',
  })
  totalReviews!: number;

  @ApiProperty({
    example: 4.3,
    description: 'Average rating',
  })
  averageRating!: number;

  @ApiProperty({
    example: { '1': 0, '2': 1, '3': 2, '4': 10, '5': 12 },
    description: 'Distribution of ratings',
  })
  ratingDistribution!: { [key: string]: number };
}

export class CreateReviewResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Review created successfully' })
  message!: string;

  @ApiProperty({ type: ReviewDto })
  data!: ReviewDto;

  @ApiProperty({ example: Date.now() })
  timestamp!: number;
}

export class GetReviewResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Review retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ReviewDto })
  data!: ReviewDto;

  @ApiProperty({ example: Date.now() })
  timestamp!: number;
}

export class GetReviewsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Reviews retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: 'object',
    properties: {
      reviews: {
        type: 'array',
        items: { $ref: '#/components/schemas/ReviewDto' },
      },
      total: { type: 'number', example: 50 },
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
    },
  })
  data!: {
    reviews: ReviewDto[];
    total: number;
    page: number;
    limit: number;
  };

  @ApiProperty({ example: Date.now() })
  timestamp!: number;
}

export class GetReviewStatsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Review statistics retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ReviewStatsDto })
  data!: ReviewStatsDto;

  @ApiProperty({ example: Date.now() })
  timestamp!: number;
}
