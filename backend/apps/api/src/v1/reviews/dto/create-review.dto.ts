import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsUUID,
  IsNumber,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MaxLength,
} from 'class-validator';

export class CreateDriverReviewDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the ride to review',
  })
  @IsUUID(4, { message: 'Ride ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Ride ID is required' })
  rideId!: string;

  @ApiProperty({
    example: 4.5,
    description: 'Rating from 1.0 to 5.0',
    minimum: 1,
    maximum: 5,
  })
  @IsNumber({}, { message: 'Rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  rating!: number;

  @ApiProperty({
    example: 'Great rider! Very polite and punctual.',
    description: 'Optional review text (max 1000 characters)',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Review must be a string' })
  @MaxLength(1000, { message: 'Review text cannot exceed 1000 characters' })
  review?: string;
}

export class CreateRiderReviewDto {
  @ApiProperty({
    example: '123e4567-e89b-12d3-a456-************',
    description: 'ID of the ride to review',
  })
  @IsUUID(4, { message: 'Ride ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Ride ID is required' })
  rideId!: string;

  @ApiProperty({
    example: 4.5,
    description: 'Rating from 1.0 to 5.0',
    minimum: 1,
    maximum: 5,
  })
  @IsNumber({}, { message: 'Rating must be a number' })
  @Min(1, { message: 'Rating must be at least 1' })
  @Max(5, { message: 'Rating must be at most 5' })
  rating!: number;

  @ApiProperty({
    example: 'Excellent driver! Safe driving and arrived on time.',
    description: 'Optional review text (max 1000 characters)',
    required: false,
    maxLength: 1000,
  })
  @IsOptional()
  @IsString({ message: 'Review must be a string' })
  @MaxLength(1000, { message: 'Review text cannot exceed 1000 characters' })
  review?: string;
}
