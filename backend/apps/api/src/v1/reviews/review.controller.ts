import {
  Controller,
  Post,
  Get,
  Put,
  Body,
  Param,
  // Query,
  Req,
  HttpCode,
  HttpStatus,
  UseGuards,
  // ParseIntPipe,
  // DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  // ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { ReviewService } from '@shared/shared/modules/review/review.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import {
  CreateDriverReviewDto,
  CreateRiderReviewDto,
  UpdateReviewDto,
  CreateReviewResponseDto,
  GetReviewResponseDto,
  GetReviewsResponseDto,
  // GetReviewStatsResponseDto,
} from './dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';

@ApiTags('Reviews')
@Controller('reviews')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ReviewController {
  constructor(private readonly reviewService: ReviewService) {}

  @Post('driver/review-rider')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Driver reviews rider',
    description: 'Allows a driver to review a rider after completing a ride',
  })
  @ApiResponse({
    status: 201,
    description: 'Review created successfully',
    type: CreateReviewResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or business rule violation',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not authorized to review this ride',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Ride not found',
    type: ApiErrorResponseDto,
  })
  async driverReviewRider(
    @Body() createReviewDto: CreateDriverReviewDto,
    @Req() req: Request,
  ) {
    const driverId = (req.user as any)?.profileId;

    const review = await this.reviewService.driverReviewRider(
      createReviewDto.rideId,
      driverId,
      createReviewDto.rating,
      createReviewDto.review,
    );

    return {
      success: true,
      message: 'Driver review submitted successfully',
      data: review,
      timestamp: Date.now(),
    };
  }

  @Post('rider/review-driver')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Rider reviews driver',
    description: 'Allows a rider to review a driver after completing a ride',
  })
  @ApiResponse({
    status: 201,
    description: 'Review created successfully',
    type: CreateReviewResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input or business rule violation',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not authorized to review this ride',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Ride not found',
    type: ApiErrorResponseDto,
  })
  async riderReviewDriver(
    @Body() createReviewDto: CreateRiderReviewDto,
    @Req() req: Request,
  ) {
    const riderId = (req.user as any)?.profileId;

    const review = await this.reviewService.riderReviewDriver(
      createReviewDto.rideId,
      riderId,
      createReviewDto.rating,
      createReviewDto.review,
    );

    return {
      success: true,
      message: 'Rider review submitted successfully',
      data: review,
      timestamp: Date.now(),
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get review by ID',
    description: 'Retrieve a specific review by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Review ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Review retrieved successfully',
    type: GetReviewResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Review not found',
    type: ApiErrorResponseDto,
  })
  async getReviewById(@Param('id') id: string) {
    const review = await this.reviewService.getReviewById(id);

    return {
      success: true,
      message: 'Review retrieved successfully',
      data: review,
      timestamp: Date.now(),
    };
  }

  // @Get('user/as-driver')
  // @ApiOperation({
  //   summary: 'Get reviews for current user as driver',
  //   description: 'Retrieve reviews where the current user was the driver',
  // })
  // @ApiQuery({
  //   name: 'page',
  //   required: false,
  //   description: 'Page number',
  //   example: 1,
  // })
  // @ApiQuery({
  //   name: 'limit',
  //   required: false,
  //   description: 'Number of items per page',
  //   example: 10,
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Reviews retrieved successfully',
  //   type: GetReviewsResponseDto,
  // })
  // async getUserReviewsAsDriver(
  //   @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  //   @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  //   @Req() req: Request,
  // ) {
  //   const userId = (req.user as any)?.profileId;

  //   const result = await this.reviewService.getUserReviews(userId, 'driver', page, limit);

  //   return {
  //     success: true,
  //     message: 'Driver reviews retrieved successfully',
  //     data: result,
  //     timestamp: Date.now(),
  //   };
  // }

  // @Get('user/as-rider')
  // @ApiOperation({
  //   summary: 'Get reviews for current user as rider',
  //   description: 'Retrieve reviews where the current user was the rider',
  // })
  // @ApiQuery({
  //   name: 'page',
  //   required: false,
  //   description: 'Page number',
  //   example: 1,
  // })
  // @ApiQuery({
  //   name: 'limit',
  //   required: false,
  //   description: 'Number of items per page',
  //   example: 10,
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Reviews retrieved successfully',
  //   type: GetReviewsResponseDto,
  // })
  // async getUserReviewsAsRider(
  //   @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
  //   @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  //   @Req() req: Request,
  // ) {
  //   const userId = (req.user as any)?.profileId;

  //   const result = await this.reviewService.getUserReviews(userId, 'rider', page, limit);

  //   return {
  //     success: true,
  //     message: 'Rider reviews retrieved successfully',
  //     data: result,
  //     timestamp: Date.now(),
  //   };
  // }

  // @Get('user/stats/as-driver')
  // @ApiOperation({
  //   summary: 'Get review statistics for current user as driver',
  //   description: 'Get aggregated review statistics for the current user as a driver',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Review statistics retrieved successfully',
  //   type: GetReviewStatsResponseDto,
  // })
  // async getUserReviewStatsAsDriver(@Req() req: Request) {
  //   const userId = (req.user as any)?.profileId;

  //   const stats = await this.reviewService.getUserReviewStats(userId, 'driver');

  //   return {
  //     success: true,
  //     message: 'Driver review statistics retrieved successfully',
  //     data: stats,
  //     timestamp: Date.now(),
  //   };
  // }

  // @Get('user/stats/as-rider')
  // @ApiOperation({
  //   summary: 'Get review statistics for current user as rider',
  //   description: 'Get aggregated review statistics for the current user as a rider',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Review statistics retrieved successfully',
  //   type: GetReviewStatsResponseDto,
  // })
  // async getUserReviewStatsAsRider(@Req() req: Request) {
  //   const userId = (req.user as any)?.profileId;

  //   const stats = await this.reviewService.getUserReviewStats(userId, 'rider');

  //   return {
  //     success: true,
  //     message: 'Rider review statistics retrieved successfully',
  //     data: stats,
  //     timestamp: Date.now(),
  //   };
  // }

  @Put(':id')
  @ApiOperation({
    summary: 'Update review',
    description: 'Update an existing review (only by the reviewer)',
  })
  @ApiParam({
    name: 'id',
    description: 'Review ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Review updated successfully',
    type: GetReviewResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Not authorized to update this review',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Review not found',
    type: ApiErrorResponseDto,
  })
  async updateReview(
    @Param('id') id: string,
    @Body() updateReviewDto: UpdateReviewDto,
    @Req() req: Request,
  ) {
    const userId = (req.user as any)?.profileId;

    const review = await this.reviewService.updateReview(
      id,
      userId,
      updateReviewDto,
    );

    return {
      success: true,
      message: 'Review updated successfully',
      data: review,
      timestamp: Date.now(),
    };
  }

  @Get('ride/:rideId')
  @ApiOperation({
    summary: 'Get reviews for a specific ride',
    description: 'Retrieve all reviews for a specific ride',
  })
  @ApiParam({
    name: 'rideId',
    description: 'Ride ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Ride reviews retrieved successfully',
    type: GetReviewsResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Ride not found',
    type: ApiErrorResponseDto,
  })
  async getReviewsForRide(@Param('rideId') rideId: string) {
    const reviews = await this.reviewService.getReviewsForRide(rideId);

    return {
      success: true,
      message: 'Ride reviews retrieved successfully',
      data: reviews,
      meta: {
        page: 1,
        limit: reviews.length,
        total: reviews.length,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false,
      },
      timestamp: Date.now(),
    };
  }
}
