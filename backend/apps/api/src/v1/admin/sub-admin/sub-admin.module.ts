import { Module } from '@nestjs/common';
import { SubAdminController } from './sub-admin.controller';
import { AdminModule } from '@shared/shared/modules/admin/admin.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [AdminModule],
  providers: [CityAdminRepository],
  controllers: [SubAdminController],
})
export class SubAdminModule {}
