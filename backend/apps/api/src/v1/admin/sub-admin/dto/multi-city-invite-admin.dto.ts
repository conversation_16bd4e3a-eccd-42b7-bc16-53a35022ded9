import { <PERSON><PERSON><PERSON>y, IsUUID, IsNotEmpty, ArrayMinSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { InviteAdminDto } from './invite-admin.dto';

export class MultiCityInviteAdminDto extends InviteAdminDto {
  @ApiProperty({
    description:
      'Array of City UUIDs to assign the admin to (at least one required)',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '456e7890-e89b-12d3-a456-************',
    ],
    type: [String],
    required: true,
  })
  @IsNotEmpty({ message: 'City IDs array cannot be empty' })
  @IsArray({ message: 'City IDs must be an array' })
  @ArrayMinSize(1, { message: 'At least one city ID is required' })
  @IsUUID('4', { each: true, message: 'Each city ID must be a valid UUID' })
  cityIds!: string[];
}
