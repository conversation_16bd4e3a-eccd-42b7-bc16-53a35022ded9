import { IsJWT, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VerifyTokenDto {
  @ApiProperty({
    description: 'JWT invitation token to verify (must be a valid JWT format)',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'Token is required' })
  @IsJWT({ message: 'Token must be a valid JWT' })
  token!: string;
}
