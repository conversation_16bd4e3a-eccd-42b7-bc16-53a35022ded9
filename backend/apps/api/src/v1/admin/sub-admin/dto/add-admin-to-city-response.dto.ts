import { ApiProperty } from '@nestjs/swagger';

export class AddAdminToCityResponseDto {
  @ApiProperty()
  @ApiProperty()
  success?: boolean;

  @ApiProperty({
    description: 'Message describing the result of the operation',
    example: 'Admin added to city successfully',
  })
  message?: string;

  @ApiProperty({
    description: 'Data containing the city admin record',
    nullable: true,
  })
  data?: any;

  @ApiProperty({
    description: 'Timestamp of the response',
    example: 1640995200000,
  })
  timestamp?: number;
}
