import { IsUUID, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class RemoveCityAdminDto {
  @ApiProperty({
    description:
      'UUID of the user profile (admin) to remove from the specific city',
    example: '123e4567-e89b-12d3-a456-************',
    type: String,
    required: true,
  })
  @IsNotEmpty({ message: 'User profile ID is required' })
  @IsUUID('4', { message: 'User profile ID must be a valid UUID' })
  userProfileId!: string;
}
