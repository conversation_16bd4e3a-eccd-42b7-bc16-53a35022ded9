import { ApiProperty } from '@nestjs/swagger';

export class UserResponseDto {
  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  id!: string;

  @ApiProperty({
    description: 'Email address',
    example: '<EMAIL>',
    type: String,
    required: true,
  })
  email!: string;

  @ApiProperty({
    description: 'Phone number',
    example: '+1234567890',
    type: String,
    required: false,
  })
  phoneNumber?: string;

  @ApiProperty({
    description: 'User creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: Date,
    required: true,
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'User last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: Date,
    required: true,
  })
  updatedAt!: Date;
}

export class RoleResponseDto {
  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  id!: string;

  @ApiProperty({
    description: 'Role name',
    example: 'admin',
    type: String,
    required: true,
  })
  name!: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Administrator role with full access',
    type: String,
    required: false,
  })
  description?: string;
}

export class CityResponseDto {
  @ApiProperty({
    description: 'City ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  id!: string;

  @ApiProperty({
    description: 'City name',
    example: 'Mumbai',
    type: String,
    required: true,
  })
  name!: string;

  @ApiProperty({
    description: 'City code',
    example: 'MUM',
    type: String,
    required: false,
  })
  code?: string;
}

export class AdminProfileResponseDto {
  @ApiProperty({
    description: 'Profile ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  id!: string;

  @ApiProperty({
    description: 'User ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  userId!: string;

  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
    type: String,
    required: true,
  })
  roleId!: string;

  @ApiProperty({
    description: 'First name',
    example: 'John',
    type: String,
    required: true,
  })
  firstName!: string;

  @ApiProperty({
    description: 'Last name',
    example: 'Doe',
    type: String,
    required: true,
  })
  lastName!: string;

  @ApiProperty({
    description: 'Profile status',
    enum: ['active', 'pending', 'disabled', 'inactive', 'invited'],
    example: 'active',
    type: String,
    required: true,
  })
  status!: string;

  @ApiProperty({
    description: 'Profile creation timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: Date,
    required: true,
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Profile last update timestamp',
    example: '2024-01-15T10:30:00.000Z',
    type: Date,
    required: true,
  })
  updatedAt!: Date;

  @ApiProperty({
    description: 'Associated user details',
    type: UserResponseDto,
    required: true,
  })
  user!: UserResponseDto;

  @ApiProperty({
    description: 'Associated role details',
    type: RoleResponseDto,
    required: false,
  })
  role?: RoleResponseDto;
}

export class InviteAdminResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin invitation sent successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Admin invitation data',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class MultiCityInviteResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Multi-city admin invitation sent successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description:
      'Multi-city invitation data (userProfile, user, role, cities, cityAdmins, inviteLink)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class SetupPasswordResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Password setup completed successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description:
      'Authentication tokens and user data (accessToken, refreshToken, etc.)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class VerifyTokenResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Token verified successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Verified token data (profile details)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class GetAdminProfileResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin profile retrieved successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Admin profile data',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class ResendInviteResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Invitation resent successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Resend invitation data (inviteLink, userProfile, user, role)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class ChangeStatusResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin profile status updated successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Status change data (userProfile, previousStatus, newStatus)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class RemoveCityAdminsResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'City admins removed successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'Removal data (removedCount, cityAdminIds)',
    type: Object,
    required: true,
  })
  data!: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}

export class RemoveCityAdminResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
    type: Boolean,
    required: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Admin removed from city successfully',
    type: String,
    required: true,
  })
  message!: string;

  @ApiProperty({
    description: 'No data returned for this operation',
    type: Object,
    required: false,
  })
  data?: any;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
    type: Number,
    required: true,
  })
  timestamp!: number;
}
