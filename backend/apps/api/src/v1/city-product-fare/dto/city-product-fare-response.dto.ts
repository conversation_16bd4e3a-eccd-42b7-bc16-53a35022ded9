import { ApiProperty } from '@nestjs/swagger';
import { CityProductFareStatus } from '@shared/shared/repositories/models/cityProductFare.model';

export class ZoneResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'Unique identifier of the zone',
  })
  id!: string;

  @ApiProperty({
    example: 'Downtown Business District',
    description: 'Name of the zone',
  })
  name!: string;

  @ApiProperty({
    example: 'Central business area of the city',
    description: 'Description of the zone',
    nullable: true,
  })
  description?: string | null;
}

export class CityProductResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440001',
    description: 'Unique identifier of the city product',
  })
  id!: string;

  @ApiProperty({
    example: 'Taxi Service',
    description: 'Name of the product',
  })
  productName!: string;

  @ApiProperty({
    example: 'Kochi',
    description: 'Name of the city',
  })
  cityName!: string;
}

export class ChargeGroupResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440002',
    description: 'Unique identifier of the charge group',
  })
  id!: string;

  @ApiProperty({
    example: 'Premium Charges',
    description: 'Name of the charge group',
  })
  name!: string;

  @ApiProperty({
    example: 'premium_charges',
    description: 'Identifier of the charge group',
    nullable: true,
  })
  identifier?: string | null;

  @ApiProperty({
    example: 1,
    description: 'Priority of the charge group within the fare rule',
  })
  priority!: number;
}

export class CityProductFareResponseDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440003',
    description: 'Unique identifier of the city product fare',
  })
  id!: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440001',
    description: 'City product ID',
  })
  cityProductId!: string;

  @ApiProperty({
    example: 1,
    description: 'Priority of the fare rule (lower values = higher priority)',
  })
  priority!: number;

  @ApiProperty({
    example: 'active',
    description: 'Status of the fare rule',
    enum: CityProductFareStatus,
  })
  status!: CityProductFareStatus;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440000',
    description: 'From zone ID (pickup zone)',
    nullable: true,
  })
  fromZoneId?: string | null;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440004',
    description: 'To zone ID (destination zone)',
    nullable: true,
  })
  toZoneId?: string | null;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Last update timestamp',
  })
  updatedAt!: string;

  @ApiProperty({
    type: CityProductResponseDto,
    description: 'City product information',
  })
  cityProduct?: CityProductResponseDto;

  @ApiProperty({
    type: ZoneResponseDto,
    description: 'From zone (pickup zone) information',
    nullable: true,
  })
  fromZone?: ZoneResponseDto | null;

  @ApiProperty({
    type: ZoneResponseDto,
    description: 'To zone (destination zone) information',
    nullable: true,
  })
  toZone?: ZoneResponseDto | null;

  @ApiProperty({
    type: [ChargeGroupResponseDto],
    description: 'Charge groups attached to this fare rule',
  })
  chargeGroups?: ChargeGroupResponseDto[];

  @ApiProperty({
    type: [ZoneResponseDto],
    description:
      'From zones (when fromZoneTypeId is used) - all zones in the city matching the from zone type',
    required: false,
  })
  fromZones?: ZoneResponseDto[];

  @ApiProperty({
    type: [ZoneResponseDto],
    description:
      'To zones (when toZoneTypeId is used) - all zones in the city matching the to zone type',
    required: false,
  })
  toZones?: ZoneResponseDto[];

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440003',
    description: 'From zone type ID used to generate fromZones',
    nullable: true,
  })
  fromZoneTypeId?: string | null;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-446655440004',
    description: 'To zone type ID used to generate toZones',
    nullable: true,
  })
  toZoneTypeId?: string | null;
}

export class CityProductFareListResponseDto {
  @ApiProperty({
    type: [CityProductFareResponseDto],
    description: 'List of city product fares',
  })
  data!: CityProductFareResponseDto[];

  @ApiProperty({
    example: 1,
    description: 'Current page number',
  })
  page!: number;

  @ApiProperty({
    example: 10,
    description: 'Number of items per page',
  })
  limit!: number;

  @ApiProperty({
    example: 25,
    description: 'Total number of items',
  })
  total!: number;

  @ApiProperty({
    example: 3,
    description: 'Total number of pages',
  })
  totalPages!: number;

  @ApiProperty({
    example: true,
    description: 'Whether there is a next page',
  })
  hasNextPage!: boolean;

  @ApiProperty({
    example: false,
    description: 'Whether there is a previous page',
  })
  hasPrevPage!: boolean;
}

export class AttachChargeGroupsResponseDto {
  @ApiProperty({
    type: [ChargeGroupResponseDto],
    description: 'Charge groups attached to the fare rule',
  })
  data!: ChargeGroupResponseDto[];

  @ApiProperty({
    example: 'Charge groups attached successfully',
    description: 'Success message',
  })
  message!: string;
}

export class DetachChargeGroupResponseDto {
  @ApiProperty({
    example: 'Charge group detached successfully',
    description: 'Success message',
  })
  message!: string;
}

export class UpdatePrioritiesResponseDto {
  @ApiProperty({
    type: [ChargeGroupResponseDto],
    description: 'Updated charge groups with new priorities',
  })
  data!: ChargeGroupResponseDto[];

  @ApiProperty({
    example: 'Charge group priorities updated successfully',
    description: 'Success message',
  })
  message!: string;
}

export class FareSelectionResponseDto {
  @ApiProperty({
    type: CityProductFareResponseDto,
    description: 'Best matching fare rule based on priority',
    nullable: true,
  })
  data?: CityProductFareResponseDto | null;

  @ApiProperty({
    example: 'Fare rule selected successfully',
    description: 'Response message',
  })
  message!: string;
}
