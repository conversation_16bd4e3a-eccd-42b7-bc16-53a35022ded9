import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNumber, IsEnum, IsOptional, Min } from 'class-validator';
import { CityProductFareStatus } from '@shared/shared/repositories/models/cityProductFare.model';

export class CreateCityProductFareDto {
  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description: 'City product ID for which the fare rule is being created',
  })
  @IsUUID('4', { message: 'City product ID must be a valid UUID' })
  cityProductId!: string;

  @ApiProperty({
    example: 1,
    description: 'Priority of the fare rule (lower values = higher priority)',
    minimum: 1,
  })
  @IsNumber({}, { message: 'Priority must be a number' })
  @Min(1, { message: 'Priority must be at least 1' })
  priority!: number;

  @ApiProperty({
    example: 'active',
    description: 'Status of the fare rule',
    enum: CityProductFareStatus,
    default: CityProductFareStatus.ACTIVE,
  })
  @IsEnum(CityProductFareStatus, {
    message: 'Status must be either active or inactive',
  })
  @IsOptional()
  status?: CityProductFareStatus;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'From zone ID (pickup zone) - optional, null for default rules',
    required: false,
  })
  @IsUUID('4', { message: 'From zone ID must be a valid UUID' })
  @IsOptional()
  fromZoneId?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'To zone ID (destination zone) - optional, null for default rules',
    required: false,
  })
  @IsUUID('4', { message: 'To zone ID must be a valid UUID' })
  @IsOptional()
  toZoneId?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'From zone type ID - optional, mutually exclusive with fromZoneId and toZoneId. When provided, all zones in the city matching this zone type will be used as from zones.',
    required: false,
  })
  @IsUUID('4', { message: 'From zone type ID must be a valid UUID' })
  @IsOptional()
  fromZoneTypeId?: string;

  @ApiProperty({
    example: '550e8400-e29b-41d4-a716-************',
    description:
      'To zone type ID - optional, mutually exclusive with fromZoneId and toZoneId. When provided, all zones in the city matching this zone type will be used as to zones.',
    required: false,
  })
  @IsUUID('4', { message: 'To zone type ID must be a valid UUID' })
  @IsOptional()
  toZoneTypeId?: string;
}
