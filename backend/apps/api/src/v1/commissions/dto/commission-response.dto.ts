import { ApiProperty } from '@nestjs/swagger';
import { CommissionType } from '@shared/shared/repositories/models/commission.model';
import { TaxGroupResponseDto } from '../../tax-groups/dto/tax-group-response.dto';

export class CommissionResponseDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Unique identifier of the commission',
  })
  id!: string;

  @ApiProperty({
    example: 'Driver Commission',
    description: 'Name of the commission',
  })
  name!: string;

  @ApiProperty({
    example: 'Commission for driver services',
    description: 'Description of the commission',
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    example: 'percentage',
    description: 'Type of commission calculation',
    enum: CommissionType,
  })
  type!: CommissionType;

  @ApiProperty({
    example: 15.5,
    description: 'Percentage value (when type is "percentage")',
    nullable: true,
  })
  percentageValue?: number | null;

  @ApiProperty({
    example: 25.00,
    description: 'Flat amount value (when type is "flat")',
    nullable: true,
  })
  flatValue?: number | null;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Tax group ID',
    nullable: true,
  })
  taxGroupId?: string | null;

  @ApiProperty({
    description: 'Tax group information',
    type: TaxGroupResponseDto,
    nullable: true,
  })
  taxGroup?: TaxGroupResponseDto | null;

  @ApiProperty({
    example: '2025-10-06T06:57:29.000Z',
    description: 'Creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-10-06T06:57:29.000Z',
    description: 'Last update timestamp',
  })
  updatedAt!: string;
}
