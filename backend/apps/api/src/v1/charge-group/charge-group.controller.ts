import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { CreateChargeGroupDto } from './dto/create-charge-group.dto';
import { UpdateChargeGroupDto } from './dto/update-charge-group.dto';
import { ChargeGroupResponseDto } from './dto/charge-group-response.dto';
import { ChargeGroupPaginationDto } from './dto/charge-group-pagination.dto';
import {
  ApiResponseDto,
  PaginatedResponseDto,
  ApiErrorResponseDto,
} from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ChargeGroupService } from '@shared/shared/modules/charge-group/charge-group.service';
import { ChargeResponseDto } from './charge/dto/charge-response.dto';
import { CreateChargeDto } from './charge/dto/create-charge.dto';
import { ChargeService } from '@shared/shared/modules/charge/charge.service';

@ApiTags('Charge Groups')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('charge-groups')
export class ChargeGroupController {
  constructor(
    private readonly chargeGroupService: ChargeGroupService,
    private readonly chargeService: ChargeService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all charge groups without pagination' })
  @ApiResponse({
    status: 200,
    description: 'All charge groups retrieved successfully',
    type: ApiResponseDto<ChargeGroupResponseDto[]>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAllWithoutPagination() {
    const data = await this.chargeGroupService.findAllChargeGroups();
    return {
      success: true,
      message: 'All charge groups retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Get('paginate')
  @ApiOperation({
    summary: 'Get all charge groups with pagination and filters',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search by charge group name',
  })
  @ApiQuery({
    name: 'sortBy',
    required: false,
    type: String,
    description: 'Field to sort by',
  })
  @ApiQuery({
    name: 'sortOrder',
    required: false,
    enum: ['asc', 'desc'],
    description: 'Sort order',
  })
  @ApiResponse({
    status: 200,
    description: 'Charge groups retrieved successfully',
    type: PaginatedResponseDto<ChargeGroupResponseDto>,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(@Query() paginationDto: ChargeGroupPaginationDto) {
    const result =
      await this.chargeGroupService.paginateChargeGroups(paginationDto);
    return {
      success: true,
      message: 'Charge groups retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  @Get(':chargeGroupId')
  @ApiOperation({ summary: 'Get charge group by ID' })
  @ApiParam({
    name: 'chargeGroupId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Charge group retrieved successfully',
    type: ApiResponseDto<ChargeGroupResponseDto>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findOne(@Param('chargeGroupId') id: string) {
    const data = await this.chargeGroupService.findChargeGroupById(id);
    return {
      success: true,
      message: 'Charge group retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Create a new charge group' })
  @ApiResponse({
    status: 201,
    description: 'Charge group created successfully',
    type: ApiResponseDto<ChargeGroupResponseDto>,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async create(@Body() createChargeGroupDto: CreateChargeGroupDto) {
    const data =
      await this.chargeGroupService.createChargeGroup(createChargeGroupDto);
    return {
      success: true,
      message: 'Charge group created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Post(':chargeGroupId/charges')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new charge',
    description:
      'Create a new charge (chargeGroupId must be provided in request body)',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Charge created successfully',
    type: ApiResponseDto<ChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  async createCharge(
    @Param('chargeGroupId') chargeGroupId: string,
    @Body() createChargeDto: CreateChargeDto,
  ): Promise<ApiResponseDto<ChargeResponseDto>> {
    const charge = await this.chargeService.createCharge(
      chargeGroupId,
      createChargeDto,
    );
    return {
      success: true,
      message: 'Charge created successfully',
      data: charge as ChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Get(':chargeGroupId/charges')
  @ApiOperation({
    summary: 'Get all charges in a charge group',
    description: 'Retrieve all charges associated with a specific charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charges retrieved successfully',
    type: ApiResponseDto<ChargeResponseDto[]>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group not found',
    type: ApiErrorResponseDto,
  })
  async getChargesByChargeGroupId(
    @Param('chargeGroupId') chargeGroupId: string,
  ): Promise<ApiResponseDto<ChargeResponseDto[]>> {
    const charges =
      await this.chargeService.findChargesByChargeGroupId(chargeGroupId);
    return {
      success: true,
      message: 'Charges retrieved successfully',
      data: charges as ChargeResponseDto[],
      timestamp: Date.now(),
    };
  }

  @Patch(':chargeGroupId')
  @ApiOperation({ summary: 'Update charge group by ID' })
  @ApiParam({
    name: 'chargeGroupId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Charge group updated successfully',
    type: ApiResponseDto<ChargeGroupResponseDto>,
  })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async update(
    @Param('chargeGroupId') id: string,
    @Body() updateChargeGroupDto: UpdateChargeGroupDto,
  ) {
    const data = await this.chargeGroupService.updateChargeGroup(
      id,
      updateChargeGroupDto,
    );
    return {
      success: true,
      message: 'Charge group updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  @Delete(':chargeGroupId')
  @ApiOperation({ summary: 'Delete charge group by ID (soft delete)' })
  @ApiParam({
    name: 'chargeGroupId',
    type: String,
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @ApiResponse({
    status: 200,
    description: 'Charge group deleted successfully',
    type: ApiResponseDto<ChargeGroupResponseDto>,
  })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async remove(@Param('chargeGroupId') id: string) {
    const data = await this.chargeGroupService.deleteChargeGroup(id);
    return {
      success: true,
      message: 'Charge group deleted successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
