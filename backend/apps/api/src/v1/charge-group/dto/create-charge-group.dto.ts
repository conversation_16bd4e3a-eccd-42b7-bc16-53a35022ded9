import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, MaxLength, IsOptional } from 'class-validator';

export class CreateChargeGroupDto {
  @ApiProperty({
    example: 'Premium Charges',
    description: 'Name of the charge group',
    maxLength: 255,
  })
  @IsString({ message: 'Charge group name must be a string' })
  @IsNotEmpty({ message: 'Charge group name is required' })
  @MaxLength(255, { message: 'Charge group name cannot exceed 255 characters' })
  name!: string;

  @ApiProperty({
    example: 'Charges for premium services and features',
    description: 'Description of the charge group',
    maxLength: 1000,
    required: false,
  })
  @IsString({ message: 'Charge group description must be a string' })
  @IsOptional()
  @MaxLength(1000, {
    message: 'Charge group description cannot exceed 1000 characters',
  })
  description?: string;

  @ApiProperty({
    example: 'premium_charges',
    description:
      'Unique identifier for the charge group (auto-generated if not provided)',
    required: false,
  })
  @IsString({ message: 'Charge group identifier must be a string' })
  @IsOptional()
  @MaxLength(255, {
    message: 'Charge group identifier cannot exceed 255 characters',
  })
  identifier?: string;
}
