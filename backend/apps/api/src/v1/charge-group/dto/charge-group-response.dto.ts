import { ApiProperty } from '@nestjs/swagger';

export class ChargeGroupResponseDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Unique identifier of the charge group',
  })
  id!: string;

  @ApiProperty({
    example: 'Premium Charges',
    description: 'Name of the charge group',
  })
  name!: string;

  @ApiProperty({
    example: 'Charges for premium services and features',
    description: 'Description of the charge group',
    nullable: true,
  })
  description?: string | null;

  @ApiProperty({
    example: 'premium_charges',
    description: 'Unique identifier for the charge group',
    nullable: true,
  })
  identifier?: string | null;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Creation timestamp',
  })
  createdAt!: string;

  @ApiProperty({
    example: '2025-09-26T06:57:29.000Z',
    description: 'Last update timestamp',
  })
  updatedAt!: string;

  @ApiProperty({
    example: null,
    description: 'Deletion timestamp (null if not deleted)',
    nullable: true,
  })
  deletedAt?: string | null;
}
