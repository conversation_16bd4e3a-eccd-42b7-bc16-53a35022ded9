import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ChargeGroupController } from './charge-group.controller';
import { ChargeGroupModule as SharedChargeGroupModule } from '@shared/shared/modules/charge-group/charge-group.module';
import { ChargeModule } from '@shared/shared/modules/charge/charge.module';
import { ChargeController } from './charge/charge.controller';
import { ChargeGroupChargeController } from './charge/charge-group-charge.controller';

@Module({
  imports: [SharedChargeGroupModule, ChargeModule],
  controllers: [
    ChargeGroupController,
    ChargeController,
    ChargeGroupChargeController,
  ],
})
export class ApiChargeGroupModule {}
