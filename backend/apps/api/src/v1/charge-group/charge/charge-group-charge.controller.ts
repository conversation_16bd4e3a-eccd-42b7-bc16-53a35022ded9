import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Put,
  HttpCode,
  HttpStatus,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiResponse,
  ApiOperation,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { ChargeService } from '@shared/shared/modules/charge/charge.service';
import { AttachChargeDto } from './dto/attach-charge.dto';
import { UpdateChargePriorityDto } from './dto/update-charge-priority.dto';
import { ChargeGroupChargeResponseDto } from './dto/charge-group-charge-response.dto';
import {
  ApiResponseDto,
  ApiErrorResponseDto,
} from '../../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';

@ApiTags('Charge Group Charges')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('charge-group-charges')
export class ChargeGroupChargeController {
  constructor(private readonly chargeService: ChargeService) {}

  @Post('attach/:chargeGroupId')
  @ApiOperation({
    summary: 'Attach existing charge',
    description: 'Attach an existing charge to a charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Charge attached successfully',
    type: ApiResponseDto<ChargeGroupChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group or charge not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Charge is already attached to this group',
    type: ApiErrorResponseDto,
  })
  async attachCharge(
    @Param('chargeGroupId') chargeGroupId: string,
    @Body() attachChargeDto: AttachChargeDto,
  ): Promise<ApiResponseDto<ChargeGroupChargeResponseDto>> {
    const result = await this.chargeService.attachChargeToGroup(
      chargeGroupId,
      attachChargeDto,
    );
    return {
      success: true,
      message: 'Charge attached successfully',
      data: result as ChargeGroupChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Delete('detach/:chargeGroupId/:chargeId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Detach charge',
    description: 'Remove the relationship between a charge and charge group',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiParam({
    name: 'chargeId',
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Charge detached successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group or charge not found',
    type: ApiErrorResponseDto,
  })
  async detachCharge(
    @Param('chargeGroupId') chargeGroupId: string,
    @Param('chargeId') chargeId: string,
  ): Promise<void> {
    await this.chargeService.detachChargeFromGroup(chargeGroupId, chargeId);
  }

  @Put(':id/priority')
  @ApiOperation({
    summary: 'Update charge priority',
    description: 'Update the priority of a charge within a charge group',
  })
  @ApiParam({
    name: 'id',
    description: 'ID of the charge group charge relationship',
    example: '123e4567-e89b-12d3-a456-426614174002',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charge priority updated successfully',
    type: ApiResponseDto<ChargeGroupChargeResponseDto>,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group charge relationship not found',
    type: ApiErrorResponseDto,
  })
  async updatePriority(
    @Param('id') id: string,
    @Body() updateChargePriorityDto: UpdateChargePriorityDto,
  ): Promise<ApiResponseDto<ChargeGroupChargeResponseDto>> {
    const result = await this.chargeService.updateChargeGroupChargePriority(
      id,
      updateChargePriorityDto.priority,
    );
    return {
      success: true,
      message: 'Charge priority updated successfully',
      data: result as ChargeGroupChargeResponseDto,
      timestamp: Date.now(),
    };
  }

  @Get('charge-group/:chargeGroupId')
  @ApiOperation({
    summary: 'Get charges in group',
    description:
      'Retrieve all charges attached to a specific charge group, ordered by priority',
  })
  @ApiParam({
    name: 'chargeGroupId',
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Charges retrieved successfully',
    type: ApiResponseDto<ChargeGroupChargeResponseDto[]>,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Charge group not found',
    type: ApiErrorResponseDto,
  })
  async getChargesInGroup(
    @Param('chargeGroupId') chargeGroupId: string,
  ): Promise<ApiResponseDto<ChargeGroupChargeResponseDto[]>> {
    const charges = await this.chargeService.getChargesInGroup(chargeGroupId);
    return {
      success: true,
      message: 'Charges retrieved successfully',
      data: charges as ChargeGroupChargeResponseDto[],
      timestamp: Date.now(),
    };
  }
}
