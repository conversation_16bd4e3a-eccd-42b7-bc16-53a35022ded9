import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID, IsInt, IsOptional, Min } from 'class-validator';

export class CreateChargeGroupChargeDto {
  @ApiProperty({
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  chargeGroupId!: string;

  @ApiProperty({
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  chargeId!: string;

  @ApiPropertyOptional({
    description:
      'Priority for ordering charges within the group (lower numbers appear first)',
    example: 0,
    default: 0,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  priority?: number;
}
