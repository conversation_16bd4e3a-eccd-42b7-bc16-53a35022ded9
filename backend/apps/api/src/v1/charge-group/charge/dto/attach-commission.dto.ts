import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsNotEmpty } from 'class-validator';

export class AttachCommissionDto {
  @ApiProperty({
    description: 'ID of the charge to attach commission to',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  @IsNotEmpty()
  chargeId!: string;

  @ApiProperty({
    description: 'ID of the commission to attach',
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
  })
  @IsUUID()
  @IsNotEmpty()
  commissionId!: string;
}
