import { ApiProperty } from '@nestjs/swagger';

export class ChargeGroupChargeResponseDto {
  @ApiProperty({
    description: 'ID of the charge group charge relationship',
    example: '123e4567-e89b-12d3-a456-426614174002',
  })
  id!: string;

  @ApiProperty({
    description: 'ID of the charge group',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  chargeGroupId!: string;

  @ApiProperty({
    description: 'ID of the charge',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  chargeId!: string;

  @ApiProperty({
    description: 'Priority for ordering charges within the group',
    example: 0,
  })
  priority!: number;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt!: Date;
}
