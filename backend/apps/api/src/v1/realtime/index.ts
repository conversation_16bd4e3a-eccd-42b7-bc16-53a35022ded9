export { RealtimeGateway } from './realtime.gateway';
export { RealtimeModule } from './realtime.module';
export {
  DriverLocationDto,
  RideSubscriptionDto,
  RideStatusUpdate,
  DriverLocationBroadcast,
  DriverStatus,
} from './dto/realtime.dto';

export interface IRealtimeGateway {
  broadcastRideStatusUpdate(data: any, riderId?: string): Promise<void>;
  notifyDriverOfRideOffer(driverId: string, offer: any): Promise<void>;
  notifyDriverOfferTimeout(
    driverId: string,
    offerId: string,
    rideId: string,
  ): Promise<void>;
  broadcastDriverLocationToRide(
    rideId: string,
    locationData: any,
  ): Promise<void>;
  isDriverConnected(driverId: string): boolean;
  isRiderConnected(riderId: string): boolean;
  getStatistics(): {
    connectedRiders: number;
    connectedDrivers: number;
    totalConnections: number;
    activeRideSubscriptions: number;
    connectedSockets: number;
  };
}
