import { Injectable, Logger } from '@nestjs/common';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';
import { DriverLocationDto } from './dto/realtime.dto';
import { GlobalEventEmitterService, IEventName } from '@shared/shared';

@Injectable()
export class RealtimeService {
  private readonly logger = new Logger(RealtimeService.name);

  constructor(
    private readonly userProfileRepository: UserProfileRepository,
    private readonly globalEventEmitter: GlobalEventEmitterService,
  ) {}

  /**
   * Extract user details from authenticated socket with database lookup
   */
  async extractUserDetailsWithDbLookup(
    client: any,
  ): Promise<{ profileId: string; userType: string }> {
    try {
      const user = client?.user;
      if (!user || !user.profileId) {
        this.logger.warn('no profile data');
        return { profileId: '', userType: 'rider' };
      }
      const profileId = user.profileId;
      const userProfile = await this.userProfileRepository.findById(profileId, {
        include: {
          role: true,
        },
      });

      if (!userProfile) {
        this.logger.warn(`User profile not found for profileId: ${profileId}`);
        return { profileId, userType: 'rider' };
      }

      if (!userProfile.role) {
        this.logger.warn(`Role not found for user profile: ${profileId}`);
        return { profileId, userType: 'rider' }; // Default fallback
      }

      const roleName = userProfile.role.name.toLowerCase();
      this.logger.debug(`Found role '${roleName}' for profileId: ${profileId}`);

      // Determine user type based on role name
      if (roleName === 'driver') {
        return { profileId, userType: 'driver' };
      } else if (roleName === 'rider') {
        return { profileId, userType: 'rider' };
      } else if (roleName === 'super_admin') {
        return { profileId, userType: 'rider' }; // Treat admins as riders for realtime purposes
      } else {
        this.logger.warn(
          `Unknown role '${roleName}' for profileId: ${profileId}, defaulting to rider`,
        );
        return { profileId, userType: 'rider' };
      }
    } catch (error) {
      this.logger.error('Failed to extract user details:', error);
      throw error;
    }
  }

  /**
   * Send ride status notification
   */
  async sendRideStatusNotification(
    update: any,
    riderId: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `Sending ride status notification to rider ${riderId}: ${update.status}`,
      );
      // Implementation would send notification via Engagespot or other service
    } catch (error) {
      this.logger.error('Failed to send ride status notification:', error);
    }
  }

  /**
   * Send driver ride offer notification
   */
  async sendDriverRideOfferNotification(
    driverId: string,
    _offer: any,
  ): Promise<void> {
    try {
      this.logger.log(`Sending ride offer notification to driver ${driverId}`);
      // Implementation would send notification via Engagespot or other service
    } catch (error) {
      this.logger.error(
        'Failed to send driver ride offer notification:',
        error,
      );
    }
  }

  /**
   * Create driver location broadcast
   */
  async createDriverLocationBroadcast(
    _rideId: string,
    driverId: string,
    _location: any,
  ): Promise<void> {
    try {
      this.logger.log(
        `Creating driver location broadcast for driver ${driverId}`,
      );
      // Implementation would broadcast driver location to relevant riders
    } catch (error) {
      this.logger.error('Failed to create driver location broadcast:', error);
    }
  }

  /**
   * Update driver status
   */
  async updateDriverStatus(driverId: string, status: string): Promise<void> {
    try {
      this.logger.log(`Updating driver ${driverId} status to ${status}`);
      // Implementation would update driver status in database
    } catch (error) {
      this.logger.error('Failed to update driver status:', error);
    }
  }

  async processLocationUpdate(locationData: DriverLocationDto): Promise<void> {
    try {
      this.logger.log(
        `Processing location update for driver ${locationData.driverId}`,
      );

      if (!locationData.timestamp) {
        locationData.timestamp = new Date().toISOString();
      }

      await this.globalEventEmitter.emit(IEventName.DRIVER_LOCATION_RECEIVED, {
        driverId: locationData.driverId,
        lat: locationData.lat,
        lon: locationData.lon,
        timestamp: locationData.timestamp,
        city: locationData.city,
        status: locationData.status,
        rideId: locationData.rideId,
        speed: locationData.speed,
        bearing: locationData.bearing,
      });

      this.logger.log(
        `Successfully emitted location event for driver ${locationData.driverId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process location update for driver ${locationData.driverId}:`,
        error,
      );
      throw error;
    }
  }
}
