import {
  IsString,
  <PERSON>N<PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON>E<PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum DriverStatus {
  OFFLINE = 'offline',
  ONLINE = 'online',
  BUSY = 'busy',
  IN_RIDE = 'in_ride',
}

export class DriverLocationDto {
  @ApiProperty({
    description: 'Driver ID',
    example: 'driver_123',
  })
  @IsString()
  @IsNotEmpty()
  driverId!: string;

  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
  })
  @IsNumber()
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
  })
  @IsNumber()
  lon!: number;

  @ApiProperty({
    description: 'Timestamp of location update',
    example: '2023-08-06T09:16:42.000Z',
  })
  @IsString()
  @IsNotEmpty()
  timestamp!: string;

  @ApiProperty({
    description:
      'City where driver is located (optional - will be auto-detected)',
    example: 'bangalore',
    required: false,
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({
    description: 'Current driver status',
    enum: DriverStatus,
    example: DriverStatus.ONLINE,
  })
  @IsEnum(DriverStatus)
  status!: DriverStatus;

  @ApiProperty({
    description: 'Ride ID if driver is currently in a ride',
    example: 'ride_456',
    required: false,
  })
  @IsOptional()
  @IsString()
  rideId?: string;

  @IsOptional()
  @IsNumber()
  bearing?: number;

  @IsNumber()
  @IsOptional()
  speed?: number;
}

export class RideSubscriptionDto {
  @ApiProperty({
    description: 'Ride ID to subscribe to',
    example: 'ride_456',
  })
  @IsString()
  @IsNotEmpty()
  rideId!: string;

  @ApiProperty({
    description: 'User ID subscribing to the ride',
    example: 'user_123',
    required: false,
  })
  @IsOptional()
  @IsString()
  userId?: string;
}

// Interfaces for realtime updates
export interface RideStatusUpdate {
  rideId: string;
  status:
    | 'requested'
    | 'processing'
    | 'matched'
    | 'accepted'
    | 'driver_enroute'
    | 'driver_arrived'
    | 'trip_started'
    | 'trip_completed'
    | 'cancelled'
    | 'failed'
    | 'unassigned'
    | 'searching_drivers';
  message: string;
  timestamp: string;
  metadata: any;
  driver?: any;
}

export interface DriverLocationBroadcast {
  rideId: string;
  driverId: string;
  location: {
    lat: number;
    lng: number;
    heading?: number;
    speed?: number;
    timestamp: string;
  };
}
