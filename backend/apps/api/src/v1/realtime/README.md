# Realtime Module

The Realtime Module consolidates all real-time WebSocket functionality for the Tukxi ride-hailing platform, combining the previously separate location ingestion and ride status management systems into a unified, scalable solution.

## Overview

This module provides comprehensive real-time capabilities including:

- **Driver Location Ingestion**: Real-time location updates from drivers
- **Location Broadcasting**: Live location sharing during active rides  
- **Ride Status Management**: Real-time ride status updates and notifications
- **Ride Offer Management**: Driver ride offer notifications and timeouts
- **Driver Tracking**: Real-time driver location tracking for riders
- **Connection Management**: Robust WebSocket connection handling for both drivers and riders

## Architecture

### Components

- **RealtimeGateway**: Main WebSocket gateway handling all real-time connections
- **RealtimeService**: Business logic for location processing and notifications  
- **RealtimeModule**: NestJS module configuration and dependency injection
- **DTOs**: Type-safe data transfer objects for all real-time operations

### Key Features

- **Unified Connection Management**: Single gateway for all user types (drivers/riders)
- **Room-based Broadcasting**: Efficient message routing using Socket.io rooms
- **JWT Authentication**: Secure WebSocket connections with token validation
- **Token Refresh Support**: Seamless token renewal during long-lived connections
- **Error Handling**: Comprehensive error handling and logging
- **Statistics & Monitoring**: Built-in connection statistics and health checks

## WebSocket Events

### Connection Events
- `connected` - Successful connection confirmation
- `ping/pong` - Connection health checks

### Location Events (Drivers)
- `location_update` - Submit location update
- `location_ack` - Location update confirmation
- `location_error` - Location update errors

### Ride Status Events
- `subscribe_to_ride` - Subscribe to ride updates
- `unsubscribe_from_ride` - Unsubscribe from ride updates
- `get_ride_status` - Get current ride status
- `ride_status_updated` - Receive ride status updates

### Driver Location Broadcasting
- `driver_location_updated` - Real-time driver location updates

### Ride Offers (Drivers)
- `ride_offer_received` - New ride offer notification
- `ride_offer_timeout` - Ride offer expiration

### Token Management
- `token_refresh` - Refresh JWT token
- `token_refresh_success/error` - Token refresh results

## Usage

### Client Connection (Driver)

```typescript
import io from 'socket.io-client';

// Method 1: Using auth object (preferred)
const socket = io('ws://localhost:3000/realtime', {
  auth: {
    token: 'your-jwt-token'
  }
});

// Method 2: Using Authorization header
const socket = io('ws://localhost:3000/realtime', {
  extraHeaders: {
    'Authorization': 'Bearer your-jwt-token'
  }
});

// Method 3: Using query parameters (fallback)
const socket = io('ws://localhost:3000/realtime?token=your-jwt-token');

socket.on('connected', (data) => {
  console.log('Connected:', data);
  console.log('User Type:', data.userType); // 'driver' or 'rider'
  console.log('Profile ID:', data.profileId);
  console.log('Capabilities:', data.capabilities);
});

// Handle authentication required
socket.on('auth_required', (data) => {
  console.log('Authentication required:', data.message);
  // Reconnect with proper token
});

// Send location updates
socket.emit('location_update', {
  lat: 12.9716,
  lon: 77.5946,
  timestamp: new Date().toISOString(),
  city: 'bangalore',
  status: 'online',
  rideId: 'ride_123' // if in active ride
});

socket.on('location_ack', (response) => {
  console.log('Location updated:', response);
});
```

### Client Connection (Rider)

```typescript
const socket = io('ws://localhost:3000/realtime', {
  auth: {
    token: 'your-jwt-token'
  }
});

// Subscribe to ride updates
socket.emit('subscribe_to_ride', {
  rideId: 'ride_456'
});

// Listen for driver location updates
socket.on('driver_location_updated', (locationData) => {
  console.log('Driver location:', locationData.location);
  // Update map with driver position
});

// Listen for ride status changes
socket.on('ride_status_updated', (statusUpdate) => {
  console.log('Ride status:', statusUpdate.status, statusUpdate.message);
});
```

### Server-side Usage

```typescript
import { RealtimeGateway } from './realtime.gateway';

@Injectable()
export class YourService {
  constructor(
    @Inject('IRealtimeGateway')
    private readonly realtimeGateway: RealtimeGateway,
  ) {}

  async notifyRideStatusChange(rideId: string, status: string) {
    await this.realtimeGateway.broadcastRideStatusUpdate({
      rideId,
      status,
      message: 'Ride status updated',
      timestamp: new Date().toISOString()
    });
  }

  async sendRideOffer(driverId: string, offer: RideOfferNotification) {
    await this.realtimeGateway.notifyDriverOfRideOffer(driverId, offer);
  }
}
```

## Configuration

The module uses the following configuration:

- **Namespace**: `/realtime` 
- **Transports**: WebSocket with polling fallback
- **CORS**: Configurable origin policy
- **Authentication**: JWT-based with automatic refresh
- **Compression**: Enabled for better performance

## Monitoring

### Statistics Available

```typescript
const stats = realtimeGateway.getStatistics();
console.log({
  connectedDrivers: stats.connectedDrivers,
  connectedRiders: stats.connectedRiders, 
  totalConnections: stats.totalConnections,
  activeRideSubscriptions: stats.activeRideSubscriptions,
  connectedSockets: stats.connectedSockets
});
```

### Health Checks

```typescript
if (realtimeGateway.isHealthy()) {
  console.log('Realtime service is healthy');
}
```

## Error Handling

The module implements comprehensive error handling:

- **Connection Errors**: Automatic disconnection of invalid connections
- **Authentication Errors**: Token validation and refresh mechanisms
- **Message Errors**: Validation and error responses for invalid messages
- **Network Errors**: Graceful handling of network interruptions
- **Rate Limiting**: Built-in protection against message flooding

## Migration from Old Modules

This module replaces the previous `DriverLocationModule` and `RideStatusModule`. Key migration points:

1. **Unified Namespace**: All connections now use `/realtime` instead of separate namespaces
2. **Enhanced Authentication**: Improved JWT handling with refresh support
3. **Consolidated Events**: Streamlined event names and payloads
4. **Better Error Handling**: More robust error responses and logging
5. **Performance Improvements**: Optimized connection management and broadcasting

## Testing

Run the integration tests:

```bash
npm test -- realtime.integration.spec.ts
```

The test suite covers:
- Connection establishment and authentication
- Location update processing  
- Ride subscription management
- Real-time message broadcasting
- Error handling scenarios
- Token refresh functionality

## Dependencies

- `@nestjs/websockets` - WebSocket gateway support
- `@nestjs/platform-socket.io` - Socket.io integration
- `socket.io` - WebSocket server implementation
- `@shared/shared` - Shared utilities and services

## Performance Considerations

- **Room-based Broadcasting**: Efficient message targeting using Socket.io rooms
- **Connection Pooling**: Optimized connection management  
- **Message Queuing**: Reliable message delivery with queuing
- **Memory Management**: Automatic cleanup of disconnected clients
- **Horizontal Scaling**: Redis adapter support for multi-instance deployment

## Security

- **JWT Authentication**: Secure token-based authentication
- **Input Validation**: Comprehensive input validation using class-validator
- **Rate Limiting**: Protection against message flooding
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Connection Limits**: Configurable maximum connection limits

## Future Enhancements

- **Message Persistence**: Offline message storage and delivery
- **Push Notifications**: Integration with mobile push notification services
- **Analytics Integration**: Real-time analytics and metrics collection
- **Load Balancing**: Advanced load balancing and failover mechanisms
- **Caching Layer**: Redis-based caching for improved performance
