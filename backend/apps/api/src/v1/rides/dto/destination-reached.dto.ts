import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class LocationDto {
  @ApiProperty({
    description: 'Latitude coordinate',
    example: 12.9716,
  })
  @IsNotEmpty()
  @IsNumber()
  lat!: number;

  @ApiProperty({
    description: 'Longitude coordinate',
    example: 77.5946,
  })
  @IsNotEmpty()
  @IsNumber()
  lng!: number;
}

export class DestinationReachedDto {
  @ApiProperty({
    description: 'Current location coordinates',
    type: LocationDto,
  })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => LocationDto)
  location!: LocationDto;
}

export class DestinationReachedResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Destination reached successfully' })
  message!: string;

  @ApiProperty({
    description: 'Updated ride data',
  })
  data!: any;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
