import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

export class ProductDetailsDto {
  @ApiProperty({ example: 'TukTuk Standard' })
  name!: string;

  @ApiProperty({
    example: 'https://s3.amazonaws.com/bucket/icon.png',
    nullable: true,
  })
  icon!: string | null;
}

export class DriverDetailsDto {
  @ApiProperty({ example: 'driver-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;

  @ApiProperty({
    example: 'https://s3.amazonaws.com/bucket/profile.jpg',
    nullable: true,
  })
  profilePictureUrl!: string | null;

  @ApiProperty({
    example: 4.5,
    nullable: true,
    description: 'Average rating of the driver',
  })
  rating!: number | null;
}

export class RiderDetailsDto {
  @ApiProperty({ example: 'rider-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'Jane' })
  firstName!: string;

  @ApiProperty({ example: 'Smith' })
  lastName!: string;

  @ApiProperty({
    example: 'https://s3.amazonaws.com/bucket/profile.jpg',
    nullable: true,
  })
  profilePictureUrl!: string | null;
}

export class VehicleTypeDetailsDto {
  @ApiProperty({ example: 'Auto Rickshaw' })
  name!: string;
}

export class DriverVehicleDetailsDto {
  @ApiProperty({ example: 'KA01AB1234' })
  vehicleNumber!: string;

  @ApiProperty({ type: VehicleTypeDetailsDto })
  vehicleType!: VehicleTypeDetailsDto;
}

export class LocationDetailsDto {
  @ApiProperty({ example: 12.9716 })
  lat!: number;

  @ApiProperty({ example: 77.5946 })
  lng!: number;

  @ApiPropertyOptional({ example: 'Koramangala, Bangalore' })
  address?: string;
}

export class RatingDetailsDto {
  @ApiProperty({
    example: 4.2,
    nullable: true,
    description: 'Average rating',
  })
  rating!: number | null;
}

export class RideDetailsForRiderDto {
  @ApiProperty({ example: 'ride-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ type: LocationDetailsDto })
  pickupLocation!: LocationDetailsDto;

  @ApiProperty({ type: LocationDetailsDto })
  destinationLocation!: LocationDetailsDto;

  @ApiPropertyOptional({
    type: [LocationDetailsDto],
    description: 'Stops along the route',
  })
  stops?: LocationDetailsDto[] | null;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({ example: '2024-01-15T11:15:00.000Z' })
  completedAt?: Date | null;

  @ApiProperty({ type: ProductDetailsDto })
  product!: ProductDetailsDto;

  @ApiPropertyOptional({ type: DriverDetailsDto })
  driver?: DriverDetailsDto | null;

  @ApiPropertyOptional({ type: DriverVehicleDetailsDto })
  driverVehicle?: DriverVehicleDetailsDto | null;

  @ApiProperty({ type: RatingDetailsDto })
  rider!: RatingDetailsDto;
}

export class RideDetailsForDriverDto {
  @ApiProperty({ example: 'ride-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ type: LocationDetailsDto })
  pickupLocation!: LocationDetailsDto;

  @ApiProperty({ type: LocationDetailsDto })
  destinationLocation!: LocationDetailsDto;

  @ApiPropertyOptional({
    type: [LocationDetailsDto],
    description: 'Stops along the route',
  })
  stops?: LocationDetailsDto[] | null;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({ example: '2024-01-15T11:15:00.000Z' })
  completedAt?: Date | null;

  @ApiProperty({ type: ProductDetailsDto })
  product!: ProductDetailsDto;

  @ApiPropertyOptional({ type: RiderDetailsDto })
  rider?: RiderDetailsDto | null;

  @ApiPropertyOptional({ type: DriverVehicleDetailsDto })
  driverVehicle?: DriverVehicleDetailsDto | null;

  @ApiProperty({ type: RatingDetailsDto })
  driver!: RatingDetailsDto;
}

export class RideDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Ride details retrieved successfully' })
  message!: string;

  @ApiProperty({
    oneOf: [
      { $ref: '#/components/schemas/RideDetailsForRiderDto' },
      { $ref: '#/components/schemas/RideDetailsForDriverDto' },
    ],
  })
  data!: RideDetailsForRiderDto | RideDetailsForDriverDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class RideLifecycleItemDto {
  @ApiProperty({ example: 'lifecycle-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.REQUESTED,
  })
  status!: RideStatus;

  @ApiPropertyOptional({
    description: 'Additional metadata for the lifecycle entry',
    example: { notes: 'Ride requested by rider', mode: 'now' },
  })
  meta?: any;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;
}

export class RideLifecycleResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Ride lifecycle retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: [RideLifecycleItemDto],
    description: 'Ride lifecycle history ordered by creation date ascending',
  })
  data!: RideLifecycleItemDto[];

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
