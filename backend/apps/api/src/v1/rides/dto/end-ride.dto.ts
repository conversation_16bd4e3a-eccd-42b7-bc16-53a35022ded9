import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { LocationDto } from './create-ride.dto';

export class EndRideDto {
  @ApiProperty({
    type: LocationDto,
    required: false,
    description:
      'Optional new destination to replace the existing destination before ending the ride',
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => LocationDto)
  newDestination?: LocationDto;
}
