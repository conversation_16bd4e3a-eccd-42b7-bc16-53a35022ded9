import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsN<PERSON>ber, Min } from 'class-validator';
import { Transform } from 'class-transformer';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

export class RideHistoryQueryDto {
  @ApiPropertyOptional({
    description: 'Comma-separated list of ride statuses to filter by',
    example: 'completed,cancelled',
    enum: RideStatus,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Start date for filtering rides (ISO 8601)',
    type: 'string',
    format: 'date-time',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  fromDate?: Date;

  @ApiPropertyOptional({
    description: 'End date for filtering rides (ISO 8601)',
    type: 'string',
    format: 'date-time',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  toDate?: Date;
}

export class ProductHistoryDto {
  @ApiProperty({ example: 'TukTuk Standard' })
  name!: string;

  @ApiProperty({
    example: 'https://s3.amazonaws.com/bucket/icon.png',
    nullable: true,
  })
  icon!: string | null;
}

export class DriverHistoryDto {
  @ApiProperty({ example: 'driver-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;
}

export class RiderHistoryDto {
  @ApiProperty({ example: 'Jane' })
  firstName!: string;

  @ApiProperty({ example: 'Smith' })
  lastName!: string;
}

export class VehicleTypeHistoryDto {
  @ApiProperty({ example: 'Auto Rickshaw' })
  name!: string;
}

export class DriverVehicleHistoryDto {
  @ApiProperty({ example: 'KA01AB1234' })
  vehicleNumber!: string;

  @ApiProperty({ type: VehicleTypeHistoryDto })
  vehicleType!: VehicleTypeHistoryDto;
}

export class LocationHistoryDto {
  @ApiProperty({ example: 12.9716 })
  lat!: number;

  @ApiProperty({ example: 77.5946 })
  lng!: number;

  @ApiPropertyOptional({ example: 'Koramangala, Bangalore' })
  address?: string;
}

export class RideHistoryItemDto {
  @ApiProperty({ example: 'ride-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ type: LocationHistoryDto })
  pickupLocation!: LocationHistoryDto;

  @ApiProperty({ type: LocationHistoryDto })
  destinationLocation!: LocationHistoryDto;

  @ApiPropertyOptional({
    type: [LocationHistoryDto],
    description: 'Stops along the route',
  })
  stops?: LocationHistoryDto[] | null;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({ example: '2024-01-15T11:15:00.000Z' })
  completedAt?: Date | null;

  @ApiProperty({ type: ProductHistoryDto })
  product!: ProductHistoryDto;
}

export class RiderRideHistoryItemDto extends RideHistoryItemDto {
  @ApiPropertyOptional({ type: DriverHistoryDto })
  driver?: DriverHistoryDto | null;

  @ApiPropertyOptional({ type: DriverVehicleHistoryDto })
  driverVehicle?: DriverVehicleHistoryDto | null;
}

export class DriverRideHistoryItemDto extends RideHistoryItemDto {
  @ApiPropertyOptional({ type: RiderHistoryDto })
  rider?: RiderHistoryDto | null;
}

export class RideHistoryResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Ride history retrieved successfully' })
  message!: string;

  @ApiProperty({
    type: 'object',
    properties: {
      rides: {
        type: 'array',
        items: { $ref: '#/components/schemas/RiderRideHistoryItemDto' },
      },
      total: { type: 'number', example: 25 },
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  data!: RiderRideHistoryItemDto[] | DriverRideHistoryItemDto[];

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
  @ApiProperty({
    description: 'Pagination details',
    type: 'object',
    properties: {
      total: { type: 'number', example: 25 },
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      totalPages: { type: 'number', example: 3 },
    },
  })
  meta!: any;
}
