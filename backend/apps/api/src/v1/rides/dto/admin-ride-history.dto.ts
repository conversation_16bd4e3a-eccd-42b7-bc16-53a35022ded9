import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, IsUUID } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { RideStatus } from '@shared/shared/modules/ride-matching/constants';

export class AdminRideHistoryQueryDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Filter by ride status',
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  @IsOptional()
  @IsString()
  status?: string;

  @ApiPropertyOptional({
    description: 'Search by rider name (first name or last name)',
    example: '<PERSON>',
  })
  @IsOptional()
  @IsString()
  riderName?: string;

  @ApiPropertyOptional({
    description: 'Search by driver name (first name or last name)',
    example: 'Jane',
  })
  @IsOptional()
  @IsString()
  driverName?: string;

  @ApiPropertyOptional({
    description: 'Filter by rider ID',
    example: 'rider-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  riderId?: string;

  @ApiPropertyOptional({
    description: 'Filter by driver ID',
    example: 'driver-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  driverId?: string;

  @ApiPropertyOptional({
    description: 'Filter by product ID',
    example: 'product-uuid-123',
  })
  @IsOptional()
  @IsUUID()
  productId?: string;

  @ApiPropertyOptional({
    description: 'Start date for filtering rides (ISO 8601)',
    type: 'string',
    format: 'date-time',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  fromDate?: Date;

  @ApiPropertyOptional({
    description: 'End date for filtering rides (ISO 8601)',
    type: 'string',
    format: 'date-time',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @Transform(({ value }) => (value ? new Date(value) : undefined))
  toDate?: Date;

  @ApiPropertyOptional({
    description: 'Filter by vehicle number (partial match)',
    example: 'ABC123',
  })
  @IsOptional()
  @IsString()
  vehicleNumber?: string;
}

export class AdminRiderDto {
  @ApiProperty({ example: 'rider-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'John' })
  firstName!: string;

  @ApiProperty({ example: 'Doe' })
  lastName!: string;
}

export class AdminDriverDto {
  @ApiProperty({ example: 'driver-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'Jane' })
  firstName!: string;

  @ApiProperty({ example: 'Smith' })
  lastName!: string;
}

export class AdminProductDto {
  @ApiProperty({ example: 'product-uuid-123' })
  id!: string;

  @ApiProperty({ example: 'TukTuk Standard' })
  name!: string;

  @ApiPropertyOptional({
    example: 'https://s3.amazonaws.com/bucket/icon.png',
    nullable: true,
  })
  icon?: string | null;
}

export class AdminRideHistoryItemDto {
  @ApiProperty({ example: 'ride-uuid-123' })
  id!: string;

  @ApiProperty({
    enum: RideStatus,
    example: RideStatus.TRIP_COMPLETED,
  })
  status!: RideStatus;

  @ApiProperty({ example: '2024-01-15T10:30:00.000Z' })
  createdAt!: Date;

  @ApiPropertyOptional({ example: '2024-01-15T11:15:00.000Z' })
  completedAt?: Date | null;

  @ApiProperty({ type: AdminRiderDto })
  rider!: AdminRiderDto;

  @ApiPropertyOptional({ type: AdminDriverDto })
  driver?: AdminDriverDto | null;

  @ApiProperty({ type: AdminProductDto })
  product!: AdminProductDto;
}

export class AdminRideHistoryResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Rides retrieved successfully' })
  message!: string;

  @ApiProperty({ type: [AdminRideHistoryItemDto] })
  data!: AdminRideHistoryItemDto[];

  @ApiProperty({
    description: 'Pagination metadata',
    type: 'object',
    properties: {
      page: { type: 'number', example: 1 },
      limit: { type: 'number', example: 10 },
      total: { type: 'number', example: 25 },
      totalPages: { type: 'number', example: 3 },
      hasNextPage: { type: 'boolean', example: true },
      hasPreviousPage: { type: 'boolean', example: false },
    },
  })
  meta!: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
