import { ApiProperty } from '@nestjs/swagger';

export class PermissionDto {
  @ApiProperty({
    description: 'Permission ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Permission name',
    example: 'users:create',
  })
  name!: string;

  @ApiProperty({
    description: 'Permission description',
    example: 'Create new users',
    required: false,
  })
  description?: string | null;

  @ApiProperty({
    description: 'Resource this permission applies to',
    example: 'users',
  })
  resource!: string;

  @ApiProperty({
    description: 'Action this permission allows',
    example: 'create',
  })
  action!: string;

  @ApiProperty({
    description:
      'Whether this permission is added to the role (only present when rideId is provided)',
    example: true,
    required: false,
  })
  isAddedRole?: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt!: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt!: Date;
}

export class PermissionListResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Permissions retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'List of permissions',
    type: [PermissionDto],
  })
  data!: PermissionDto[];

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}
