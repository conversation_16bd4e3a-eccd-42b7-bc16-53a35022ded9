import { ApiProperty } from '@nestjs/swagger';

export class RoleDto {
  @ApiProperty({
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id!: string;

  @ApiProperty({
    description: 'Role name (unique identifier)',
    example: 'custom_manager',
  })
  name!: string;

  @ApiProperty({
    description: 'Role description',
    example: 'Custom manager role with specific permissions',
    nullable: true,
  })
  description!: string | null;

  @ApiProperty({
    description: 'Whether this is a custom role',
    example: true,
  })
  isCustomRole!: boolean;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  createdAt!: string;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-01T00:00:00.000Z',
  })
  updatedAt!: string;
}

export class RoleResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Role retrieved successfully' })
  message!: string;

  @ApiProperty({ type: RoleDto })
  data!: RoleDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class RoleMetaDto {
  @ApiProperty({ description: 'Current page number', example: 1 })
  page!: number;

  @ApiProperty({ description: 'Number of items per page', example: 10 })
  limit!: number;

  @ApiProperty({ description: 'Total number of items', example: 100 })
  total!: number;

  @ApiProperty({ description: 'Total number of pages', example: 10 })
  totalPages!: number;
}

export class RolePaginatedResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Roles retrieved successfully' })
  message!: string;

  @ApiProperty({ type: [RoleDto] })
  data!: RoleDto[];

  @ApiProperty({ type: RoleMetaDto })
  meta!: RoleMetaDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
