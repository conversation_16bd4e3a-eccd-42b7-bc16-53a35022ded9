import {
  <PERSON><PERSON><PERSON>al,
  Is<PERSON>tring,
  IsN<PERSON>ber,
  Is<PERSON>num,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UserProfileStatus } from '@shared/shared/repositories/models/userProfile.model';

export class AdminListQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @IsEnum(UserProfileStatus)
  status?: UserProfileStatus;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number = 10;

  @IsOptional()
  @IsUUID()
  exceptCityId?: string;
}
