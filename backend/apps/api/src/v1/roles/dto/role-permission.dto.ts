import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsUUID, ArrayNotEmpty } from 'class-validator';

export class AddRemovePermissionsDto {
  @ApiProperty({
    description: 'Array of permission IDs to add or remove',
    example: [
      '123e4567-e89b-12d3-a456-************',
      '987fcdeb-51a2-43d1-9f4e-123456789abc',
    ],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true })
  permissionIds!: string[];
}

export class RolePermissionResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Permissions added to role successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}

export class RolePermissionListResponseDto {
  @ApiProperty({
    description: 'Success status',
    example: true,
  })
  success!: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Role permissions retrieved successfully',
  })
  message!: string;

  @ApiProperty({
    description: 'List of permissions for the role',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string', example: '123e4567-e89b-12d3-a456-************' },
        name: { type: 'string', example: 'users:create' },
        description: { type: 'string', example: 'Create new users' },
        resource: { type: 'string', example: 'users' },
        action: { type: 'string', example: 'create' },
        createdAt: { type: 'string', example: '2023-01-01T00:00:00.000Z' },
        updatedAt: { type: 'string', example: '2023-01-01T00:00:00.000Z' },
      },
    },
  })
  data!: any[];

  @ApiProperty({
    description: 'Response timestamp',
    example: 1640995200000,
  })
  timestamp!: number;
}
