import { Module } from '@nestjs/common';
import { RoleModule as SharedRoleModule } from '@shared/shared/modules/role/role.module';
import { RoleController } from './role.controller';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [SharedRoleModule],
  providers: [CityAdminRepository],
  controllers: [RoleController],
})
export class ApiRoleModule {}
