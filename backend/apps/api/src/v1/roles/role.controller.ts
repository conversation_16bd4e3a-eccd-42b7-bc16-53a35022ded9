import {
  Controller,
  Get,
  Post,
  Patch,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { RoleService } from '@shared/shared/modules/role/role.service';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import {
  CreateRoleDto,
  UpdateRoleDto,
  RoleResponseDto,
  RolePaginatedResponseDto,
  RolePaginationDto,
} from './dto';
import {
  AddRemovePermissionsDto,
  RolePermissionResponseDto,
  RolePermissionListResponseDto,
} from './dto/role-permission.dto';
import { AdminListQueryDto } from './dto/admin-list-query.dto';
import { AdminPaginatedResponseDto } from './dto/admin-paginated-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { CaslAbilityGuard } from '../../common/guards/casl-ability.guard';
import { Ability } from '@shared/shared/casl';

@ApiTags('Roles')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@Controller('roles')
export class RoleController {
  constructor(private readonly roleService: RoleService) {}

  // ==================== GET APIs ====================

  @Get()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of custom roles',
    description:
      'Retrieve a paginated list of custom roles with optional search',
  })
  @ApiResponse({
    status: 200,
    description: 'Roles retrieved successfully',
    type: RolePaginatedResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid query parameters',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:list')
  async getRoles(@Query() query: RolePaginationDto) {
    const result = await this.roleService.getCustomRoles(query);
    return {
      success: true,
      message: 'Roles retrieved successfully',
      data: result.data,
      meta: result.meta,
      timestamp: Date.now(),
    };
  }

  // ==================== GET by ID APIs ====================

  @Get(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get role by ID',
    description: 'Retrieve a specific custom role by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role retrieved successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  async getRoleById(@Param('id', ParseUUIDPipe) id: string) {
    const role = await this.roleService.getCustomRoleById(id);
    return {
      success: true,
      message: 'Role retrieved successfully',
      data: role,
      timestamp: Date.now(),
    };
  }

  @Get(':id/admins')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of admins for a role',
    description:
      'Retrieve a paginated list of admins assigned to the specified role with optional search and status filters',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by name or email',
    example: 'john',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by admin status',
    enum: ['active', 'pending', 'disabled', 'inactive', 'invited'],
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiQuery({
    name: 'exceptCityId',
    required: false,
    description: 'Exclude admins assigned to this city ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Admins retrieved successfully',
    type: AdminPaginatedResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  async getAdminsForRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Query() query: AdminListQueryDto,
  ) {
    const result = await this.roleService.getAdminsForRole(id, query);
    return {
      success: true,
      message: 'Admins retrieved successfully',
      data: result.admins,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  @Get('identifier/:identifier/admins')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get paginated list of admins for a role by identifier',
    description:
      'Retrieve a paginated list of admins assigned to the role identified by its identifier with optional search and status filters',
  })
  @ApiParam({
    name: 'identifier',
    description: 'Role identifier',
    example: 'city_admin',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search by name or email',
    example: 'john',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'Filter by admin status',
    enum: ['active', 'pending', 'disabled', 'inactive', 'invited'],
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number',
    example: 1,
  })
  @ApiQuery({
    name: 'exceptCityId',
    required: false,
    description: 'Exclude admins assigned to this city ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Number of items per page',
    example: 10,
  })
  @ApiResponse({
    status: 200,
    description: 'Admins retrieved successfully',
    type: AdminPaginatedResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  async getAdminsForRoleByIdentifier(
    @Param('identifier') identifier: string,
    @Query() query: AdminListQueryDto,
  ) {
    const role = await this.roleService.getRoleByIdentifier(identifier);
    const result = await this.roleService.getAdminsForRole(role.id, query);
    return {
      success: true,
      message: 'Admins retrieved successfully',
      data: result.admins,
      meta: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
      timestamp: Date.now(),
    };
  }

  // ==================== POST APIs ====================

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new custom role',
    description: 'Create a new custom role with unique name validation',
  })
  @ApiResponse({
    status: 201,
    description: 'Role created successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Role name already exists',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:create')
  async createRole(@Body() createRoleDto: CreateRoleDto) {
    const role = await this.roleService.createCustomRole(createRoleDto);
    return {
      success: true,
      message: 'Role created successfully',
      data: role,
      timestamp: Date.now(),
    };
  }

  // ==================== PATCH APIs ====================

  @Patch(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update a custom role',
    description: 'Update a custom role with unique name validation',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role updated successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Role name already exists',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:edit')
  async updateRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ) {
    const role = await this.roleService.updateCustomRole(id, updateRoleDto);
    return {
      success: true,
      message: 'Role updated successfully',
      data: role,
      timestamp: Date.now(),
    };
  }

  // ==================== DELETE APIs ====================

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete a custom role',
    description: 'Soft delete a custom role by its ID',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role deleted successfully',
    type: RoleResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:delete')
  async deleteRole(@Param('id', ParseUUIDPipe) id: string) {
    const role = await this.roleService.deleteCustomRole(id);
    return {
      success: true,
      message: 'Role deleted successfully',
      data: role,
      timestamp: Date.now(),
    };
  }

  // ==================== PERMISSION MANAGEMENT APIs ====================

  @Post(':id/add-permissions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Add permissions to a role',
    description: 'Add multiple permissions to a custom role',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions added to role successfully',
    type: RolePermissionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid permission IDs',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:manage_permissions')
  async addPermissionsToRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() addPermissionsDto: AddRemovePermissionsDto,
  ): Promise<RolePermissionResponseDto> {
    await this.roleService.addPermissionsToRole(
      id,
      addPermissionsDto.permissionIds,
    );
    return {
      success: true,
      message: 'Permissions added to role successfully',
      timestamp: Date.now(),
    };
  }

  @Post(':id/remove-permissions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Remove permissions from a role',
    description: 'Remove multiple permissions from a custom role',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Permissions removed from role successfully',
    type: RolePermissionResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request - Invalid permission IDs',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:manage_permissions')
  async removePermissionsFromRole(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() removePermissionsDto: AddRemovePermissionsDto,
  ): Promise<RolePermissionResponseDto> {
    await this.roleService.removePermissionsFromRole(
      id,
      removePermissionsDto.permissionIds,
    );
    return {
      success: true,
      message: 'Permissions removed from role successfully',
      timestamp: Date.now(),
    };
  }

  @Get(':id/list-permissions')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Get permissions for a role',
    description: 'Retrieve all permissions assigned to a custom role',
  })
  @ApiParam({
    name: 'id',
    description: 'Role ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 200,
    description: 'Role permissions retrieved successfully',
    type: RolePermissionListResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Role not found',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('role:manage_permissions')
  async getRolePermissions(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<RolePermissionListResponseDto> {
    const permissions = await this.roleService.getRolePermissions(id);
    return {
      success: true,
      message: 'Role permissions retrieved successfully',
      data: permissions,
      timestamp: Date.now(),
    };
  }
}
