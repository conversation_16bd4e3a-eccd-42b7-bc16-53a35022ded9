import { ApiProperty } from '@nestjs/swagger';
import {
  ZoneTypeResponseDto,
  ZoneTypeResponseDtoClass,
} from '@shared/shared/modules/zone-type/zone-type-response.dto';

export class ZoneTypeListResponseDto {
  @ApiProperty({
    description: 'Array of zone types',
    type: [ZoneTypeResponseDtoClass],
  })
  data!: ZoneTypeResponseDto[];

  @ApiProperty({
    description: 'Total number of zone types',
    example: 25,
  })
  total!: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
  })
  page!: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
  })
  limit!: number;
}

export class CreateZoneTypeResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone type created successfully' })
  message!: string;

  @ApiProperty({ type: ZoneTypeResponseDtoClass })
  data!: ZoneTypeResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class ZoneTypeDetailsResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone type retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ZoneTypeResponseDtoClass })
  data!: ZoneTypeResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}

export class ZoneTypePaginatedResponseDto {
  @ApiProperty({ example: true })
  success!: boolean;

  @ApiProperty({ example: 'Zone types retrieved successfully' })
  message!: string;

  @ApiProperty({ type: ZoneTypeListResponseDto })
  data!: ZoneTypeListResponseDto;

  @ApiProperty({ example: 1640995200000 })
  timestamp!: number;
}
