import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
  ParseUUIDPipe,
  UseGuards,
  Logger,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ZoneAlgorithm } from '@prisma/client';
import { ZoneTypePaginationDto } from './dto/zone-type-pagination.dto';
import {
  CreateZoneTypeResponseDto,
  ZoneTypeDetailsResponseDto,
  ZoneTypePaginatedResponseDto,
} from './dto/zone-type-response.dto';
import { ApiErrorResponseDto } from '../../docs/swagger/common-responses.dto';
import { JwtAuthGuard } from '@shared/shared/modules/auth/guards/jwt-auth.guard';
import { ZoneTypeService } from '@shared/shared/modules/zone-type/zone-type.service';
import {
  ZoneTypeResponseDto,
  ZoneTypeResponseDtoClass,
} from '@shared/shared/modules/zone-type/zone-type-response.dto';
import {
  CreateZoneTypeInputDto,
  UpdateZoneTypeInputDto,
} from '@shared/shared/modules/zone-type/zone-type-input.dto';
import { CaslAbilityGuard } from '../../common/guards/casl-ability.guard';
import { Ability } from '@shared/shared/casl';
import { ChangeZoneTypeStatusDto } from './dto/change-zone-type-status.dto';

@ApiTags('Zone Types')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
@Controller('zone-types')
export class ZoneTypeController {
  private readonly logger = new Logger(ZoneTypeController.name);

  constructor(private readonly zoneTypeService: ZoneTypeService) {}

  /**
   * Get all zone types with pagination
   */
  @Get()
  @ApiOperation({
    summary: 'Get all zone types',
    description: `Retrieves a paginated list of zone types with comprehensive filtering options.
    
    **Features:**
    - Pagination support with customizable page size
    - Search by zone type name
    - Filter by algorithm type (CITY, AIRPORT, HIGHWAY, etc.)
    - Filter by active status
    - Include inactive zone types optionally
    - Include zone count statistics
    
    **Zone Type Algorithms:**
    - CITY: General city zones for urban ride-hailing
    - AIRPORT: Specialized airport pickup/dropoff zones
    - HIGHWAY: Highway service areas and rest stops
    - SUBURBAN: Suburban residential and commercial areas
    - RURAL: Rural and remote area coverage
    
    **Use cases:**
    - Configure different pricing algorithms by zone type
    - Manage operational parameters for different area types
    - Analyze zone distribution and usage statistics`,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone types retrieved successfully',
    type: ZoneTypePaginatedResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('zoneType:list')
  async findAll(
    @Query() paginationDto: ZoneTypePaginationDto,
  ): Promise<ZoneTypePaginatedResponseDto> {
    this.logger.log(
      `Getting zone types: page=${paginationDto.page}, limit=${paginationDto.limit}`,
    );

    const filters: any = {};
    if (paginationDto.search) filters.search = paginationDto.search.trim();
    if (paginationDto.algorithm) filters.algorithm = paginationDto.algorithm;
    if (paginationDto.isActive !== undefined)
      filters.isActive = paginationDto.isActive;

    if (paginationDto.isInactive !== undefined)
      filters.isActive = paginationDto.isActive = false;
    if (paginationDto.includeInactive !== undefined)
      filters.includeInactive = paginationDto.includeInactive;
    if (paginationDto.includeZoneCount !== undefined)
      filters.includeZoneCount = paginationDto.includeZoneCount;

    const result = await this.zoneTypeService.findPaginated(
      paginationDto.page || 1,
      paginationDto.limit || 10,
      filters,
    );

    return {
      success: true,
      message: 'Zone types retrieved successfully',
      data: result,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone type by ID
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Get zone type by ID',
    description: 'Retrieves a specific zone type by its unique identifier',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone type UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone type retrieved successfully',
    type: ZoneTypeDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone type ID format',
    type: ApiErrorResponseDto,
  })
  async findById(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ZoneTypeDetailsResponseDto> {
    this.logger.log(`Getting zone type by ID: ${id}`);

    const data = await this.zoneTypeService.findById(id);

    return {
      success: true,
      message: 'Zone type retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone type by name
   */
  @Get('name/:name')
  @ApiOperation({
    summary: 'Get zone type by name',
    description: 'Retrieves a specific zone type by its name',
  })
  @ApiParam({
    name: 'name',
    type: String,
    description: 'Zone type name',
    example: 'Downtown City Zone',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone type retrieved successfully',
    type: ZoneTypeDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone type not found',
    type: ApiErrorResponseDto,
  })
  async findByName(
    @Param('name') name: string,
  ): Promise<ZoneTypeDetailsResponseDto> {
    this.logger.log(`Getting zone type by name: ${name}`);

    const data = await this.zoneTypeService.findByName(name);

    return {
      success: true,
      message: 'Zone type retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone types by algorithm
   */
  @Get('algorithm/:algorithm')
  @ApiOperation({
    summary: 'Get zone types by algorithm',
    description: 'Retrieves all zone types that use a specific algorithm',
  })
  @ApiParam({
    name: 'algorithm',
    enum: ZoneAlgorithm,
    description: 'Algorithm type',
    example: ZoneAlgorithm.CITY,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone types retrieved successfully',
    type: [ZoneTypeResponseDtoClass],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid algorithm specified',
    type: ApiErrorResponseDto,
  })
  async findByAlgorithm(@Param('algorithm') algorithm: ZoneAlgorithm): Promise<{
    success: boolean;
    message: string;
    data: ZoneTypeResponseDto[];
    timestamp: number;
  }> {
    this.logger.log(`Getting zone types by algorithm: ${algorithm}`);

    const data = await this.zoneTypeService.findByAlgorithm(algorithm);

    return {
      success: true,
      message: 'Zone types retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get zone types with zone counts
   */
  @Get('stats/with-counts')
  @ApiOperation({
    summary: 'Get zone types with zone counts',
    description:
      'Retrieves all active zone types with the count of zones using each type',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone types with counts retrieved successfully',
    type: [ZoneTypeResponseDtoClass],
  })
  async getZoneTypesWithCounts(): Promise<{
    success: boolean;
    message: string;
    data: ZoneTypeResponseDto[];
    timestamp: number;
  }> {
    this.logger.log('Getting zone types with zone counts');

    const data = await this.zoneTypeService.getZoneTypesWithCounts();

    return {
      success: true,
      message: 'Zone types with counts retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Get available algorithms
   */
  @Get('meta/algorithms')
  @ApiOperation({
    summary: 'Get available algorithms',
    description: 'Returns a list of all available zone algorithms',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Available algorithms retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: {
          type: 'string',
          example: 'Available algorithms retrieved successfully',
        },
        data: {
          type: 'array',
          items: { type: 'string', enum: Object.values(ZoneAlgorithm) },
        },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  getAvailableAlgorithms(): {
    success: boolean;
    message: string;
    data: ZoneAlgorithm[];
    timestamp: number;
  } {
    this.logger.log('Getting available algorithms');

    const data = this.zoneTypeService.getAvailableAlgorithms();

    return {
      success: true,
      message: 'Available algorithms retrieved successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Create a new zone type
   */
  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new zone type',
    description: `Creates a new zone type configuration for algorithmic zone management.
    
    **Process:**
    1. Validates zone type name uniqueness
    2. Validates algorithm selection and configuration
    3. Sets up algorithmic parameters for zone operations
    4. Configures pricing and operational rules
    
    **Algorithm Configuration:**
    Each zone type can specify algorithm-specific parameters:
    - **CITY**: Urban density factors, traffic patterns, driver distribution
    - **AIRPORT**: Queue management, surge pricing rules, pickup protocols
    - **HIGHWAY**: Route optimization, fuel considerations, rest stop priorities
    - **SUBURBAN**: Residential area factors, parking availability, local regulations
    
    **Use cases:**
    - Define specialized operational zones (airports, malls, business districts)
    - Configure algorithm parameters for different geographic contexts
    - Set up pricing and surge rules specific to zone types
    - Establish driver assignment and routing preferences`,
  })
  @ApiBody({
    type: CreateZoneTypeInputDto,
    description: 'Zone type data',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Zone type created successfully',
    type: CreateZoneTypeResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Zone type with the same name already exists',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('zoneType:create')
  async create(
    @Body() createDto: CreateZoneTypeInputDto,
  ): Promise<CreateZoneTypeResponseDto> {
    this.logger.log(`Creating zone type: ${createDto.name}`);

    const data = await this.zoneTypeService.create(createDto);

    return {
      success: true,
      message: 'Zone type created successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Update zone type by ID
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Update zone type',
    description: 'Updates an existing zone type with the provided data',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone type UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiBody({
    type: UpdateZoneTypeInputDto,
    description: 'Zone type update data',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone type updated successfully',
    type: ZoneTypeDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid input data or zone type ID',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Zone type with the same name already exists',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('zoneType:edit')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateZoneTypeInputDto,
  ): Promise<ZoneTypeDetailsResponseDto> {
    this.logger.log(`Updating zone type: ${id}`);

    const data = await this.zoneTypeService.update(id, updateDto);

    return {
      success: true,
      message: 'Zone type updated successfully',
      data,
      timestamp: Date.now(),
    };
  }

  /**
   * Change zone type status (active/inactive)
   */
  @Patch(':id/update-status')
  @ApiOperation({ summary: 'Change zone type status (active/inactive)' })
  @ApiResponse({ status: 200, type: ZoneTypeDetailsResponseDto })
  @ApiResponse({ status: 404, type: ApiErrorResponseDto })
  @ApiResponse({ status: 400, type: ApiErrorResponseDto })
  @UseGuards(CaslAbilityGuard)
  @Ability('zoneType:status_update')
  async changeStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() body: ChangeZoneTypeStatusDto,
  ) {
    const data = await this.zoneTypeService.update(id, {
      isActive: body.isActive,
    });
    return {
      success: true,
      message: 'Zone type status changed successfully',
      data,
      timestamp: Date.now(),
    };
  }
  /**
   * Delete zone type by ID (soft delete)
   */
  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete zone type',
    description:
      'Soft deletes a zone type by marking it as inactive and deleted',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone type UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone type deleted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Zone type deleted successfully' },
        timestamp: { type: 'number', example: 1640995200000 },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone type ID format',
    type: ApiErrorResponseDto,
  })
  @UseGuards(CaslAbilityGuard)
  @Ability('zoneType:delete')
  async delete(@Param('id', ParseUUIDPipe) id: string): Promise<{
    success: boolean;
    message: string;
    timestamp: number;
  }> {
    this.logger.log(`Deleting zone type: ${id}`);

    await this.zoneTypeService.delete(id);

    return {
      success: true,
      message: 'Zone type deleted successfully',
      timestamp: Date.now(),
    };
  }

  /**
   * Restore soft-deleted zone type
   */
  @Put(':id/restore')
  @ApiOperation({
    summary: 'Restore zone type',
    description: 'Restores a soft-deleted zone type',
  })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'Zone type UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Zone type restored successfully',
    type: ZoneTypeDetailsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Zone type not found',
    type: ApiErrorResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid zone type ID format',
    type: ApiErrorResponseDto,
  })
  async restore(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ZoneTypeDetailsResponseDto> {
    this.logger.log(`Restoring zone type: ${id}`);

    const data = await this.zoneTypeService.restore(id);

    return {
      success: true,
      message: 'Zone type restored successfully',
      data,
      timestamp: Date.now(),
    };
  }
}
