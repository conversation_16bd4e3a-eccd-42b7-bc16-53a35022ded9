import { Module } from '@nestjs/common';
import { ZoneTypeModule as SharedZoneTypeModule } from '../../../../../libs/shared/src/modules/zone-type/zone-type.module';
import { ZoneTypeController } from './zone-type.controller';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

@Module({
  imports: [SharedZoneTypeModule],
  providers: [CityAdminRepository],
  controllers: [ZoneTypeController],
})
export class ApiZoneTypeModule {}
