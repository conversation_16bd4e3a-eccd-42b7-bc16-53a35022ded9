import { PrismaClient } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

interface PermissionData {
    name: string;
    description: string;
    resource: string;
    action: string;
}

interface RoleData {
    name: string;
    description: string;
    identifier: string;
    isCustomRole: boolean;
}

async function seedCountryIndia() {
    console.log('🌱 Seeding country: India...');
    const existing = await prisma.country.findFirst({ where: { name: 'India' } });
    if (existing) {
        console.log('⏭️  Country India already exists. Skipping.');
        return existing;
    }
    const india = await prisma.country.create({
        data: {
            name: 'India',
            iso2: 'IN',
            iso3: 'IND',
            phoneCode: '+91',
            currency: 'INR',
        },
    });
    console.log('✅ Country India created');
    return india;
}

// Vehicle Document Seeder for India
async function seedVehicleDocumentsIndia() {
    console.log('🌱 Seeding vehicle documents for India...');
    const india = await prisma.country.findFirst({ where: { name: 'India' } });
    if (!india) {
        throw new Error('Country India must exist before seeding vehicle documents');
    }
    const docs = [
        { name: 'Vehicle Registration', identifier: 'vehicle_registration' },
        { name: 'NOC', identifier: 'noc' },
        { name: 'Insurance', identifier: 'insurance' },
    ];
    for (const doc of docs) {
        // Try to find existing document by identifier and countryId
        const existing = await prisma.vehicleDocument.findFirst({
            where: { identifier: doc.identifier, countryId: india.id },
        });
        if (existing) {
            await prisma.vehicleDocument.update({
                where: { id: existing.id },
                data: {
                    name: doc.name,
                    identifier: doc.identifier,
                    countryId: india.id,
                },
            });
        } else {
            await prisma.vehicleDocument.create({
                data: {
                    name: doc.name,
                    identifier: doc.identifier,
                    countryId: india.id,
                },
            });
        }
    }
    console.log('✅ Vehicle documents for India seeded');
}
// Vehicle Seeder
async function seedVehicles() {
    console.log('🌱 Seeding vehicles...');

    const existingVehiclesCount = await prisma.vehicleType.count();
    if (existingVehiclesCount > 0) {
        console.log(`⏭️  Vehicles already exist (${existingVehiclesCount} found). Skipping vehicle seeding.`);
        return;
    }

    const vehicles = [
        {
            name: 'Sedan',
            description: 'Car',
            image: null,
        },
        {
            name: 'Motorbike/Scooter',
            description: 'Two-wheeler',
            image: null,
        },
        {
            name: 'Auto Rickshaw',
            description: 'Three-wheeler',
            image: null,
        },
    ];

    const createdVehicles = await prisma.vehicleType.createMany({
        data: vehicles,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdVehicles.count} vehicles`);
}

// KYC Documents Seeder for India
async function seedKycDocumentsIndia() {
    console.log('🌱 Seeding KYC documents for India...');

    // Get India country
    const india = await prisma.country.findFirst({ where: { name: 'India' } });
    if (!india) {
        console.log('❌ India country not found. Cannot seed KYC documents.');
        return;
    }

    const existingKycDocsCount = await prisma.kycDocument.count({ where: { countryId: india.id } });
    if (existingKycDocsCount > 0) {
        console.log(`⏭️  KYC documents for India already exist (${existingKycDocsCount} found). Skipping KYC document seeding.`);
        return;
    }

    const kycDocuments = [
        {
            countryId: india.id,
            name: 'Aadhaar Card',
            identifier: 'aadhaar_card',
            requiredFields: {
                fields: ['aadhaar_number', 'name', 'address', 'date_of_birth']
            },
            isMandatory: true,
        },
        {
            countryId: india.id,
            name: 'Driving Licence',
            identifier: 'driving_licence',
            requiredFields: {
                fields: ['licence_number', 'name', 'address', 'date_of_birth', 'expiry_date', 'vehicle_class']
            },
            isMandatory: true,
        },
        {
            countryId: india.id,
            name: 'Profile Photo',
            identifier: 'profile_photo',
            requiredFields: {
                fields: ['photo_url', 'upload_date']
            },
            isMandatory: false,
        },
        {
            countryId: india.id,
            name: 'Bank Details',
            identifier: 'bank_details',
            requiredFields: {
                fields: ['account_number', 'ifsc_code', 'account_holder_name', 'bank_name']
            },
            isMandatory: false,
        },
        {
            countryId: india.id,
            name: 'Police Clearance Certificate',
            identifier: 'police_clearance_certificate',
            requiredFields: {
                fields: ['certificate_number', 'issue_date', 'expiry_date', 'issuing_authority']
            },
            isMandatory: false,
        },
    ];

    const createdKycDocuments = await prisma.kycDocument.createMany({
        data: kycDocuments,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdKycDocuments.count} KYC documents for India`);
}

// Language Seeder
async function seedLanguages() {
    console.log('🌱 Seeding languages...');
    await prisma.language.deleteMany({ where: { deletedAt: { not: null } } }); // Clear existing languages
    const existingLanguagesCount = await prisma.language.count();
    if (existingLanguagesCount > 0) {
        console.log(`⏭️  Languages already exist (${existingLanguagesCount} found). Skipping language seeding.`);
        return;
    }
    const languages = [
        {
            code: 'en',
            name: 'English',
            nameInNative: 'English',
        },
        {
            code: 'ml',
            name: 'Malayalam',
            nameInNative: 'മലയാളം',
        },
        {
            code: 'hi',
            name: 'Hindi',
            nameInNative: 'हिन्दी',
        },
    ];

    const createdLanguages = await prisma.language.createMany({
        data: languages,
        skipDuplicates: true,
    });

    console.log(`✅ Created ${createdLanguages.count} languages`);
}

// Product Services Seeder
async function seedProductServices() {
    console.log('🌱 Seeding product services...');

    const productServices = [
        {
            name: 'City Ride',
            identifier: 'city_ride',
            description: 'Ride within the city limits.',
            languageSpec: {
                name: {
                    en: 'City Ride',
                    fr: 'Course en ville',
                    ml: 'സിറ്റി റൈഡ്'
                },
                description: {
                    en: 'Ride within the city limits.',
                    fr: 'Voyage dans les limites de la ville.',
                    ml: 'നഗരപരിധിക്കുള്ളിലെ യാത്ര.'
                }
            }
        },
        {
            name: 'Intercity',
            identifier: 'intercity',
            description: 'Travel between different cities.',
            languageSpec: {
                name: {
                    en: 'Intercity',
                    fr: 'Intercité',
                    ml: 'ഇന്റർസിറ്റി'
                },
                description: {
                    en: 'Travel between different cities.',
                    fr: 'Voyage entre différentes villes.',
                    ml: 'വിവിധ നഗരങ്ങൾ തമ്മിലുള്ള യാത്ര.'
                }
            }
        },
        {
            name: 'City Rental',
            identifier: 'city_rental',
            description: 'Rent a vehicle for extended periods.',
            languageSpec: {
                name: {
                    en: 'Rental',
                    fr: 'Location',
                    ml: 'വാടക'
                },
                description: {
                    en: 'Rent a vehicle for extended periods.',
                    fr: 'Louer un véhicule pour des périodes prolongées.',
                    ml: 'ദീർഘകാലത്തേക്ക് വാഹനം വാടകയ്ക്ക് എടുക്കുക.'
                }
            }
        }
    ];

    for (const service of productServices) {
        // Check if service with same identifier already exists
        const existingService = await prisma.productService.findFirst({
            where: { identifier: service.identifier }
        });

        if (existingService) {
            console.log(`⏭️  Product service '${service.name}' (${service.identifier}) already exists. Skipping.`);
            continue;
        }

        // Create the product service
        await prisma.productService.create({
            data: {
                name: service.name,
                identifier: service.identifier,
                description: service.description,
                languageSpec: service.languageSpec,
            }
        });

        console.log(`✅ Created product service: ${service.name}`);
    }

    console.log('✅ Product services seeding completed');
}

// Define all permissions for the system
const permissions: PermissionData[] = [
    // Driver Management
    { name: 'driver:create', description: 'Create driver', resource: 'driver', action: 'create' },
    { name: 'driver:edit', description: 'Edit driver details', resource: 'driver', action: 'edit' },
    { name: 'driver:list', description: 'List all drivers', resource: 'driver', action: 'list' },
    { name: 'driver:status_update', description: 'Update driver status', resource: 'driver', action: 'status_update' },
    { name: 'driver:manage', description: 'Manage driver account', resource: 'driver', action: 'manage' },

    // City Management
    { name: 'city:create', description: 'Create city', resource: 'city', action: 'create' },
    { name: 'city:edit', description: 'Edit city details', resource: 'city', action: 'edit' },
    { name: 'city:list', description: 'List all cities', resource: 'city', action: 'list' },
    { name: 'city:manage', description: 'Manage city boundary, products, and zones', resource: 'city', action: 'manage' },
    { name: 'city:status_update', description: 'Update city status', resource: 'city', action: 'status_update' },

    // Admin Language Management
    { name: 'language:create', description: 'Create language', resource: 'language', action: 'create' },
    { name: 'language:edit', description: 'Edit language', resource: 'language', action: 'edit' },
    { name: 'language:list', description: 'List languages', resource: 'language', action: 'list' },
    { name: 'language:delete', description: 'Delete language', resource: 'language', action: 'delete' },

    // Product Management
    { name: 'product:create', description: 'Create product', resource: 'product', action: 'create' },
    { name: 'product:edit', description: 'Edit product', resource: 'product', action: 'edit' },
    { name: 'product:list', description: 'List products', resource: 'product', action: 'list' },
    { name: 'product:status_update', description: 'Update product status', resource: 'product', action: 'status_update' },

    // Product Service Management
    { name: 'product_service:edit', description: 'Edit product service', resource: 'product_service', action: 'edit' },
    { name: 'product_service:list', description: 'List product services', resource: 'product_service', action: 'list' },

    // Vehicle Category Management
    { name: 'vehicle_category:create', description: 'Create vehicle category', resource: 'vehicle_category', action: 'create' },
    { name: 'vehicle_category:list', description: 'List vehicle categories', resource: 'vehicle_category', action: 'list' },
    { name: 'vehicle_category:edit', description: 'Edit vehicle category', resource: 'vehicle_category', action: 'edit' },

    // Zone Type Management
    { name: 'zone_type:create', description: 'Create zone type', resource: 'zone_type', action: 'create' },
    { name: 'zone_type:edit', description: 'Edit zone type', resource: 'zone_type', action: 'edit' },
    { name: 'zone_type:list', description: 'List zone types', resource: 'zone_type', action: 'list' },
    { name: 'zone_type:delete', description: 'Delete zone type', resource: 'zone_type', action: 'delete' },
    { name: 'zone_type:status_update', description: 'Update zone type status', resource: 'zone_type', action: 'status_update' },

    // Role Management
    { name: 'roles:create', description: 'Create roles', resource: 'roles', action: 'create' },
    { name: 'roles:edit', description: 'Edit roles', resource: 'roles', action: 'edit' },
    { name: 'roles:list', description: 'List roles', resource: 'roles', action: 'list' },
    { name: 'roles:manage_permissions', description: 'Manage role permissions', resource: 'roles', action: 'manage_permissions' },

    // Sub Admin Management
    { name: 'sub_admin:create', description: 'Create sub admin', resource: 'sub_admin', action: 'create' },
    // { name: 'sub_admin:edit', description: 'Edit sub admin', resource: 'sub_admin', action: 'edit' },
    { name: 'sub_admin:list', description: 'List sub admins', resource: 'sub_admin', action: 'list' },
    { name: 'sub_admin:status_update', description: 'Update sub admin status', resource: 'sub_admin', action: 'status_update' },

    // City Admin Management
    { name: 'city_admin:create', description: 'Create city admin', resource: 'city_admin', action: 'create' },
    { name: 'city_admin:list', description: 'List city admins', resource: 'city_admin', action: 'list' },
    { name: 'city_admin:edit', description: 'Edit city admin', resource: 'city_admin', action: 'edit' },
    { name: 'city_admin:status_update', description: 'Update city admin status', resource: 'city_admin', action: 'status_update' },

    // Charge Group Management
    { name: 'charge_group:create', description: 'Create charge group', resource: 'charge_group', action: 'create' },
    { name: 'charge_group:list', description: 'List charge groups', resource: 'charge_group', action: 'list' },
    { name: 'charge_group:edit', description: 'Edit charge group', resource: 'charge_group', action: 'edit' },
    { name: 'charge_group:delete', description: 'Delete charge group', resource: 'charge_group', action: 'delete' },
    { name: 'charge_group:manage', description: 'Manage charge group', resource: 'charge_group', action: 'manage' },
// Ride Management
    { name: 'ride:list', description: 'List rides', resource: 'ride', action: 'list' },
];


// Define roles and their permissions
const roles: RoleData[] = [
    {
        name: 'super_admin',
        description: 'Super administrator with full system access',
        identifier: 'super_admin',
        isCustomRole: false,
    },
    {
        name: 'rider',
        description: 'Regular rider user',
        identifier: 'rider',
        isCustomRole: false,
    },
    {
        name: 'driver',
        description: 'Driver user with vehicle and ride management capabilities',
        identifier: 'driver',
        isCustomRole: false,
    },
    {
        name: 'city_admin',
        description: 'City administrator with city-specific management capabilities',
        identifier: 'city_admin',
        isCustomRole: true,
    },
    {
        name: 'sub_admin',
        description: 'Sub administrator with limited management capabilities',
        identifier: 'sub_admin',
        isCustomRole: true,
    }
];

async function seedPermissions() {
    console.log('🌱 Seeding permissions...');

    // const existingPermissionsCount = await prisma.permission.count();
    // if (existingPermissionsCount > 0) {
    //     console.log(`⏭️  Permissions already exist (${existingPermissionsCount} found). Skipping permission seeding.`);
    //     return;
    // }

    for (const perm of permissions) {
        await prisma.permission.upsert({
            where: { name: perm.name }, // needs @unique on name
            update: {},                 // nothing to update if found
            create: perm,
        });
    }

    console.log(`✅ Created permissions`);
}

async function seedRoles() {
    console.log('🌱 Seeding roles...');

    // const existingRolesCount = await prisma.role.count();
    // if (existingRolesCount > 0) {
    //     console.log(`⏭️  Roles already exist (${existingRolesCount} found). Skipping role seeding.`);
    //     return;
    // }

    for (const roleData of roles) {

        // Check if role with same name already exists
        let role = await prisma.role.findFirst({
            where: { name: roleData.name }
        });

        if (role) {
            await prisma.role.update({
                where: { id: role.id },
                data: {
                    description: roleData.description,
                    identifier: roleData.identifier,
                    isCustomRole: roleData.isCustomRole,
                },
            });
            console.log(`📝 Updated role: ${role.name}`);
        } else {
            // Create the role
            role = await prisma.role.create({
                data: {
                    name: roleData.name,
                    description: roleData.description,
                    identifier: roleData.identifier,
                    isCustomRole: roleData.isCustomRole,
                },
            });

            console.log(`📝 Created role: ${role.name}`);
        }
        if (roleData.name === 'super_admin') {

            if (roleData.name === 'super_admin') {
                const allPermissions = await prisma.permission.findMany({ select: { id: true } });

                await Promise.all(
                    allPermissions.map(async (perm) => {
                        const exists = await prisma.rolePermission.findFirst({
                            where: { roleId: role.id, permissionId: perm.id },
                        });
                        if (!exists) {
                            await prisma.rolePermission.create({
                                data: { roleId: role.id, permissionId: perm.id },
                            });
                        }
                    })
                );

                console.log(`✅ Ensured all permissions assigned to role: ${roleData.name}`);
            }

            console.log(`✅ Ensured all permissions assigned to role: ${roleData.name}`);
        }


    }

    console.log('✅ Roles seeding completed');
}

// Superadmin Seeder
async function seedSuperAdmin() {
    console.log('🔑 Seeding superadmin user...');

    const adminEmail = '<EMAIL>'
    const adminPassword = 'tukxi@2025';

    // Check if superadmin already exists
    const existingAdmin = await prisma.user.findFirst({
        where: { email: adminEmail },
    });

    if (existingAdmin) {
        console.log('⏭️  Superadmin already exists. Skipping.');
        return existingAdmin;
    }

    // Find super_admin role
    const superAdminRole = await prisma.role.findFirst({
        where: { name: 'super_admin' },
    });

    if (!superAdminRole) {
        throw new Error('Super admin role not found. Please run role seeding first.');
    }

    // Create superadmin user
    const adminUser = await prisma.user.create({
        data: {
            email: adminEmail,
            emailVerifiedAt: new Date(),
            phoneNumber: null,
            phoneVerifiedAt: null,
            otpSecret: null,
            isPolicyAllowed: true,
        },
    });

    // Hash password and create auth credential
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(adminPassword, saltRounds);

    await prisma.authCredential.create({
        data: {
            type: 'PASSWORD',
            identifier: hashedPassword,
            userId: adminUser.id,
            metadata: {
                createdFor: 'superadmin',
                createdAt: new Date().toISOString(),
            },
        },
    });

    // Assign super_admin role to user
    await prisma.userRole.create({
        data: {
            userId: adminUser.id,
            roleId: superAdminRole.id,
        },
    });

    await prisma.userProfile.create({
        data: {
            userId: adminUser.id,
            roleId: superAdminRole.id,
            firstName: 'Super',
            lastName: 'Admin',
            status: 'active',
        },
    });

    console.log(`✅ Superadmin created with email: ${adminEmail}`);
    console.log(`🔐 Password: ${adminPassword}`);

    return adminUser;
}

async function main() {
    console.log('🚀 Starting database seeding...');

    try {
        // Always seed country India and vehicle documents for India first
        await seedCountryIndia();
        await seedVehicleDocumentsIndia();
        await seedKycDocumentsIndia();
        await seedProductServices();
        await seedRoles();
        await seedPermissions();

        // Check if seeding is needed for the rest
        const existingRolesCount = await prisma.role.count();
        const existingPermissionsCount = await prisma.permission.count();
        const existingLanguagesCount = await prisma.language.count();
        const existingVehiclesCount = await prisma.vehicleType.count();

        if (existingRolesCount > 0 && existingPermissionsCount > 0 && existingLanguagesCount > 0 && existingVehiclesCount > 0) {
            console.log('🛑 Database already seeded. Skipping seeding process.');
            console.log(`   Found ${existingRolesCount} roles, languages ${existingLanguagesCount} and ${existingPermissionsCount} permissions.`);
            console.log(`   Found ${existingVehiclesCount} Vehicle Types.`);
            return;
        }

        await seedLanguages();
        await seedVehicles();
        await seedSuperAdmin();

        console.log('🎉 Database seeding completed successfully!');

        // Print summary
        const finalRolesCount = await prisma.role.count();
        const finalPermissionsCount = await prisma.permission.count();
        const finalRolePermissionsCount = await prisma.rolePermission.count();
        const finalLanguagesCount = await prisma.language.count();

        console.log('\n📊 Seeding Summary:');
        console.log(`   Permissions: ${finalPermissionsCount}`);
        console.log(`   Roles: ${finalRolesCount}`);
        console.log(`   Role-Permission associations: ${finalRolePermissionsCount}`);
        console.log(`   Languages: ${finalLanguagesCount}`);

    } catch (error) {
        console.error('❌ Error during seeding:', error);
        throw error;
    }
}

main()
    .catch((e) => {
        console.error('💥 Seeding failed:', e);
        process.exit(1);
    })
    .finally(async () => {
        await prisma.$disconnect();
    });

// Export the main function for potential reuse
export { main as seedDatabase };
