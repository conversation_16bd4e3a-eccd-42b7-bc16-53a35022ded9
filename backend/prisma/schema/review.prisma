model Review {
  id         String    @id @default(uuid()) @map("id") @db.Uuid
  riderId    String    @map("rider_id") @db.Uuid
  driverId   String    @map("driver_id") @db.Uuid
  rideId     String    @map("ride_id") @db.Uuid
  reviewById String    @map("review_by_id") @db.Uuid // ID of the user who wrote the review
  rating     Decimal   @map("rating") @db.Decimal(2, 1) // Rating from 1.0 to 5.0
  review     String?   @map("review") @db.VarChar(1000) // Optional review text
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  rider    UserProfile @relation("ReviewRider", fields: [riderId], references: [id], onDelete: Cascade)
  driver   UserProfile @relation("ReviewDriver", fields: [driverId], references: [id], onDelete: Cascade)
  ride     Ride        @relation(fields: [rideId], references: [id], onDelete: Cascade)
  reviewBy UserProfile @relation("ReviewBy", fields: [reviewById], references: [id], onDelete: Cascade)

  @@unique([rideId, reviewById], name: "unique_review_per_ride_per_user") // Ensure one review per user per ride
  @@index([riderId], name: "idx_review_rider_id")
  @@index([driverId], name: "idx_review_driver_id")
  @@index([rideId], name: "idx_review_ride_id")
  @@index([reviewById], name: "idx_review_by_id")
  @@index([rating], name: "idx_review_rating")
  @@index([createdAt], name: "idx_review_created_at")
  @@map("reviews")
}
