model CityAdmin {
  id            String    @id @default(uuid()) @db.Uuid
  cityId        String    @map("city_id") @db.Uuid
  userProfileId String    @map("product_id") @db.Uuid
  isEnabled     <PERSON><PERSON><PERSON>   @default(true) @map("is_enabled")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  city  City        @relation(fields: [cityId], references: [id])
  admin UserProfile @relation(fields: [userProfileId], references: [id])

  @@map("city_admins")
}
