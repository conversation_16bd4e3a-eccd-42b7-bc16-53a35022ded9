model TaxGroup {
  id              String    @id @default(uuid()) @map("id") @db.Uuid
  name            String    @map("name")
  description     String?   @map("description")
  totalPercentage Decimal   @default(0) @map("total_percentage") @db.Decimal(5, 2)
  isActive        Boolean   @default(true) @map("is_active")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  subcategories TaxSubcategory[]
  commissions   Commission[]
  charges       Charge[]

  @@unique([name], name: "unique_tax_group_name")
  @@index([name], name: "idx_tax_group_name")
  @@index([isActive], name: "idx_tax_group_is_active")
  @@map("tax_groups")
}

model TaxSubcategory {
  id         String    @id @default(uuid()) @map("id") @db.Uuid
  taxGroupId String    @map("tax_group_id") @db.Uuid
  name       String    @map("name")
  percentage Decimal   @map("percentage") @db.Decimal(5, 2)
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  taxGroup TaxGroup @relation(fields: [taxGroupId], references: [id], onDelete: Cascade)

  @@index([taxGroupId], name: "idx_tax_subcategory_tax_group_id")
  @@index([name], name: "idx_tax_subcategory_name")
  @@map("tax_subcategories")
}
