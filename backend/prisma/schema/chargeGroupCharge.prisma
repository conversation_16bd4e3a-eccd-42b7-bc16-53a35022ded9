model ChargeGroupCharge {
  id            String    @id @default(uuid()) @map("id") @db.Uuid
  chargeGroupId String    @map("charge_group_id") @db.Uuid
  chargeId      String    @map("charge_id") @db.Uuid
  priority      Int       @default(0) @map("priority")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  chargeGroup ChargeGroup @relation(fields: [chargeGroupId], references: [id], onDelete: Cascade)
  charge      Charge      @relation(fields: [chargeId], references: [id], onDelete: Cascade)

  @@unique([chargeGroupId, chargeId])
  @@index([chargeGroupId], name: "idx_charge_group_charges_charge_group_id")
  @@index([chargeId], name: "idx_charge_group_charges_charge_id")
  @@index([priority], name: "idx_charge_group_charges_priority")
  @@map("charge_group_charges")
}
