model UserMetaData {
  id             String    @id @default(uuid()) @map("id") @db.Uuid
  userProfileId  String    @unique @map("user_profile_id") @db.Uuid
  avgRating      Decimal?  @map("avg_rating") @db.Decimal(3, 2) // e.g., 4.75 (max 5.00)
  ridesCompleted Int       @default(0) @map("rides_completed")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  userProfile UserProfile @relation(fields: [userProfileId], references: [id], onDelete: Cascade)

  @@index([userProfileId], name: "idx_user_meta_data_user_profile_id")
  @@index([avgRating], name: "idx_user_meta_data_avg_rating")
  @@index([ridesCompleted], name: "idx_user_meta_data_rides_completed")
  @@index([createdAt], name: "idx_user_meta_data_created_at")
  @@map("user_meta_data")
}
