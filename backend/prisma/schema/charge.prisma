enum ChargeType {
  flat
  metered
}

enum ChargeMeter {
  pickup_distance
  pickup_duration
  pickup_wait_duration
  trip_duration
  trip_wait_duration
  trip_distance
}

enum PriceModel {
  flat_amount
  linear_rate
  tiered
  percentage_of_charge
  formula
}

model Charge {
  id                   String       @id @default(uuid()) @map("id") @db.Uuid
  name                 String       @map("name")
  identifier           String       @unique @map("identifier")
  chargeType           ChargeType   @map("charge_type")
  condition            Json?        @map("condition") @db.JsonB
  meter                ChargeMeter? @map("meter")
  priceModel           PriceModel   @map("price_model")
  price                Json?        @map("price") @db.JsonB
  percentage           Decimal?     @map("percentage") @db.Decimal(6, 4)
  percentageOfChargeId String?      @map("percentage_of_charge_id") @db.Uuid
  taxGroupId           String?      @map("tax_group_id") @db.Uuid
  commissionId         String?      @map("commission_id") @db.Uuid
  createdAt            DateTime     @default(now()) @map("created_at")
  updatedAt            DateTime     @updatedAt @map("updated_at")
  deletedAt            DateTime?    @map("deleted_at") @db.Timestamptz
  isCom<PERSON>      @default(false)

  // Relations
  percentageOfCharge Charge?             @relation("ChargePercentage", fields: [percentageOfChargeId], references: [id], onDelete: SetNull)
  percentageCharges  Charge[]            @relation("ChargePercentage")
  chargeGroupCharges ChargeGroupCharge[]
  taxGroup           TaxGroup?           @relation(fields: [taxGroupId], references: [id], onDelete: SetNull)
  commission         Commission?         @relation(fields: [commissionId], references: [id], onDelete: SetNull)

  @@index([name], name: "idx_charge_name")
  @@index([identifier], name: "idx_charge_identifier")
  @@index([chargeType], name: "idx_charge_charge_type")
  @@index([priceModel], name: "idx_charge_price_model")
  @@index([taxGroupId], name: "idx_charge_tax_group_id")
  @@index([commissionId], name: "idx_charge_commission_id")
  @@map("charges")
}
