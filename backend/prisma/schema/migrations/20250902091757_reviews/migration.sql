-- AlterTable
-- ALTER TABLE "rides" ALTER COLUMN "pickup_location" DROP NOT NULL,
-- ALTER COLUMN "destination_location" DROP NOT NULL;

-- CreateTable
CREATE TABLE "reviews" (
    "id" UUID NOT NULL,
    "rider_id" UUID NOT NULL,
    "driver_id" UUID NOT NULL,
    "ride_id" UUID NOT NULL,
    "review_by_id" UUID NOT NULL,
    "rating" DECIMAL(2,1) NOT NULL,
    "review" VARCHAR(1000),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "reviews_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_review_rider_id" ON "reviews"("rider_id");

-- CreateIndex
CREATE INDEX "idx_review_driver_id" ON "reviews"("driver_id");

-- CreateIndex
CREATE INDEX "idx_review_ride_id" ON "reviews"("ride_id");

-- CreateIndex
CREATE INDEX "idx_review_by_id" ON "reviews"("review_by_id");

-- CreateIndex
CREATE INDEX "idx_review_rating" ON "reviews"("rating");

-- CreateIndex
CREATE INDEX "idx_review_created_at" ON "reviews"("created_at");

-- CreateIndex
CREATE UNIQUE INDEX "reviews_ride_id_review_by_id_key" ON "reviews"("ride_id", "review_by_id");

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_rider_id_fkey" FOREIGN KEY ("rider_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_driver_id_fkey" FOREIGN KEY ("driver_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_ride_id_fkey" FOREIGN KEY ("ride_id") REFERENCES "rides"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_review_by_id_fkey" FOREIGN KEY ("review_by_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
