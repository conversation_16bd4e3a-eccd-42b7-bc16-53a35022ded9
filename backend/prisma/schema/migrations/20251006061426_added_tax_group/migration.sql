-- CreateTable
CREATE TABLE "tax_groups" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "total_percentage" DECIMAL(5,2) NOT NULL DEFAULT 0,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "tax_groups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tax_subcategories" (
    "id" UUID NOT NULL,
    "tax_group_id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "percentage" DECIMAL(5,2) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "tax_subcategories_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_tax_group_name" ON "tax_groups"("name");

-- CreateIndex
CREATE INDEX "idx_tax_group_is_active" ON "tax_groups"("is_active");

-- CreateIndex
CREATE UNIQUE INDEX "tax_groups_name_key" ON "tax_groups"("name");

-- CreateIndex
CREATE INDEX "idx_tax_subcategory_tax_group_id" ON "tax_subcategories"("tax_group_id");

-- CreateIndex
CREATE INDEX "idx_tax_subcategory_name" ON "tax_subcategories"("name");

-- AddForeignKey
ALTER TABLE "tax_subcategories" ADD CONSTRAINT "tax_subcategories_tax_group_id_fkey" FOREIGN KEY ("tax_group_id") REFERENCES "tax_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;
