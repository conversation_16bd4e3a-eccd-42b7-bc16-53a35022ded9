-- CreateTable
CREATE TABLE "city_admins" (
    "id" UUID NOT NULL,
    "city_id" UUID NOT NULL,
    "product_id" UUID NOT NULL,
    "is_enabled" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "city_admins_pkey" PRIMARY KEY ("id")
);

-- AddForeignKey
ALTER TABLE "city_admins" ADD CONSTRAINT "city_admins_city_id_fkey" FOREIGN KEY ("city_id") REFERENCES "cities"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON><PERSON><PERSON><PERSON>
ALTER TABLE "city_admins" ADD CONSTRAINT "city_admins_product_id_fkey" FOREIGN KEY ("product_id") REFERENCES "user_profiles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
