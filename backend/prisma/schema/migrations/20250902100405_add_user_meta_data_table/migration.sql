-- AlterTable
ALTER TABLE "rides" ALTER COLUMN "pickup_location" DROP NOT NULL,
ALTER COLUMN "destination_location" DROP NOT NULL;

-- CreateTable
CREATE TABLE "user_meta_data" (
    "id" UUID NOT NULL,
    "user_profile_id" UUID NOT NULL,
    "avg_rating" DECIMAL(3,2),
    "rides_completed" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "user_meta_data_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "user_meta_data_user_profile_id_key" ON "user_meta_data"("user_profile_id");

-- CreateIndex
CREATE INDEX "idx_user_meta_data_user_profile_id" ON "user_meta_data"("user_profile_id");

-- CreateIndex
CREATE INDEX "idx_user_meta_data_avg_rating" ON "user_meta_data"("avg_rating");

-- CreateIndex
CREATE INDEX "idx_user_meta_data_rides_completed" ON "user_meta_data"("rides_completed");

-- CreateIndex
CREATE INDEX "idx_user_meta_data_created_at" ON "user_meta_data"("created_at");

-- AddForeignKey
ALTER TABLE "user_meta_data" ADD CONSTRAINT "user_meta_data_user_profile_id_fkey" FOREIGN KEY ("user_profile_id") REFERENCES "user_profiles"("id") ON DELETE CASCADE ON UPDATE CASCADE;
