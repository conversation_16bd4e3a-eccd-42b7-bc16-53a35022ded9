-- CreateEnum
CREATE TYPE "ChargeType" AS ENUM ('flat', 'metered');

-- CreateEnum
CREATE TYPE "ChargeMeter" AS ENUM ('pickup_distance', 'pickup_duration', 'pickup_wait_duration', 'trip_duration', 'trip_wait_duration', 'trip_distance');

-- CreateEnum
CREATE TYPE "PriceModel" AS ENUM ('flat_amount', 'linear_rate', 'tiered', 'percentage_of_charge', 'formula');

-- CreateTable
CREATE TABLE "charges" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "identifier" TEXT NOT NULL,
    "charge_type" "ChargeType" NOT NULL,
    "condition" JSONB,
    "meter" "ChargeMeter",
    "price_model" "PriceModel" NOT NULL,
    "price" JSONB NOT NULL,
    "percentage" DECIMAL(5,4),
    "percentage_of_charge_id" UUID,
    "charge_group_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "charges_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "charges_identifier_key" ON "charges"("identifier");

-- CreateIndex
CREATE INDEX "idx_charge_name" ON "charges"("name");

-- CreateIndex
CREATE INDEX "idx_charge_identifier" ON "charges"("identifier");

-- CreateIndex
CREATE INDEX "idx_charge_charge_group_id" ON "charges"("charge_group_id");

-- CreateIndex
CREATE INDEX "idx_charge_charge_type" ON "charges"("charge_type");

-- CreateIndex
CREATE INDEX "idx_charge_price_model" ON "charges"("price_model");

-- CreateIndex
CREATE INDEX "idx_charge_group_name_unique" ON "charges"("charge_group_id", "name");

-- AddForeignKey
ALTER TABLE "charges" ADD CONSTRAINT "charges_charge_group_id_fkey" FOREIGN KEY ("charge_group_id") REFERENCES "charge_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "charges" ADD CONSTRAINT "charges_percentage_of_charge_id_fkey" FOREIGN KEY ("percentage_of_charge_id") REFERENCES "charges"("id") ON DELETE SET NULL ON UPDATE CASCADE;
