/*
  Warnings:

  - You are about to drop the column `charge_group_id` on the `charges` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "charges" DROP CONSTRAINT "charges_charge_group_id_fkey";

-- DropIndex
DROP INDEX "idx_charge_charge_group_id";

-- DropIndex
DROP INDEX "idx_charge_group_name_unique";

-- AlterTable
ALTER TABLE "charges" DROP COLUMN "charge_group_id";

-- CreateTable
CREATE TABLE "charge_group_charges" (
    "id" UUID NOT NULL,
    "charge_group_id" UUID NOT NULL,
    "charge_id" UUID NOT NULL,
    "priority" INTEGER NOT NULL DEFAULT 0,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "charge_group_charges_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_charge_group_charges_charge_group_id" ON "charge_group_charges"("charge_group_id");

-- CreateIndex
CREATE INDEX "idx_charge_group_charges_charge_id" ON "charge_group_charges"("charge_id");

-- CreateIndex
CREATE INDEX "idx_charge_group_charges_priority" ON "charge_group_charges"("priority");

-- CreateIndex
CREATE UNIQUE INDEX "charge_group_charges_charge_group_id_charge_id_key" ON "charge_group_charges"("charge_group_id", "charge_id");

-- AddForeignKey
ALTER TABLE "charge_group_charges" ADD CONSTRAINT "charge_group_charges_charge_group_id_fkey" FOREIGN KEY ("charge_group_id") REFERENCES "charge_groups"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "charge_group_charges" ADD CONSTRAINT "charge_group_charges_charge_id_fkey" FOREIGN KEY ("charge_id") REFERENCES "charges"("id") ON DELETE CASCADE ON UPDATE CASCADE;
