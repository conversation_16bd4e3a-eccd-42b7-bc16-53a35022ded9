import { Decimal } from '@prisma/client/runtime/library';

export interface Review {
  id: string;
  riderId: string;
  driverId: string;
  rideId: string;
  reviewById: string;
  rating: Decimal;
  review: string | null;
  createdAt: Date;
  updatedAt: Date;
  deletedAt: Date | null;
}

export interface ReviewWithRelations extends Review {
  rider?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    profilePictureUrl: string | null;
  };
  driver?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    profilePictureUrl: string | null;
  };
  ride?: {
    id: string;
    status: string;
    createdAt: Date;
    completedAt: Date | null;
  };
  reviewBy?: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    profilePictureUrl: string | null;
  };
}

export interface CreateReviewData {
  riderId: string;
  driverId: string;
  rideId: string;
  reviewById: string;
  rating: number;
  review?: string | undefined;
}

export interface UpdateReviewData {
  rating?: number;
  review?: string;
}

export interface ReviewFilters {
  riderId?: string;
  driverId?: string;
  rideId?: string;
  reviewById?: string;
  minRating?: number;
  maxRating?: number;
  hasReviewText?: boolean;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    [key: string]: number; // "1": count, "2": count, etc.
  };
}
