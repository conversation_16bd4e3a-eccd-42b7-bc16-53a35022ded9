import { Decimal } from '@prisma/client/runtime/library';
import { BaseEntity } from '../base.repository';
import { UserProfile } from './userProfile.model';

export interface UserMetaData extends BaseEntity {
  userProfileId: string;
  avgRating: Decimal | null;
  ridesCompleted: number;
  userProfile?: UserProfile;
}

export interface CreateUserMetaDataData {
  userProfileId: string;
  avgRating?: number;
  ridesCompleted?: number;
}

export interface UpdateUserMetaDataData {
  avgRating?: number;
  ridesCompleted?: number;
}

export interface UserMetaDataWithProfile
  extends Omit<UserMetaData, 'userProfile'> {
  userProfile: {
    id: string;
    firstName: string;
    lastName: string;
    profilePictureUrl?: string;
  };
}
