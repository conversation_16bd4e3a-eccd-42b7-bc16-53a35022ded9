import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Permission } from './models/permission.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class PermissionRepository extends BaseRepository<Permission> {
  protected readonly modelName = 'permission';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Get all permissions
   */
  async getAllPermissions(roleId?: string): Promise<Permission[]> {
    return this.findMany({
      where: { deletedAt: null },
      orderBy: { name: 'asc' },
      include: {
        rolePermissions: roleId
          ? {
              where: { roleId },
            }
          : undefined,
      },
    });
  }

  async getAllPermissionsGroupByResource(
    roleId?: string,
  ): Promise<Record<string, Permission[]>> {
    const permissions = await this.findMany({
      where: { deletedAt: null },
      orderBy: { name: 'asc' },
      include: {
        rolePermissions: roleId
          ? {
              where: { roleId },
            }
          : undefined,
      },
    });

    return permissions.reduce(
      (acc, permission) => {
        if (!acc[permission.resource]) {
          acc[permission.resource] = [];
        }
        acc[permission.resource].push(permission);
        return acc;
      },
      {} as Record<string, Permission[]>,
    );
  }
  /**
   * Find permission by name
   */
  async findByName(name: string): Promise<Permission | null> {
    return this.findOne({
      where: { name, deletedAt: null },
    });
  }

  /**
   * Find permissions by IDs
   */
  async findByIds(ids: string[]): Promise<Permission[]> {
    return this.findMany({
      where: {
        id: { in: ids },
        deletedAt: null,
      },
    });
  }

  /**
   * Get permissions for a role
   */
  async getPermissionsByRoleId(roleId: string): Promise<Permission[]> {
    const result = await this.prisma.rolePermission.findMany({
      where: {
        roleId,
        deletedAt: null,
      },
      include: {
        permission: true,
      },
    });

    return result.map((rp) => rp.permission as Permission);
  }

  /**
   * Check if role has permission
   */
  async roleHasPermission(
    roleId: string,
    permissionId: string,
  ): Promise<boolean> {
    const rolePermission = await this.prisma.rolePermission.findFirst({
      where: {
        roleId,
        permissionId,
        deletedAt: null,
      },
    });

    return !!rolePermission;
  }

  /**
   * Add permissions to role
   */
  async addPermissionsToRole(
    roleId: string,
    permissionIds: string[],
  ): Promise<void> {
    const existingPermissions = await this.prisma.rolePermission.findMany({
      where: {
        roleId,
        permissionId: { in: permissionIds },
        deletedAt: null,
      },
    });

    const existingPermissionIds = existingPermissions.map(
      (rp) => rp.permissionId,
    );
    const newPermissionIds = permissionIds.filter(
      (id) => !existingPermissionIds.includes(id),
    );

    if (newPermissionIds.length > 0) {
      await this.prisma.rolePermission.createMany({
        data: newPermissionIds.map((permissionId) => ({
          roleId,
          permissionId,
        })),
      });
    }
  }

  /**
   * Remove permissions from role
   */
  async removePermissionsFromRole(
    roleId: string,
    permissionIds: string[],
  ): Promise<void> {
    await this.prisma.rolePermission.deleteMany({
      where: {
        roleId,
        permissionId: { in: permissionIds },
      },
    });
  }

  /**
   * Get all permissions with role status for a specific role
   */
  async getAllPermissionsWithRoleStatus(roleId: string): Promise<Permission[]> {
    const [allPermissions, rolePermissions] = await Promise.all([
      this.getAllPermissions(),
      this.getPermissionsByRoleId(roleId),
    ]);

    const rolePermissionIds = new Set(rolePermissions.map((p) => p.id));

    return allPermissions.map((permission) => ({
      ...permission,
      isAddedRole: rolePermissionIds.has(permission.id),
    }));
  }

  /**
   * Get permissions for a user via their roles
   */
  async getUserPermissions(roleIds: string[]): Promise<Permission[]> {
    const rolePermissions = await this.prisma.rolePermission.findMany({
      where: {
        role: {
          id: {
            in: roleIds,
          },
        },
        deletedAt: null,
      },
      include: {
        permission: true,
      },
    });

    return rolePermissions.map((rp) => rp.permission as Permission);
  }
}
