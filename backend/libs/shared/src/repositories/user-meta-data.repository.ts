import { Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  UserMetaData,
  CreateUserMetaDataData,
  UpdateUserMetaDataData,
  UserMetaDataWithProfile,
} from './models/userMetaData.model';

@Injectable()
export class UserMetaDataRepository extends BaseRepository<UserMetaData> {
  protected readonly modelName = 'userMetaData';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create user metadata
   */
  async createUserMetaData(
    data: CreateUserMetaDataData,
  ): Promise<UserMetaData> {
    const createData: any = {
      userProfileId: data.userProfileId,
      ridesCompleted: data.ridesCompleted || 0,
    };

    if (data.avgRating !== undefined) {
      createData.avgRating = new Decimal(data.avgRating);
    }

    return this.create(createData);
  }

  /**
   * Find user metadata by user profile ID
   */
  async findByUserProfileId(
    userProfileId: string,
  ): Promise<UserMetaData | null> {
    return this.findOne({
      where: { userProfileId },
    });
  }

  /**
   * Find user metadata by user profile ID with profile details
   */
  async findByUserProfileIdWithProfile(
    userProfileId: string,
  ): Promise<UserMetaDataWithProfile | null> {
    return this.findOne({
      where: { userProfileId },
      include: {
        userProfile: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
      },
    }) as Promise<UserMetaDataWithProfile | null>;
  }

  /**
   * Update user metadata by user profile ID
   */
  async updateByUserProfileId(
    userProfileId: string,
    data: UpdateUserMetaDataData,
  ): Promise<UserMetaData> {
    const updateData: any = {};

    if (data.avgRating !== undefined) {
      updateData.avgRating = new Decimal(data.avgRating);
    }

    if (data.ridesCompleted !== undefined) {
      updateData.ridesCompleted = data.ridesCompleted;
    }

    return this.update({
      where: { userProfileId },
      data: updateData,
    });
  }

  /**
   * Create or update user metadata
   */
  async upsertUserMetaData(
    userProfileId: string,
    data: UpdateUserMetaDataData,
  ): Promise<UserMetaData> {
    const createData: any = {
      userProfileId,
      ridesCompleted: data.ridesCompleted || 0,
    };

    const updateData: any = {};

    if (data.avgRating !== undefined) {
      createData.avgRating = new Decimal(data.avgRating);
      updateData.avgRating = new Decimal(data.avgRating);
    }

    if (data.ridesCompleted !== undefined) {
      updateData.ridesCompleted = data.ridesCompleted;
    }

    return this.upsert({ userProfileId }, createData, updateData);
  }

  /**
   * Increment rides completed count
   */
  async incrementRidesCompleted(userProfileId: string): Promise<UserMetaData> {
    // First, try to find existing metadata
    const existing = await this.findByUserProfileId(userProfileId);

    if (existing) {
      // Update existing record
      return this.update({
        where: { userProfileId },
        data: {
          ridesCompleted: existing.ridesCompleted + 1,
        },
      });
    } else {
      // Create new record with rides completed = 1
      return this.create({
        userProfileId,
        ridesCompleted: 1,
        avgRating: null,
      });
    }
  }

  /**
   * Update average rating
   */
  async updateAverageRating(
    userProfileId: string,
    newAvgRating: number,
  ): Promise<UserMetaData> {
    return this.upsertUserMetaData(userProfileId, {
      avgRating: newAvgRating,
    });
  }

  /**
   * Get top rated users (drivers/riders)
   */
  async getTopRatedUsers(
    limit: number = 10,
  ): Promise<UserMetaDataWithProfile[]> {
    return this.findMany({
      where: {
        avgRating: {
          not: null,
        },
      },
      include: {
        userProfile: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
      },
      orderBy: [{ avgRating: 'desc' }, { ridesCompleted: 'desc' }],
      take: limit,
    }) as Promise<UserMetaDataWithProfile[]>;
  }

  /**
   * Get users with most completed rides
   */
  async getMostActiveUsers(
    limit: number = 10,
  ): Promise<UserMetaDataWithProfile[]> {
    return this.findMany({
      where: {
        ridesCompleted: {
          gt: 0,
        },
      },
      include: {
        userProfile: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
      },
      orderBy: [{ ridesCompleted: 'desc' }, { avgRating: 'desc' }],
      take: limit,
    }) as Promise<UserMetaDataWithProfile[]>;
  }

  /**
   * Get user statistics
   */
  async getUserStats(userProfileId: string): Promise<{
    avgRating: number | null;
    ridesCompleted: number;
    rank?: number;
  }> {
    const metaData = await this.findByUserProfileId(userProfileId);

    if (!metaData) {
      return {
        avgRating: null,
        ridesCompleted: 0,
      };
    }

    // Calculate rank based on average rating
    let rank: number | undefined;
    if (metaData.avgRating) {
      const betterUsers = await this.count({
        avgRating: {
          gt: metaData.avgRating,
        },
      });
      rank = betterUsers + 1;
    }

    return {
      avgRating: metaData.avgRating ? Number(metaData.avgRating) : null,
      ridesCompleted: metaData.ridesCompleted,
      ...(rank !== undefined && { rank }),
    };
  }
}
