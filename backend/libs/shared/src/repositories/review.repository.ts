import { Injectable } from '@nestjs/common';
import { Decimal } from '@prisma/client/runtime/library';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import {
  Review,
  ReviewWithRelations,
  CreateReviewData,
  UpdateReviewData,
  ReviewFilters,
  ReviewStats,
} from './models/review.model';

@Injectable()
export class ReviewRepository extends BaseRepository<Review> {
  protected readonly modelName = 'review';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create a new review
   */
  async createReview(data: CreateReviewData): Promise<Review> {
    const createData: any = {
      riderId: data.riderId,
      driverId: data.driverId,
      rideId: data.rideId,
      reviewById: data.reviewById,
      rating: new Decimal(data.rating),
      review: data.review || null,
    };

    return this.create(createData);
  }

  /**
   * Find review by ID with relations
   */
  async findReviewByIdWithRelations(
    id: string,
  ): Promise<ReviewWithRelations | null> {
    return this.findOne({
      where: { id },
      include: {
        rider: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
        driver: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
        ride: {
          select: {
            id: true,
            status: true,
            createdAt: true,
            completedAt: true,
          },
        },
        reviewBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
      },
    }) as Promise<ReviewWithRelations | null>;
  }

  /**
   * Find reviews with filters and pagination
   */
  async findReviewsWithFilters(
    filters: ReviewFilters,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ reviews: ReviewWithRelations[]; total: number }> {
    const where: any = {};

    if (filters.riderId) where.riderId = filters.riderId;
    if (filters.driverId) where.driverId = filters.driverId;
    if (filters.rideId) where.rideId = filters.rideId;
    if (filters.reviewById) where.reviewById = filters.reviewById;
    if (filters.minRating)
      where.rating = { gte: new Decimal(filters.minRating) };
    if (filters.maxRating) {
      where.rating = { ...where.rating, lte: new Decimal(filters.maxRating) };
    }
    if (filters.hasReviewText !== undefined) {
      where.review = filters.hasReviewText ? { not: null } : null;
    }

    const [reviews, total] = await Promise.all([
      this.findMany({
        where,
        include: {
          rider: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePictureUrl: true,
            },
          },
          driver: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePictureUrl: true,
            },
          },
          ride: {
            select: {
              id: true,
              status: true,
              createdAt: true,
              completedAt: true,
            },
          },
          reviewBy: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              profilePictureUrl: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit,
      }) as Promise<ReviewWithRelations[]>,
      this.count(where),
    ]);

    return { reviews, total };
  }

  /**
   * Check if a user has already reviewed a specific ride
   */
  async hasUserReviewedRide(
    rideId: string,
    reviewById: string,
  ): Promise<boolean> {
    const review = await this.findOne({
      where: {
        rideId,
        reviewById,
      },
    });
    return !!review;
  }

  /**
   * Get review statistics for a user (as driver or rider)
   */
  async getUserReviewStats(
    userId: string,
    asRole: 'driver' | 'rider',
  ): Promise<ReviewStats> {
    const whereClause =
      asRole === 'driver' ? { driverId: userId } : { riderId: userId };

    const reviews = await this.findMany({
      where: whereClause,
      select: { rating: true },
    });

    const totalReviews = reviews.length;
    if (totalReviews === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: {},
      };
    }

    const ratingSum = reviews.reduce(
      (sum, review) => sum + Number(review.rating),
      0,
    );
    const averageRating = Number((ratingSum / totalReviews).toFixed(1));

    const ratingDistribution: { [key: string]: number } = {};
    reviews.forEach((review) => {
      const rating = Math.floor(Number(review.rating)).toString();
      ratingDistribution[rating] = (ratingDistribution[rating] || 0) + 1;
    });

    return {
      totalReviews,
      averageRating,
      ratingDistribution,
    };
  }

  /**
   * Update a review
   */
  async updateReview(id: string, data: UpdateReviewData): Promise<Review> {
    const updateData: any = { ...data };
    if (data.rating !== undefined) {
      updateData.rating = new Decimal(data.rating);
    }
    return this.updateById(id, updateData);
  }

  /**
   * Get reviews for a specific ride
   */
  async getReviewsForRide(rideId: string): Promise<ReviewWithRelations[]> {
    return this.findMany({
      where: { rideId },
      include: {
        reviewBy: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            profilePictureUrl: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
    }) as Promise<ReviewWithRelations[]>;
  }
}
