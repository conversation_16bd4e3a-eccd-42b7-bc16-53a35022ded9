import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { CityAdmin } from '@prisma/client';

@Injectable()
export class CityAdminRepository extends BaseRepository<CityAdmin> {
  protected readonly modelName = 'cityAdmin';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  /**
   * Create city admin assignment
   */
  async createCityAdmin(
    cityId: string,
    userProfileId: string,
  ): Promise<CityAdmin> {
    // Check if already assigned
    const existing = await this.findOne({
      where: {
        cityId,
        userProfileId,
        deletedAt: null,
      },
    });

    if (existing) {
      console.log(
        `User profile ${userProfileId} already assigned to city ${cityId}`,
      );
      return existing;
    }

    return this.create({
      cityId,
      userProfileId,
      isEnabled: true,
    } as Omit<CityAdmin, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>);
  }

  /**
   * Remove city admin assignment (soft delete)
   */
  async removeCityAdmin(
    cityId: string,
    userProfileId: string,
  ): Promise<CityAdmin | null> {
    const cityAdmin = await this.findOne({
      where: {
        cityId,
        userProfileId,
      },
    });

    if (!cityAdmin) {
      return null;
    }

    return this.hardDeleteById(cityAdmin.id);
  }

  /**
   * Get paginated city admins with profiles
   */
  async getCityAdminsPaginated(
    cityId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
  ) {
    const skip = (page - 1) * limit;

    const where: any = {
      cityId,
      deletedAt: null,
    };

    if (search) {
      where.admin = {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } },
          {
            user: {
              email: { contains: search, mode: 'insensitive' },
            },
          },
        ],
      };
    }

    if (status) {
      where.admin = {
        ...where.admin,
        status,
      };
    }

    const [data, total] = await Promise.all([
      this.prisma.cityAdmin.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          cityId: true,
          isEnabled: true,
          createdAt: true,
          admin: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              status: true,
              user: {
                select: {
                  id: true,
                  email: true,
                  phoneNumber: true,
                },
              },
              role: {
                select: { id: true, name: true },
              },
            },
          },
        },
      }),
      this.prisma.cityAdmin.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      admins: data,
      page,
      limit,
      total,
      totalPages,
    };
  }

  /**
   * Check if user profile is admin for city
   */
  async isCityAdmin(cityId: string, userProfileId: string): Promise<boolean> {
    const count = await this.count({
      cityId,
      userProfileId,
      deletedAt: null,
    });
    return count > 0;
  }

  /**
   * Get city admin assignment by user profile ID
   */
  async getCityAdminByProfileId(
    userProfileId: string,
  ): Promise<CityAdmin | null> {
    return this.findOne({
      where: {
        userProfileId,
        deletedAt: null,
      },
      select: {
        id: true,
        isEnabled: true,
        createdAt: true,
        updatedAt: true,
        userProfileId: true,
        cityId: true,
        city: { select: { id: true, name: true } },
      },
    });
  }

  async getCityAdminsByProfileId(userProfileId: string): Promise<CityAdmin[]> {
    return this.findMany({
      where: {
        userProfileId,
        deletedAt: null,
      },
      select: {
        cityId: true,
      },
    });
  }
}
