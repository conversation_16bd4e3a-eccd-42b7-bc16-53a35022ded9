import { Module } from '@nestjs/common';
import { PrismaModule } from '../../database/prisma/prisma.module';
import { PermissionService } from './permission.service';
import { PermissionRepository } from '../../repositories/permission.repository';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';

@Module({
  imports: [PrismaModule],
  providers: [PermissionService, PermissionRepository, UserProfileRepository],
  exports: [PermissionService, PermissionRepository],
})
export class PermissionModule {}
