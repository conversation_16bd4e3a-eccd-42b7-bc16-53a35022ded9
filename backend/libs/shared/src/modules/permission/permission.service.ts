import { Injectable, NotFoundException } from '@nestjs/common';
import { PermissionRepository } from '../../repositories/permission.repository';
import { Permission } from '../../repositories/models/permission.model';
import { UserProfileRepository } from '@shared/shared/repositories/user-profile.repository';

@Injectable()
export class PermissionService {
  constructor(
    private readonly permissionRepository: PermissionRepository,
    private readonly userProfileRepository: UserProfileRepository,
  ) {}

  /**
   * Get all permissions
   */
  async getAllPermissions(
    roleId?: string,
  ): Promise<Record<string, Permission[]>> {
    return this.permissionRepository.getAllPermissionsGroupByResource(roleId);
  }

  /**
   * Get permission by ID
   */
  async getPermissionById(id: string): Promise<Permission> {
    const permission = await this.permissionRepository.findById(id);
    if (!permission) {
      throw new NotFoundException('Permission not found');
    }
    return permission;
  }

  /**
   * Get permission by name
   */
  async getPermissionByName(name: string): Promise<Permission | null> {
    return this.permissionRepository.findByName(name);
  }

  /**
   * Get permissions by IDs
   */
  async getPermissionsByIds(ids: string[]): Promise<Permission[]> {
    return this.permissionRepository.findByIds(ids);
  }

  /**
   * Get permissions for a user via their roles
   */
  async getUserPermissions(userId: string): Promise<Permission[]> {
    const userRoleIds = await this.userProfileRepository.getUserRoleIds(userId);
    return this.permissionRepository.getUserPermissions(userRoleIds);
  }
}
