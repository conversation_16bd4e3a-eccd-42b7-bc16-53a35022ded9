import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AdminService } from './admin.service';
import { UserRepository } from '../../repositories/user.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { RoleRepository } from '../../repositories/role.repository';
import { AuthCredentialRepository } from '../../repositories/auth-credential.repository';
import { RefreshTokenRepository } from '../../repositories/refresh-token.repository';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigModule } from '../../config';
import { PrismaService } from '../../database/prisma/prisma.service';
import { CityModule } from '../city/city.module';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';
import { RoleModule } from '../role/role.module';
import { TokenService } from '../auth/services/token.service';
import { PermissionRepository } from '@shared/shared/repositories';

@Module({
  imports: [
    AppConfigModule,
    RoleModule,
    CityModule,
    JwtModule.register({}), // Configuration will be provided by AppConfigService
  ],
  providers: [
    AdminService,
    UserRepository,
    UserProfileRepository,
    RoleRepository,
    AuthCredentialRepository,
    RefreshTokenRepository,
    TokenService,
    NotificationService,
    PrismaService,
    CityAdminRepository,
    PermissionRepository,
  ],
  exports: [AdminService],
})
export class AdminModule {}
