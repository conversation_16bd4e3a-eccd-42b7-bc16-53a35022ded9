import { Module } from '@nestjs/common';
import { ChargeService } from './charge.service';
import { ChargeRepository } from '../../repositories/charge.repository';
import { ChargeGroupRepository } from '../../repositories/charge-group.repository';
import { TaxGroupRepository } from '../../repositories/tax-group.repository';
import { CommissionRepository } from '../../repositories/commission.repository';
import { RepositoryModule } from '../../repositories/repository.module';
import { ChargeGroupChargeRepository } from '@shared/shared/repositories/charge-group-charge.repository';

@Module({
  imports: [RepositoryModule],
  providers: [
    ChargeService,
    ChargeRepository,
    ChargeGroupRepository,
    ChargeGroupChargeRepository,
    TaxGroupRepository,
    CommissionRepository,
  ],
  exports: [ChargeService],
})
export class ChargeModule { }
