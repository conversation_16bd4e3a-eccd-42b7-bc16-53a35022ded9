import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import {
  Charge,
  ChargeType,
  PriceModel,
} from '@shared/shared/repositories/models/charge.model';
import { ChargeRepository } from '@shared/shared/repositories/charge.repository';
import { ChargeGroupRepository, TaxGroupRepository, CommissionRepository } from '@shared/shared/repositories';
import { ChargeGroupChargeRepository } from '@shared/shared/repositories/charge-group-charge.repository';

@Injectable()
export class ChargeService {
  constructor(
    private readonly chargeRepository: ChargeRepository,
    private readonly chargeGroupRepository: ChargeGroupRepository,
    private readonly chargeGroupChargeRepository: ChargeGroupChargeRepository,
    private readonly taxGroupRepository: TaxGroupRepository,
    private readonly commissionRepository: CommissionRepository,
  ) { }

  /**
   * Create a new charge.
   */
  async createCharge(
    chargeGroupId: string | null | undefined,
    data: Omit<
      Charge,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'chargeGroupId'
    > & { identifier?: string },
  ): Promise<Charge> {
    this.validateChargeData(data);

    // If chargeGroupId is provided, validate existence
    if (chargeGroupId) {
      const chargeGroup =
        await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
      if (!chargeGroup) {
        throw new NotFoundException(
          `Charge group with ID '${chargeGroupId}' not found`,
        );
      }
    }

    // Generate or validate identifier
    let identifier = data.identifier?.trim();
    if (!identifier) {
      identifier = `${data.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '_')
        .replace(/^_+|_+$/g, '')}_${Date.now().toString(36)}`;
    }
    if (await this.chargeRepository.identifierExistsGlobally(identifier)) {
      throw new ConflictException(
        `Charge with identifier "${identifier}" already exists`,
      );
    }

    // Validate percentage reference if needed
    if (data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
      if (!chargeGroupId) {
        throw new BadRequestException(
          'Charge group ID is required for percentage-based charges',
        );
      }
      await this.validatePercentageReference(
        data.percentageOfChargeId!,
        chargeGroupId,
      );
    }

    // Validate tax group if provided
    if (data.taxGroupId) {
      const taxGroup = await this.taxGroupRepository.findTaxGroupById(data.taxGroupId);
      if (!taxGroup) {
        throw new BadRequestException('Tax group not found');
      }
    }

    // Create charge
    const charge = await this.chargeRepository.createCharge({
      ...data,
      identifier,
    });

    // Attach to group if needed
    if (chargeGroupId) {
      try {
        await this.chargeGroupChargeRepository.createChargeGroupCharge({
          chargeGroupId,
          chargeId: charge.id,
          priority:
            await this.chargeGroupChargeRepository.getNextPriority(
              chargeGroupId,
            ),
        });
      } catch (error) {
        await this.chargeRepository.hardDelete({ where: { id: charge.id } });
        throw error;
      }
    }

    return charge;
  }

  /**
   * Create a new charge with charge group ID in the data.
   */
  async createChargeWithGroupId(
    data: Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'> & {
      chargeGroupId: string;
    },
  ): Promise<Charge> {
    return this.createCharge(data.chargeGroupId, data);
  }

  /**
   * Create a standalone charge (not attached to any charge group).
   */
  async createStandaloneCharge(
    data: Omit<
      Charge,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'chargeGroupId'
    > & { identifier?: string },
  ): Promise<Charge> {
    // Validate that percentage-based charges are not created as standalone
    if (data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
      throw new BadRequestException(
        'Percentage-based charges cannot be created as standalone charges. They must be attached to a charge group and reference another charge within the same group.',
      );
    }
    data.isCommon = true;

    return this.createCharge(null, data);
  }

  /**
   * Find all charges (across all charge groups).
   */
  async findAllCharges(): Promise<Charge[]> {
    return this.chargeRepository.findMany({
      include: {
        percentageOfCharge: true,
      },
    });
  }

  /**
   * Find paginated charges with search and filter capabilities.
   */
  async findPaginatedCharges(
    page: number = 1,
    limit: number = 10,
    filters?: {
      search?: string;
      chargeType?: string;
      priceModel?: string;
      minAmount?: number;
      maxAmount?: number;
      createdFrom?: string;
      createdTo?: string;
      chargeGroupId?: string;
      includeUnattached?: boolean;
      isCommon?: boolean;
      taxGroupId?: string;
    },
  ): Promise<{
    data: Charge[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    // Validate pagination parameters
    if (page < 1) page = 1;
    if (limit < 1 || limit > 100) limit = 10;

    // Validate charge group if provided
    if (filters?.chargeGroupId) {
      const chargeGroup = await this.chargeGroupRepository.findChargeGroupById(
        filters.chargeGroupId,
      );
      if (!chargeGroup) {
        throw new NotFoundException(
          `Charge group with ID '${filters.chargeGroupId}' not found`,
        );
      }
    }

    return this.chargeRepository.findPaginated(page, limit, filters);
  }

  /**
   * Find all charges by charge group ID.
   */
  async findChargesByChargeGroupId(chargeGroupId: string): Promise<Charge[]> {
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
    if (!chargeGroup) {
      throw new NotFoundException(
        `Charge group with ID '${chargeGroupId}' not found`,
      );
    }

    const chargeGroupCharges =
      await this.chargeGroupChargeRepository.findChargesInGroup(chargeGroupId);
    const charges = chargeGroupCharges.map((cgc: any) => ({
      ...cgc.charge,
      chargeGroupId: cgc.chargeGroupId,
      priority: cgc.priority,
    }));

    return charges;
  }

  /**
   * Find charge by ID (across all charge groups).
   */
  async findChargeById(id: string): Promise<Charge> {
    const charge = await this.chargeRepository.findById(id, { include: { taxGroup: true, percentageOfCharge: true } });
    if (!charge) {
      throw new NotFoundException(`Charge with ID '${id}' not found`);
    }
    return charge;
  }

  /**
   * Update charge by ID (across all charge groups).
   */
  async updateChargeById(
    id: string,
    data: Partial<Omit<Charge, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>>,
  ): Promise<Charge> {
    // Verify charge exists
    const existingCharge = await this.findChargeById(id);

    // Validate updated charge data
    if (Object.keys(data).length > 0) {
      this.validateChargeData({ ...existingCharge, ...data });
    }

    // Check for duplicate identifier (excluding current record) - check globally since charges can exist independently
    if (data.identifier) {
      const identifierExists =
        await this.chargeRepository.identifierExistsGlobally(
          data.identifier,
          id,
        );
      if (identifierExists) {
        throw new ConflictException(
          `Charge with identifier "${data.identifier}" already exists`,
        );
      }
    }

    // Don't allow identifier to be changed
    if (data.identifier !== undefined) {
      delete data.identifier;
    }

    // Validate percentage reference if applicable
    if (
      data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE ||
      (data.percentageOfChargeId &&
        existingCharge.priceModel === PriceModel.PERCENTAGE_OF_CHARGE)
    ) {
      // For percentage references, we need to find which group the charge belongs to
      const chargeRelationships =
        await this.chargeGroupChargeRepository.findMany({
          where: { chargeId: id },
        });

      if (chargeRelationships.length === 0) {
        throw new BadRequestException(
          'Charge must be attached to a group to use percentage pricing',
        );
      }

      // Use the first group (in a real scenario, you might want to handle multiple groups differently)
      const chargeGroupId = chargeRelationships[0].chargeGroupId;

      await this.validatePercentageReference(
        data.percentageOfChargeId || existingCharge.percentageOfChargeId!,
        chargeGroupId,
        id,
      );
    }

    // Validate tax group if provided
    if (data.taxGroupId) {
      const taxGroup = await this.taxGroupRepository.findTaxGroupById(data.taxGroupId);
      if (!taxGroup) {
        throw new BadRequestException('Tax group not found');
      }
    }

    return this.chargeRepository.updateById(id, data);
  }

  /**
   * Delete charge by ID (across all charge groups).
   */
  async deleteChargeById(id: string): Promise<void> {
    // Verify charge exists
    await this.findChargeById(id);

    // Check if other charges reference this charge
    const referencingCharges =
      await this.chargeRepository.findChargesReferencingAsPercentage(id);
    if (referencingCharges.length > 0) {
      throw new BadRequestException(
        `Cannot delete charge as it is referenced by ${referencingCharges.length} other charge(s)`,
      );
    }

    // Delete all relationships first
    await this.chargeGroupChargeRepository.softDeleteMany({ chargeId: id });

    // Then delete the charge
    await this.chargeRepository.softDelete({ where: { id } });
  }

  /**
   * Delete charge by ID and charge group ID.
   */
  async deleteChargeByIdAndChargeGroupId(
    id: string,
    chargeGroupId: string,
  ): Promise<void> {
    // Check if other charges reference this charge
    const referencingCharges =
      await this.chargeRepository.findChargesReferencingAsPercentage(id);
    if (referencingCharges.length > 0) {
      throw new BadRequestException(
        `Cannot delete charge as it is referenced by ${referencingCharges.length} other charge(s)`,
      );
    }

    // Delete the specific relationship first
    await this.chargeGroupChargeRepository.deleteByChargeGroupAndChargeId(
      chargeGroupId,
      id,
    );

    // If the charge is not attached to any other groups, delete it
    const remainingRelationships =
      await this.chargeGroupChargeRepository.findMany({
        where: { chargeId: id },
      });

    if (remainingRelationships.length === 0) {
      await this.chargeRepository.softDelete({ where: { id } });
    }
  }

  /**
   * Validate charge data.
   */
  private validateChargeData(data: Partial<Charge>): void {
    // Validate meter field for metered charges
    if (data.chargeType === ChargeType.METERED && !data.meter) {
      throw new BadRequestException(
        'Meter field is required for metered charges',
      );
    }

    // Validate percentage fields for percentage price model
    if (data.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
      if (!data.percentage || data.percentage <= 0 || data.percentage > 100) {
        throw new BadRequestException(
          'Valid percentage (0.0001-100.0000) is required for percentage price model',
        );
      }
      if (!data.percentageOfChargeId) {
        throw new BadRequestException(
          'Percentage of charge ID is required for percentage price model',
        );
      }
    }

    // Validate price configuration
    if (data.price) {
      this.validatePriceConfig(data.priceModel!, data.price);
    }
  }

  /**
   * Validate price configuration based on price model.
   */
  private validatePriceConfig(priceModel: PriceModel, price: any): void {
    switch (priceModel) {
      case PriceModel.FLAT_AMOUNT:
        if (!price.amount || price.amount <= 0) {
          throw new BadRequestException(
            'Valid amount is required for flat amount price model',
          );
        }
        if (!price.currency) {
          throw new BadRequestException(
            'Currency is required for flat amount price model',
          );
        }
        break;

      case PriceModel.LINEAR_RATE:
        if (!price.rate || price.rate <= 0) {
          throw new BadRequestException(
            'Valid rate is required for linear rate price model',
          );
        }
        if (!price.currency) {
          throw new BadRequestException(
            'Currency is required for linear rate price model',
          );
        }
        break;

      case PriceModel.TIERED:
        if (
          !price.tiers ||
          !Array.isArray(price.tiers) ||
          price.tiers.length === 0
        ) {
          throw new BadRequestException(
            'Valid tiers array is required for tiered price model',
          );
        }
        this.validateTierStructure(price.tiers);
        break;

      case PriceModel.FORMULA:
        if (!price.formula || typeof price.formula !== 'string') {
          throw new BadRequestException(
            'Valid formula string is required for formula price model',
          );
        }
        break;
    }
  }

  /**
   * Validate tier structure for tiered pricing.
   */
  private validateTierStructure(tiers: any[]): void {
    for (let i = 0; i < tiers.length; i++) {
      const tier = tiers[i];

      // Validate required fields
      if (typeof tier.From !== 'number' || tier.From < 0) {
        throw new BadRequestException(
          `Tier ${i + 1}: 'From' must be a non-negative number`,
        );
      }

      if (
        tier.To !== 'inf' &&
        (typeof tier.To !== 'number' || tier.To <= tier.From)
      ) {
        throw new BadRequestException(
          `Tier ${i + 1}: 'To' must be a number greater than 'From' or 'inf'`,
        );
      }

      // Validate pricing type - must have either Flat_fee or Rate, but not both
      const hasFlatFee = tier.Flat_fee !== undefined;
      const hasRate = tier.Rate !== undefined;

      if (!hasFlatFee && !hasRate) {
        throw new BadRequestException(
          `Tier ${i + 1}: Must specify either 'Flat_fee' or 'Rate'`,
        );
      }

      if (hasFlatFee && hasRate) {
        throw new BadRequestException(
          `Tier ${i + 1}: Cannot specify both 'Flat_fee' and 'Rate'`,
        );
      }

      // Validate Flat_fee format
      if (hasFlatFee) {
        if (typeof tier.Flat_fee !== 'number' || tier.Flat_fee < 0) {
          throw new BadRequestException(
            `Tier ${i + 1}: 'Flat_fee' must be a non-negative number`,
          );
        }
      }

      // Validate Rate format
      if (hasRate) {
        if (typeof tier.Rate !== 'number' || tier.Rate < 0) {
          throw new BadRequestException(
            `Tier ${i + 1}: 'Rate' must be a non-negative number`,
          );
        }
      }

      // Validate currency
      if (!tier.currency) {
        throw new BadRequestException(`Tier ${i + 1}: 'currency' is required`);
      }
    }

    // Validate tier ranges don't overlap and are in order
    const sortedTiers = [...tiers].sort((a, b) => a.From - b.From);

    for (let i = 0; i < sortedTiers.length - 1; i++) {
      const currentTier = sortedTiers[i];
      const nextTier = sortedTiers[i + 1];

      if (currentTier.To !== 'inf' && currentTier.To > nextTier.From) {
        throw new BadRequestException('Tier ranges cannot overlap');
      }

      if (currentTier.To === 'inf' && i < sortedTiers.length - 1) {
        throw new BadRequestException(
          'Only the last tier can have "inf" as To value',
        );
      }
    }
  }

  /**
   * Validate percentage reference to prevent circular dependencies.
   */
  private async validatePercentageReference(
    percentageOfChargeId: string,
    chargeGroupId: string,
    excludeId?: string,
  ): Promise<void> {
    // Find the referenced charge (it must exist and be attached to the same group)
    const referencedCharge =
      await this.chargeRepository.findById(percentageOfChargeId);

    if (!referencedCharge) {
      throw new BadRequestException('Referenced charge not found');
    }

    // Check if the referenced charge is attached to the same group
    const isAttachedToGroup =
      await this.chargeGroupChargeRepository.relationshipExists(
        chargeGroupId,
        percentageOfChargeId,
      );

    if (!isAttachedToGroup) {
      throw new BadRequestException(
        'Referenced charge must be attached to the same charge group',
      );
    }

    // Prevent self-reference
    if (excludeId && percentageOfChargeId === excludeId) {
      throw new BadRequestException('Charge cannot reference itself');
    }

    // Prevent circular reference (comprehensive check)
    if (referencedCharge.priceModel === PriceModel.PERCENTAGE_OF_CHARGE) {
      // Check if the referenced charge references back to the current charge (excludeId)
      if (referencedCharge.percentageOfChargeId === excludeId) {
        throw new BadRequestException(
          'Cannot create circular reference between charges',
        );
      }

      // Check for deeper circular references
      const referencedChargeReferences =
        await this.chargeRepository.findChargesReferencingAsPercentage(
          referencedCharge.id,
        );
      for (const ref of referencedChargeReferences) {
        if (ref.id === excludeId) {
          throw new BadRequestException(
            'Cannot create circular reference between charges',
          );
        }
      }

      throw new BadRequestException(
        'Cannot reference a charge that is also a percentage-based charge',
      );
    }
  }
  /**
   * Attach an existing charge to a charge group.
   */
  async attachChargeToGroup(
    chargeGroupId: string,
    data: any, // AttachChargeDto
  ): Promise<any> {
    // Verify charge group exists
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
    if (!chargeGroup) {
      throw new NotFoundException(
        `Charge group with ID '${chargeGroupId}' not found`,
      );
    }

    // Verify charge exists
    const charge = await this.findChargeById(data.chargeId);
    if (!charge) {
      throw new NotFoundException(
        `Charge with ID '${data.chargeId}' not found`,
      );
    }

    // Check if relationship already exists
    const existingRelationship =
      await this.chargeGroupChargeRepository.relationshipExists(
        chargeGroupId,
        data.chargeId,
      );
    if (existingRelationship) {
      throw new ConflictException(
        'Charge is already attached to this charge group',
      );
    }

    // Get the next priority for this group
    const nextPriority =
      data.priority ??
      (await this.chargeGroupChargeRepository.getNextPriority(chargeGroupId));

    // Create the charge group charge relationship
    const chargeGroupCharge =
      await this.chargeGroupChargeRepository.createChargeGroupCharge({
        chargeGroupId,
        chargeId: data.chargeId,
        priority: nextPriority,
      });

    // Return the relationship with charge details
    return this.chargeGroupChargeRepository.findOne({
      where: { id: chargeGroupCharge.id },
      include: {
        charge: {
          include: {
            percentageOfCharge: true,
          },
        },
        chargeGroup: true,
      },
    });
  }

  /**
   * Detach a charge from a charge group.
   */
  async detachChargeFromGroup(
    chargeGroupId: string,
    chargeId: string,
  ): Promise<void> {
    // Verify charge group exists
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
    if (!chargeGroup) {
      throw new NotFoundException(
        `Charge group with ID '${chargeGroupId}' not found`,
      );
    }

    // Verify charge exists
    const charge = await this.findChargeById(chargeId);
    if (!charge) {
      throw new NotFoundException(`Charge with ID '${chargeId}' not found`);
    }
    // Check if relationship exists
    const existingRelationship =
      await this.chargeGroupChargeRepository.relationshipExists(
        chargeGroupId,
        chargeId,
      );
    if (!existingRelationship) {
      throw new NotFoundException(
        'Charge is not attached to this charge group',
      );
    }

    // Delete the relationship
    await this.chargeGroupChargeRepository.softDelete({
      where: { id: existingRelationship.id },
    });
  }

  /**
   * Update charge priority within a charge group.
   */
  async updateChargeGroupChargePriority(
    relationshipId: string,
    priority: number,
  ): Promise<any> {
    // Verify relationship exists
    const relationship =
      await this.chargeGroupChargeRepository.findById(relationshipId);
    if (!relationship) {
      throw new NotFoundException(
        `Charge group charge relationship with ID '${relationshipId}' not found`,
      );
    }

    // Update the priority
    return this.chargeGroupChargeRepository.updatePriority(
      relationshipId,
      priority,
    );
  }

  /**
   * Get all charges in a charge group ordered by priority.
   */
  async getChargesInGroup(chargeGroupId: string): Promise<any[]> {
    // Verify charge group exists
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
    if (!chargeGroup) {
      throw new NotFoundException(
        `Charge group with ID '${chargeGroupId}' not found`,
      );
    }

    return this.chargeGroupChargeRepository.findChargesInGroup(chargeGroupId);
  }

  /**
   * Attach a commission to a charge.
   */
  async attachCommissionToCharge(chargeId: string, commissionId: string): Promise<Charge> {
    // Verify charge exists
    const existingCharge = await this.findChargeById(chargeId);

    // Verify commission exists
    const commission = await this.commissionRepository.findCommissionById(commissionId);
    if (!commission) {
      throw new NotFoundException(`Commission with ID '${commissionId}' not found`);
    }

    // Check if charge already has a commission
    if (existingCharge.commissionId) {
      throw new BadRequestException(
        `Charge already has a commission attached. Please detach the existing commission first.`,
      );
    }

    return this.chargeRepository.attachCommission(chargeId, commissionId);
  }

  /**
   * Detach a commission from a charge.
   */
  async detachCommissionFromCharge(chargeId: string): Promise<Charge> {
    // Verify charge exists
    const existingCharge = await this.findChargeById(chargeId);

    // Check if charge has a commission
    if (!existingCharge.commissionId) {
      throw new BadRequestException(`Charge does not have a commission attached`);
    }

    return this.chargeRepository.detachCommission(chargeId);
  }
}
