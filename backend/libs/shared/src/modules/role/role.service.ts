import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { RoleRepository } from '../../repositories/role.repository';
import { PermissionRepository } from '../../repositories/permission.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { Role } from '@prisma/client';
import { Permission } from '../../repositories/models/permission.model';
import { UserProfileStatus } from '../../repositories/models/userProfile.model';

export interface CreateRoleData {
  name: string;
  description?: string;
  identifier?: string;
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  identifier?: string;
}

export interface RoleListOptions {
  page?: number;
  limit?: number;
  search?: string;
}

export interface AdminListOptions {
  page?: number;
  limit?: number;
  search?: string;
  status?: UserProfileStatus;
  exceptCityId?: string;
}

@Injectable()
export class RoleService {
  constructor(
    private readonly roleRepository: RoleRepository,
    private readonly permissionRepository: PermissionRepository,
    private readonly userProfileRepository: UserProfileRepository,
  ) {}

  /**
   * Get paginated list of custom roles
   */
  async getCustomRoles(options: RoleListOptions = {}) {
    return this.roleRepository.findCustomRoles(options);
  }

  /**
   * Get custom role by ID
   */
  async getCustomRoleById(id: string): Promise<Role> {
    const role = await this.roleRepository.findCustomRoleById(id);
    if (!role) {
      throw new NotFoundException('Custom role not found');
    }
    return role;
  }

  /**
   * Get role by identifier
   */
  async getRoleByIdentifier(identifier: string): Promise<Role> {
    const role = await this.roleRepository.findByIdentifier(identifier);
    if (!role) {
      throw new NotFoundException(
        `Role with identifier '${identifier}' not found`,
      );
    }
    return role;
  }

  /**
   * Create a new custom role
   */
  async createCustomRole(data: CreateRoleData): Promise<Role> {
    // Check if name already exists
    const nameExists = await this.roleRepository.checkNameExistsForCustomRole(
      data.name,
    );
    if (nameExists) {
      throw new ConflictException(
        `Role with name '${data.name}' already exists`,
      );
    }
    data.identifier = data.name.toLowerCase().replace(/\s+/g, '_');
    return this.roleRepository.createCustomRole(data);
  }

  /**
   * Update a custom role
   */
  async updateCustomRole(id: string, data: UpdateRoleData): Promise<Role> {
    // Check if role exists and is custom
    const existingRole = await this.roleRepository.findCustomRoleById(id);
    if (!existingRole) {
      throw new NotFoundException('Custom role not found');
    }

    // Check if name already exists (excluding current role)
    if (data.name) {
      const nameExists = await this.roleRepository.checkNameExistsForCustomRole(
        data.name,
        id,
      );
      if (nameExists) {
        throw new ConflictException(
          `Role with name '${data.name}' already exists`,
        );
      }
    }

    const updatedRole = await this.roleRepository.updateCustomRole(id, data);
    if (!updatedRole) {
      throw new NotFoundException('Custom role not found');
    }

    return updatedRole;
  }

  /**
   * Delete a custom role
   */
  async deleteCustomRole(id: string): Promise<Role> {
    const deletedRole = await this.roleRepository.deleteCustomRole(id);
    if (!deletedRole) {
      throw new NotFoundException('Custom role not found');
    }
    return deletedRole;
  }

  /**
   * Check if role name is available
   */
  async isRoleNameAvailable(
    name: string,
    excludeId?: string,
  ): Promise<boolean> {
    const exists = await this.roleRepository.checkNameExistsForCustomRole(
      name,
      excludeId,
    );
    return !exists;
  }

  /**
   * Add permissions to a role
   */
  async addPermissionsToRole(
    roleId: string,
    permissionIds: string[],
  ): Promise<void> {
    // Check if role exists and is custom
    const role = await this.roleRepository.findCustomRoleById(roleId);
    if (!role) {
      throw new NotFoundException('Custom role not found');
    }

    // Validate that all permission IDs exist
    const permissions =
      await this.permissionRepository.findByIds(permissionIds);
    if (permissions.length !== permissionIds.length) {
      const foundIds = permissions.map((p) => p.id);
      const missingIds = permissionIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(
        `Permissions not found: ${missingIds.join(', ')}`,
      );
    }

    await this.permissionRepository.addPermissionsToRole(roleId, permissionIds);
  }

  /**
   * Remove permissions from a role
   */
  async removePermissionsFromRole(
    roleId: string,
    permissionIds: string[],
  ): Promise<void> {
    // Check if role exists and is custom
    const role = await this.roleRepository.findCustomRoleById(roleId);
    if (!role) {
      throw new NotFoundException('Custom role not found');
    }

    // Validate that all permission IDs exist
    const permissions =
      await this.permissionRepository.findByIds(permissionIds);
    if (permissions.length !== permissionIds.length) {
      const foundIds = permissions.map((p) => p.id);
      const missingIds = permissionIds.filter((id) => !foundIds.includes(id));
      throw new BadRequestException(
        `Permissions not found: ${missingIds.join(', ')}`,
      );
    }

    await this.permissionRepository.removePermissionsFromRole(
      roleId,
      permissionIds,
    );
  }

  /**
   * Get permissions for a role
   */
  async getRolePermissions(roleId: string): Promise<Permission[]> {
    // Check if role exists and is custom
    const role = await this.roleRepository.findCustomRoleById(roleId);
    if (!role) {
      throw new NotFoundException('Custom role not found');
    }

    return this.permissionRepository.getPermissionsByRoleId(roleId);
  }

  /**
   * Get paginated admins for a role
   */
  async getAdminsForRole(roleId: string, options: AdminListOptions) {
    const { page = 1, limit = 10, search, status, exceptCityId } = options;

    // Build where clause
    let whereClause: any = {
      roleId,
      deletedAt: null,
    };

    if (status) {
      whereClause.status = status;
    }

    if (exceptCityId) {
      // Exclude admins assigned to the specified city
      whereClause.cityAdmins = {
        none: { cityId: exceptCityId },
      };
    }

    if (search) {
      whereClause.OR = [
        { firstName: { contains: search, mode: 'insensitive' } },
        { lastName: { contains: search, mode: 'insensitive' } },
        {
          user: {
            email: { contains: search, mode: 'insensitive' },
          },
        },
      ];
    }

    const [profiles, total] = await Promise.all([
      this.userProfileRepository.findMany({
        where: whereClause,
        include: {
          role: {
            select: {
              id: true,
              name: true,
            },
          },
          user: {
            select: {
              id: true,
              email: true,
              phoneNumber: true,
            },
          },
        },
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { createdAt: 'desc' },
      }),
      this.userProfileRepository.count(whereClause),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      admins: profiles,
      page,
      limit,
      total,
      totalPages,
    };
  }
}
