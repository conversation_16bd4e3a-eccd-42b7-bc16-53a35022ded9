import { Module } from '@nestjs/common';
import { ReviewService } from './review.service';
import { ReviewRepository } from '../../repositories/review.repository';
import { RideRepository } from '../../repositories/ride.repository';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { UserMetaDataModule } from '../user-meta-data/user-meta-data.module';
import { PrismaService } from '../../database/prisma/prisma.service';

@Module({
  imports: [UserMetaDataModule],
  providers: [
    ReviewService,
    ReviewRepository,
    RideRepository,
    UserProfileRepository,
    PrismaService,
  ],
  exports: [ReviewService, ReviewRepository],
})
export class ReviewModule {}
