import { CityProduct } from "@shared/shared/repositories/models/cityProduct.model";
import { RideBookingType } from "../../ride/ride-search.service";
import { Zone } from "@shared/shared/repositories/models/zone.model";

export interface FareContext {
    pickup: Zone | null;
    destination: Zone | null;
    stops: Zone[] | null;
    cityProducts: CityProduct[];
    type?: RideBookingType | undefined;
    pickupTime?: string | undefined;
}

export interface FareCalculationResult {
    status: boolean;
    data?: any | undefined;
}