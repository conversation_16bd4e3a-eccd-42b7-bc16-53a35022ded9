import { Injectable, Logger } from "@nestjs/common";
import { FareCalculationResult, FareContext } from "./interfaces/interface";
import { CityProductFareRepository } from "../../repositories/city-product-fare.repository";

@Injectable()
export class FareEngineService {
    private readonly logger = new Logger(FareEngineService.name);

    constructor(
        private readonly cityProductFareRepository: CityProductFareRepository,
    ) { }


    /**
     *  Estimate fare for a ride
     */
    async estimateFare(request: FareContext): Promise<FareCalculationResult> {
        const { pickup, destination, stops, cityProducts } = request;


        return { status: true, data: null };
    }
}

