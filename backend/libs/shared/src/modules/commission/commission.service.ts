import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { Commission, CommissionType, CommissionRepository } from '@shared/shared/repositories';
import { TaxGroupRepository } from '@shared/shared/repositories';

export interface CreateCommissionData {
  name: string;
  description?: string;
  type: CommissionType;
  percentageValue?: number;
  flatValue?: number;
  taxGroupId?: string;
}

export interface UpdateCommissionData {
  name?: string;
  description?: string;
  type?: CommissionType;
  percentageValue?: number;
  flatValue?: number;
  taxGroupId?: string;
}

@Injectable()
export class CommissionService {
  constructor(
    private readonly commissionRepository: CommissionRepository,
    private readonly taxGroupRepository: TaxGroupRepository,
  ) { }

  /**
   * Create a new commission.
   * @param data - Commission creation data
   */
  async createCommission(data: CreateCommissionData): Promise<Commission> {
    // Validate input
    this.validateCommissionData(data);

    // Check for duplicate name
    const existingCommission = await this.commissionRepository.findCommissionByName(data.name);
    if (existingCommission && !existingCommission.deletedAt) {
      throw new BadRequestException(`Commission with name "${data.name}" already exists`);
    }

    // Validate tax group if provided
    if (data.taxGroupId) {
      const taxGroup = await this.taxGroupRepository.findTaxGroupById(data.taxGroupId);
      if (!taxGroup) {
        throw new BadRequestException('Tax group not found');
      }
    }

    return this.commissionRepository.createCommission(data);
  }

  /**
   * Find all commissions.
   */
  async findAllCommissions(): Promise<Commission[]> {
    return this.commissionRepository.findAllCommissions();
  }

  /**
   * Find commission by ID.
   * @param id - Commission ID
   */
  async findCommissionById(id: string): Promise<Commission> {
    const commission = await this.commissionRepository.findCommissionById(id);
    if (!commission) {
      throw new NotFoundException('Commission not found');
    }
    return commission;
  }

  /**
   * Find commissions by type.
   * @param type - Commission type
   */
  async findCommissionsByType(type: CommissionType): Promise<Commission[]> {
    return this.commissionRepository.findCommissionsByType(type);
  }

  /**
   * Update commission by ID.
   * @param id - Commission ID
   * @param data - Updated commission data
   */
  async updateCommission(id: string, data: UpdateCommissionData): Promise<Commission> {
    // Check if commission exists
    const existingCommission = await this.findCommissionById(id);

    // Validate input if type is being changed or values are provided
    if (data.type || data.percentageValue !== undefined || data.flatValue !== undefined) {
      const validationData: any = {
        type: data.type || existingCommission.type,
        percentageValue: data.percentageValue !== undefined ? data.percentageValue : existingCommission.percentageValue,
        flatValue: data.flatValue !== undefined ? data.flatValue : existingCommission.flatValue,
      };
      this.validateCommissionData(validationData);
    }

    // Check for duplicate name (excluding current record)
    if (data.name && data.name !== existingCommission.name) {
      const duplicateCommission = await this.commissionRepository.findCommissionByName(data.name);
      if (duplicateCommission && duplicateCommission.id !== id) {
        throw new BadRequestException(`Commission with name "${data.name}" already exists`);
      }
    }

    // Validate tax group if provided
    if (data.taxGroupId) {
      const taxGroup = await this.taxGroupRepository.findTaxGroupById(data.taxGroupId);
      if (!taxGroup) {
        throw new BadRequestException('Tax group not found');
      }
    }

    return this.commissionRepository.updateCommission(id, data);
  }

  /**
   * Delete commission by ID.
   * @param id - Commission ID
   */
  async deleteCommission(id: string): Promise<Commission> {
    // Check if commission exists
    await this.findCommissionById(id);

    // Check if commission is in use
    const isInUse = await this.commissionRepository.isCommissionInUse(id);
    if (isInUse) {
      throw new BadRequestException('This commission is in use and cannot be deleted');
    }

    // Soft delete commission
    return this.commissionRepository.deleteCommission(id);
  }

  /**
   * Get paginated commissions.
   * @param paginationDto - Pagination parameters
   */
  async paginateCommissions(paginationDto: any) {
    const { page = 1, limit = 10, search, type, taxGroupId } = paginationDto;
    return this.commissionRepository.paginateCommissions(page, limit, { search, type, taxGroupId });
  }

  /**
   * Find commissions by tax group ID.
   * @param taxGroupId - Tax group ID
   */
  async findCommissionsByTaxGroupId(taxGroupId: string): Promise<Commission[]> {
    return this.commissionRepository.findCommissionsByTaxGroupId(taxGroupId);
  }

  /**
   * Validate commission data.
   * @param data - Commission data to validate
   */
  private validateCommissionData(data: Partial<CreateCommissionData>): void {
    if (!data.type) {
      return; // Skip validation if type is not provided (for partial updates)
    }

    if (data.type === CommissionType.PERCENTAGE) {
      if (data.percentageValue === undefined || data.percentageValue === null) {
        throw new BadRequestException('Percentage value is required when type is "percentage"');
      }
      if (data.percentageValue <= 0 || data.percentageValue > 100) {
        throw new BadRequestException('Percentage value must be between 0.01 and 100');
      }
      if (data.flatValue !== undefined && data.flatValue !== null) {
        throw new BadRequestException('Flat value should not be provided when type is "percentage"');
      }
    } else if (data.type === CommissionType.FLAT) {
      if (data.flatValue === undefined || data.flatValue === null) {
        throw new BadRequestException('Flat value is required when type is "flat"');
      }
      if (data.flatValue <= 0) {
        throw new BadRequestException('Flat value must be greater than 0');
      }
      if (data.percentageValue !== undefined && data.percentageValue !== null) {
        throw new BadRequestException('Percentage value should not be provided when type is "flat"');
      }
    }
  }
}
