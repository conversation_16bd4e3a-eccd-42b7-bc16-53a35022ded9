import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { CityProductFareRepository } from '@shared/shared/repositories/city-product-fare.repository';
import { CityProductRepository } from '@shared/shared/repositories/city-product.repository';
import { ChargeGroupRepository } from '@shared/shared/repositories/charge-group.repository';
import { ZoneRepository } from '@shared/shared/repositories/zone.repository';
import { ZoneTypeRepository } from '@shared/shared/repositories/zone-type.repository';
import {
  CityProductFare,
  CityProductFareStatus,
} from '@shared/shared/repositories/models/cityProductFare.model';

@Injectable()
export class CityProductFareService {
  constructor(
    private readonly cityProductFareRepository: CityProductFareRepository,
    private readonly cityProductRepository: CityProductRepository,
    private readonly chargeGroupRepository: ChargeGroupRepository,
    private readonly zoneRepository: ZoneRepository,
    private readonly zoneTypeRepository: ZoneTypeRepository,
  ) {}

  /**
   * Create a new city product fare rule.
   * @param data - City product fare data
   */
  async createCityProductFare(
    data: Omit<CityProductFare, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<CityProductFare> {
    // Validate city product exists and is enabled
    const cityProduct = await this.cityProductRepository.findById(
      data.cityProductId,
    );
    if (!cityProduct) {
      throw new NotFoundException(
        `City product with ID ${data.cityProductId} not found`,
      );
    }
    if (!cityProduct.isEnabled) {
      throw new BadRequestException(
        `City product ${data.cityProductId} is not enabled`,
      );
    }

    // Validate mutual exclusivity: zone IDs and zone type IDs cannot be used together
    const hasZoneIds = data.fromZoneId || data.toZoneId;
    const hasZoneTypeIds = data.fromZoneTypeId || data.toZoneTypeId;

    if (hasZoneIds && hasZoneTypeIds) {
      throw new BadRequestException(
        'Zone IDs (fromZoneId/toZoneId) and zone type IDs (fromZoneTypeId/toZoneTypeId) are mutually exclusive',
      );
    }

    const zoneChecks = [
      { id: data.fromZoneId, repo: this.zoneRepository, label: 'From zone' },
      { id: data.toZoneId, repo: this.zoneRepository, label: 'To zone' },
      {
        id: data.fromZoneTypeId,
        repo: this.zoneTypeRepository,
        label: 'From zone type',
      },
      {
        id: data.toZoneTypeId,
        repo: this.zoneTypeRepository,
        label: 'To zone type',
      },
    ];

    for (const { id, repo, label } of zoneChecks) {
      if (id) {
        const entity = await repo.findById(id);
        if (!entity) {
          throw new NotFoundException(`${label} with ID ${id} not found`);
        }
      }
    }

    const exists = await this.cityProductFareRepository.fareRuleExists(
      data.cityProductId,
      data.fromZoneId,
      data.toZoneId,
      data.fromZoneTypeId,
      data.toZoneTypeId,
    );

    if (exists) {
      throw new ConflictException(
        `Fare rule already exists for city product ${data.cityProductId} with the specified zones`,
      );
    }

    return this.cityProductFareRepository.createCityProductFare(data);
  }

  /**
   * Find all fare rules for a city product.
   * @param cityProductId - City product ID
   */
  async findCityProductFaresByCityProductId(
    cityProductId: string,
  ): Promise<CityProductFare[]> {
    // Validate city product exists
    const cityProduct =
      await this.cityProductRepository.findById(cityProductId);
    if (!cityProduct) {
      throw new NotFoundException(
        `City product with ID ${cityProductId} not found`,
      );
    }

    const result =
      await this.cityProductFareRepository.findCityProductFaresByCityProductId(
        cityProductId,
      );

    if (result.length == 0) {
      await this.createCityProductFare({
        cityProductId: cityProductId,
        priority: 1,
        status: CityProductFareStatus.ACTIVE,
      });
      return this.findCityProductFaresByCityProductId(cityProductId);
    }

    return result;
  }

  /**
   * Find city product fare by ID.
   * @param id - City product fare ID
   */
  async findCityProductFareById(id: string): Promise<CityProductFare> {
    const fare =
      await this.cityProductFareRepository.findCityProductFareById(id);
    if (!fare) {
      throw new NotFoundException(`City product fare with ID ${id} not found`);
    }
    return fare;
  }

  /**
   * Update city product fare by ID.
   * @param id - City product fare ID
   * @param data - Partial city product fare data
   */
  async updateCityProductFare(
    id: string,
    data: Partial<CityProductFare>,
  ): Promise<CityProductFare> {
    // Check if fare exists
    await this.findCityProductFareById(id);

    // Validate zones if being updated
    if (data.fromZoneId) {
      const fromZone = await this.zoneRepository.findById(data.fromZoneId);
      if (!fromZone) {
        throw new NotFoundException(
          `From zone with ID ${data.fromZoneId} not found`,
        );
      }
    }

    if (data.toZoneId) {
      const toZone = await this.zoneRepository.findById(data.toZoneId);
      if (!toZone) {
        throw new NotFoundException(
          `To zone with ID ${data.toZoneId} not found`,
        );
      }
    }

    return this.cityProductFareRepository.updateCityProductFare(id, data);
  }

  /**
   * Delete city product fare by ID (soft delete).
   * @param id - City product fare ID
   */
  async deleteCityProductFare(id: string): Promise<CityProductFare> {
    // Check if fare exists
    await this.findCityProductFareById(id);

    return this.cityProductFareRepository.deleteCityProductFare(id);
  }

  /**
   * Get paginated city product fares.
   * @param page - Page number
   * @param limit - Items per page
   * @param options - Additional query options
   */
  async paginateCityProductFares(page = 1, limit = 10, options?: any) {
    return this.cityProductFareRepository.paginateCityProductFares(
      page,
      limit,
      options,
    );
  }

  /**
   * Attach charge groups to a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupIds - Array of charge group IDs with priorities
   */
  async attachChargeGroupsToFare(
    cityProductFareId: string,
    chargeGroupIds: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    // Validate fare exists
    await this.findCityProductFareById(cityProductFareId);

    // Validate all charge groups exist
    for (const { chargeGroupId } of chargeGroupIds) {
      const chargeGroup =
        await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
      if (!chargeGroup) {
        throw new NotFoundException(
          `Charge group with ID ${chargeGroupId} not found`,
        );
      }
    }

    // Check for duplicate priorities
    const priorities = chargeGroupIds.map((cg) => cg.priority);
    const uniquePriorities = new Set(priorities);
    if (priorities.length !== uniquePriorities.size) {
      throw new BadRequestException('Duplicate priorities are not allowed');
    }

    return this.cityProductFareRepository.attachChargeGroupsToFare(
      cityProductFareId,
      chargeGroupIds,
    );
  }

  /**
   * Detach a charge group from a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupId - Charge group ID
   */
  async detachChargeGroupFromFare(
    cityProductFareId: string,
    chargeGroupId: string,
  ): Promise<void> {
    // Validate fare exists
    await this.findCityProductFareById(cityProductFareId);

    // Validate charge group exists
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
    if (!chargeGroup) {
      throw new NotFoundException(
        `Charge group with ID ${chargeGroupId} not found`,
      );
    }

    return this.cityProductFareRepository.detachChargeGroupFromFare(
      cityProductFareId,
      chargeGroupId,
    );
  }

  /**
   * Update charge group priorities for a fare rule.
   * @param cityProductFareId - City product fare ID
   * @param chargeGroupPriorities - Array of charge group IDs with new priorities
   */
  async updateChargeGroupPriorities(
    cityProductFareId: string,
    chargeGroupPriorities: Array<{ chargeGroupId: string; priority: number }>,
  ): Promise<any[]> {
    // Validate fare exists
    await this.findCityProductFareById(cityProductFareId);

    // Validate all charge groups exist
    for (const { chargeGroupId } of chargeGroupPriorities) {
      const chargeGroup =
        await this.chargeGroupRepository.findChargeGroupById(chargeGroupId);
      if (!chargeGroup) {
        throw new NotFoundException(
          `Charge group with ID ${chargeGroupId} not found`,
        );
      }
    }

    // Check for duplicate priorities
    const priorities = chargeGroupPriorities.map((cg) => cg.priority);
    const uniquePriorities = new Set(priorities);
    if (priorities.length !== uniquePriorities.size) {
      throw new BadRequestException('Duplicate priorities are not allowed');
    }

    return this.cityProductFareRepository.updateChargeGroupPriorities(
      cityProductFareId,
      chargeGroupPriorities,
    );
  }

  /**
   * Get charge groups for a specific fare rule.
   * @param cityProductFareId - City product fare ID
   */
  async getFareChargeGroups(cityProductFareId: string): Promise<any[]> {
    // Validate fare exists
    await this.findCityProductFareById(cityProductFareId);

    return this.cityProductFareRepository.getFareChargeGroups(
      cityProductFareId,
    );
  }

  /**
   * Find fare rules that match pickup and destination zones.
   * Implements the logic: fromZone=(pickup_zone OR NULL) AND toZone=(dest_zone OR NULL)
   * @param pickupZoneId - Pickup zone ID (can be null for default rules)
   * @param destinationZoneId - Destination zone ID (can be null for default rules)
   */
  async findMatchingFareRules(
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
  ): Promise<CityProductFare[]> {
    return this.cityProductFareRepository.findMatchingFareRules(
      pickupZoneId,
      destinationZoneId,
    );
  }

  /**
   * Select the best fare rule based on priority for given zones.
   * @param pickupZoneId - Pickup zone ID
   * @param destinationZoneId - Destination zone ID
   */
  async selectBestFareRule(
    pickupZoneId?: string | null,
    destinationZoneId?: string | null,
  ): Promise<CityProductFare | null> {
    const matchingFares = await this.findMatchingFareRules(
      pickupZoneId,
      destinationZoneId,
    );

    if (matchingFares.length === 0) {
      return null;
    }

    // Return the fare with highest priority (lowest priority number)
    return matchingFares.sort((a, b) => a.priority - b.priority)[0];
  }
}
