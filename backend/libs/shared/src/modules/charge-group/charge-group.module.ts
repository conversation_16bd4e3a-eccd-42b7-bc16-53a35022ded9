import { Module } from '@nestjs/common';
import { ChargeGroupService } from './charge-group.service';
import { ChargeGroupRepository } from '../../repositories/charge-group.repository';
import { RepositoryModule } from '../../repositories/repository.module';

@Module({
  imports: [RepositoryModule],
  providers: [ChargeGroupService, ChargeGroupRepository],
  exports: [ChargeGroupService],
})
export class ChargeGroupModule {}
