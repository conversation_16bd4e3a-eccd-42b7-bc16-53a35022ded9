import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { ChargeGroup } from '../../repositories/models/chargeGroup.model';
import { ChargeGroupRepository } from '@shared/shared/repositories';

@Injectable()
export class ChargeGroupService {
  constructor(private readonly chargeGroupRepository: ChargeGroupRepository) {}

  /**
   * Create a new charge group.
   * @param data - Charge group data excluding id and timestamps
   */
  async createChargeGroup(
    data: Omit<ChargeGroup, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<ChargeGroup> {
    const existingByName = await this.chargeGroupRepository.findOne({
      where: {
        identifier: {
          equals: data.identifier,
          mode: 'insensitive',
        },
      },
      includeSoftDeleted: true,
    });
    if (existingByName && !existingByName.deletedAt) {
      throw new BadRequestException(
        `Charge group with name "${data.name}" already exists`,
      );
    }

    if (existingByName && existingByName.deletedAt) {
      await this.chargeGroupRepository.restore({
        where: { id: existingByName.id },
      });
      return this.chargeGroupRepository.updateChargeGroup(existingByName.id, {
        ...data,
        deletedAt: null,
      });
    }

    let identifier = data.identifier;
    if (!identifier) {
      identifier = data.name.toLowerCase().replace(/\s+/g, '_');
    }

    const existingByIdentifier =
      await this.chargeGroupRepository.findChargeGroupByIdentifier(identifier);
    if (existingByIdentifier) {
      throw new BadRequestException(
        `Charge group with identifier "${identifier}" already exists`,
      );
    }

    return this.chargeGroupRepository.createChargeGroup({
      ...data,
      identifier,
    });
  }

  /**
   * Find all charge groups.
   */
  async findAllChargeGroups(): Promise<ChargeGroup[]> {
    return this.chargeGroupRepository.findAllChargeGroups();
  }

  /**
   * Find charge group by ID.
   * @param id - Charge group ID
   */
  async findChargeGroupById(id: string): Promise<ChargeGroup> {
    const chargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(id);
    if (!chargeGroup) {
      throw new NotFoundException(`Charge group with ID ${id} not found`);
    }
    return chargeGroup;
  }

  /**
   * Update charge group by ID.
   * @param id - Charge group ID
   * @param data - Partial charge group data
   */
  async updateChargeGroup(
    id: string,
    data: Partial<ChargeGroup>,
  ): Promise<ChargeGroup> {
    // Check if charge group exists
    const existingChargeGroup =
      await this.chargeGroupRepository.findChargeGroupById(id);
    if (!existingChargeGroup) {
      throw new NotFoundException(`Charge group with ID ${id} not found`);
    }

    // Check for duplicate name (excluding current record)
    if (data.name) {
      const existingByName = await this.chargeGroupRepository.findOne({
        where: {
          name: {
            equals: data.name,
            mode: 'insensitive',
          },
        },
        select: { id: true },
      });
      if (existingByName && existingByName.id !== id) {
        throw new BadRequestException(
          `Charge group with name "${data.name}" already exists`,
        );
      }
    }

    // Don't allow identifier to be changed
    if (data.identifier !== undefined) {
      delete data.identifier;
    }

    return this.chargeGroupRepository.updateChargeGroup(id, data);
  }

  /**
   * Delete charge group by ID (soft delete).
   * @param id - Charge group ID
   */
  async deleteChargeGroup(id: string): Promise<ChargeGroup> {
    // Check if charge group exists
    await this.findChargeGroupById(id);

    return this.chargeGroupRepository.deleteChargeGroup(id);
  }

  /**
   * Get paginated charge groups.
   * @param paginationDto - Pagination parameters
   */
  async paginateChargeGroups(paginationDto: any) {
    const { page = 1, limit = 10 } = paginationDto;
    const options = this.buildPaginateOptions(paginationDto);
    return this.chargeGroupRepository.paginateChargeGroups(
      page,
      limit,
      options,
    );
  }

  /**
   * Find charge groups by name.
   * @param name - Name to search for
   */
  async findChargeGroupsByName(name: string): Promise<ChargeGroup[]> {
    return this.chargeGroupRepository.findChargeGroupsByName(name);
  }

  private buildPaginateOptions(dto?: any) {
    const options: any = {};

    if (dto) {
      const whereConditions: any = {};

      if (dto.search) {
        whereConditions.name = {
          contains: dto.search,
          mode: 'insensitive',
        };
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }

      // Build orderBy clause
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      } else {
        // Default sorting by createdAt desc if no sortBy is provided
        options.orderBy = { createdAt: 'desc' };
      }
    } else {
      // Default sorting when no dto is provided
      options.orderBy = { createdAt: 'desc' };
    }

    return options;
  }
}
