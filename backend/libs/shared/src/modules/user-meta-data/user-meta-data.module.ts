import { Module } from '@nestjs/common';
import { UserMetaDataService } from './user-meta-data.service';
import { UserMetaDataRepository } from '../../repositories/user-meta-data.repository';
import { ReviewRepository } from '../../repositories/review.repository';
import { PrismaService } from '../../database/prisma/prisma.service';

@Module({
  providers: [
    UserMetaDataService,
    UserMetaDataRepository,
    ReviewRepository,
    PrismaService,
  ],
  exports: [UserMetaDataService, UserMetaDataRepository],
})
export class UserMetaDataModule {}
