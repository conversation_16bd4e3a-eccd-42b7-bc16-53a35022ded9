import { Injectable, Logger } from '@nestjs/common';
import { UserMetaDataRepository } from '../../repositories/user-meta-data.repository';
import { ReviewRepository } from '../../repositories/review.repository';
import {
  UserMetaData,
  CreateUserMetaDataData,
  UpdateUserMetaDataData,
  UserMetaDataWithProfile,
} from '../../repositories/models/userMetaData.model';

@Injectable()
export class UserMetaDataService {
  private readonly logger = new Logger(UserMetaDataService.name);

  constructor(
    private readonly userMetaDataRepository: UserMetaDataRepository,
    private readonly reviewRepository: ReviewRepository,
  ) {}

  /**
   * Get user metadata by user profile ID
   */
  async getUserMetaData(userProfileId: string): Promise<UserMetaData | null> {
    return this.userMetaDataRepository.findByUserProfileId(userProfileId);
  }

  /**
   * Get user metadata with profile details
   */
  async getUserMetaDataWithProfile(
    userProfileId: string,
  ): Promise<UserMetaDataWithProfile | null> {
    return this.userMetaDataRepository.findByUserProfileIdWithProfile(
      userProfileId,
    );
  }

  /**
   * Create user metadata
   */
  async createUserMetaData(
    data: CreateUserMetaDataData,
  ): Promise<UserMetaData> {
    this.logger.log(
      `Creating user metadata for user profile ${data.userProfileId}`,
    );
    return this.userMetaDataRepository.createUserMetaData(data);
  }

  /**
   * Update user metadata
   */
  async updateUserMetaData(
    userProfileId: string,
    data: UpdateUserMetaDataData,
  ): Promise<UserMetaData> {
    this.logger.log(`Updating user metadata for user profile ${userProfileId}`);
    return this.userMetaDataRepository.updateByUserProfileId(
      userProfileId,
      data,
    );
  }

  /**
   * Increment rides completed count for a user
   */
  async incrementRidesCompleted(userProfileId: string): Promise<UserMetaData> {
    this.logger.log(
      `Incrementing rides completed for user profile ${userProfileId}`,
    );

    try {
      const updatedMetaData =
        await this.userMetaDataRepository.incrementRidesCompleted(
          userProfileId,
        );
      this.logger.log(
        `Successfully incremented rides completed for user ${userProfileId}. New count: ${updatedMetaData.ridesCompleted}`,
      );
      return updatedMetaData;
    } catch (error) {
      this.logger.error(
        `Failed to increment rides completed for user ${userProfileId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Update average rating for a user based on all their reviews
   */
  async updateAverageRating(
    userProfileId: string,
    asRole: 'driver' | 'rider',
  ): Promise<UserMetaData> {
    this.logger.log(
      `Updating average rating for user profile ${userProfileId} as ${asRole}`,
    );

    try {
      // Get review statistics for the user
      const reviewStats = await this.reviewRepository.getUserReviewStats(
        userProfileId,
        asRole,
      );

      if (reviewStats.totalReviews === 0) {
        this.logger.log(
          `No reviews found for user ${userProfileId} as ${asRole}`,
        );
        // If no reviews, ensure metadata exists but don't update rating
        return this.userMetaDataRepository.upsertUserMetaData(
          userProfileId,
          {},
        );
      }

      // Update the average rating
      const updatedMetaData =
        await this.userMetaDataRepository.updateAverageRating(
          userProfileId,
          reviewStats.averageRating,
        );

      this.logger.log(
        `Successfully updated average rating for user ${userProfileId}. New rating: ${reviewStats.averageRating} (based on ${reviewStats.totalReviews} reviews)`,
      );

      return updatedMetaData;
    } catch (error) {
      this.logger.error(
        `Failed to update average rating for user ${userProfileId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Handle ride completion - increment rides completed for both rider and driver
   */
  async handleRideCompletion(riderId: string, driverId: string): Promise<void> {
    this.logger.log(
      `Handling ride completion for rider ${riderId} and driver ${driverId}`,
    );

    try {
      // Increment rides completed for both rider and driver
      await Promise.all([
        this.incrementRidesCompleted(riderId),
        this.incrementRidesCompleted(driverId),
      ]);

      this.logger.log(
        `Successfully updated ride completion counts for rider ${riderId} and driver ${driverId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to handle ride completion for rider ${riderId} and driver ${driverId}:`,
        error,
      );
      // Don't throw error to avoid failing the ride completion process
    }
  }

  /**
   * Handle new review - update average rating for the reviewed user
   */
  async handleNewReview(
    reviewedUserId: string,
    reviewedUserRole: 'driver' | 'rider',
  ): Promise<void> {
    this.logger.log(
      `Handling new review for user ${reviewedUserId} as ${reviewedUserRole}`,
    );

    try {
      await this.updateAverageRating(reviewedUserId, reviewedUserRole);
      this.logger.log(
        `Successfully updated average rating after new review for user ${reviewedUserId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to handle new review for user ${reviewedUserId}:`,
        error,
      );
      // Don't throw error to avoid failing the review creation process
    }
  }

  /**
   * Get user statistics
   */
  async getUserStats(userProfileId: string): Promise<{
    avgRating: number | null;
    ridesCompleted: number;
    rank?: number;
  }> {
    return this.userMetaDataRepository.getUserStats(userProfileId);
  }

  /**
   * Get top rated users
   */
  async getTopRatedUsers(
    limit: number = 10,
  ): Promise<UserMetaDataWithProfile[]> {
    return this.userMetaDataRepository.getTopRatedUsers(limit);
  }

  /**
   * Get most active users (by rides completed)
   */
  async getMostActiveUsers(
    limit: number = 10,
  ): Promise<UserMetaDataWithProfile[]> {
    return this.userMetaDataRepository.getMostActiveUsers(limit);
  }

  /**
   * Initialize user metadata if it doesn't exist
   */
  async initializeUserMetaData(userProfileId: string): Promise<UserMetaData> {
    this.logger.log(
      `Initializing user metadata for user profile ${userProfileId}`,
    );

    const existing = await this.getUserMetaData(userProfileId);
    if (existing) {
      return existing;
    }

    return this.createUserMetaData({
      userProfileId,
      ridesCompleted: 0,
    });
  }

  /**
   * Recalculate all user statistics (useful for data migration or cleanup)
   */
  async recalculateUserStats(userProfileId: string): Promise<UserMetaData> {
    this.logger.log(
      `Recalculating all statistics for user profile ${userProfileId}`,
    );

    try {
      // Get review stats for both driver and rider roles
      const [driverStats, riderStats] = await Promise.all([
        this.reviewRepository.getUserReviewStats(userProfileId, 'driver'),
        this.reviewRepository.getUserReviewStats(userProfileId, 'rider'),
      ]);

      // Use the role with more reviews for the average rating
      let avgRating: number | undefined;
      if (driverStats.totalReviews > 0 && riderStats.totalReviews > 0) {
        // If user has reviews in both roles, use weighted average
        const totalReviews = driverStats.totalReviews + riderStats.totalReviews;
        avgRating =
          (driverStats.averageRating * driverStats.totalReviews +
            riderStats.averageRating * riderStats.totalReviews) /
          totalReviews;
      } else if (driverStats.totalReviews > 0) {
        avgRating = driverStats.averageRating;
      } else if (riderStats.totalReviews > 0) {
        avgRating = riderStats.averageRating;
      }

      // Update metadata with recalculated values
      const updateData: UpdateUserMetaDataData = {};
      if (avgRating !== undefined) {
        updateData.avgRating = avgRating;
      }

      const updatedMetaData =
        await this.userMetaDataRepository.upsertUserMetaData(
          userProfileId,
          updateData,
        );

      this.logger.log(
        `Successfully recalculated statistics for user ${userProfileId}`,
      );
      return updatedMetaData;
    } catch (error) {
      this.logger.error(
        `Failed to recalculate statistics for user ${userProfileId}:`,
        error,
      );
      throw error;
    }
  }
}
