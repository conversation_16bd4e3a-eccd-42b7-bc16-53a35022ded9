import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { UserProfileRepository } from '../../repositories/user-profile.repository';
import { UserOnboardingService } from '../user-onboarding/user-onboarding.service';
import { UserProfile } from '../../repositories/models/userProfile.model';
import { OnboardingStep } from '../../repositories/models/userOnboard.model';
import {
  AuthCredentialRepository,
  RoleRepository,
  UserRepository,
} from '@shared/shared/repositories';
import { OtpService } from '../auth/services/otp.service';
import { UserRegistrationService } from '../auth/services/user-registration.service';
import { UserRoleRepository } from '../../repositories/role.repository';
import { UpdateProfileDto } from 'apps/api/src/v1/user-profile/dto/update-profile.dto';
import { UserProfileRedisService } from './user-profile-redis.service';
import { NotificationService } from '../../common/notifications/engagespot/engagespot.service';
import { AppConfigService } from '../../config/config.service';
import * as speakeasy from 'speakeasy';
import { AuthProvider } from '@shared/shared/common/constants/constants';
import { LanguageService } from '../language/language.service';
import { KycDocumentService } from '../kyc-document/kyc-document.service';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { ApiConsumer } from '../auth/interfaces';
import { CityAdminRepository } from '../../repositories/city-admin.repository';

interface ProfileUpdateResult {
  profile: UserProfile;
  emailVerificationRequired?: boolean;
  phoneVerificationRequired?: boolean;
}

@Injectable()
export class UserProfileService {
  private readonly logger = new Logger(UserProfileService.name);
  constructor(
    private readonly userRepository: UserRepository,
    private readonly userProfileRepository: UserProfileRepository,
    private readonly userOnboardingService: UserOnboardingService,
    private readonly roleRepository: RoleRepository,
    private readonly userProfileRedisService: UserProfileRedisService,
    private readonly notificationService: NotificationService,
    private readonly configService: AppConfigService,
    private readonly authCredentialRepository: AuthCredentialRepository,
    private readonly userRoleRepository: UserRoleRepository,
    private readonly otpService: OtpService,
    private readonly userRegistrationService: UserRegistrationService,
    private readonly languageService: LanguageService,
    private readonly kycDocumentService: KycDocumentService,
    private readonly fileUploadService: FileUploadService,
    private readonly cityAdminRepository: CityAdminRepository,
  ) {}

  /**
   * Get a user profile by user ID and role name
   * @param userId User ID
   * @param roleName Role name (e.g., 'driver', 'rider')
   * @returns User profile with user details
   */
  async getProfileByUserIdAndRoleName(
    userId: string,
    roleName: string,
  ): Promise<{
    profile: UserProfile | null;
    user: any;
  }> {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const profile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );

    return { profile, user };
  }

  async getProfileByUserIdAndRoleId(
    userId: string,
    roleId: string,
  ): Promise<UserProfile | null> {
    return this.userProfileRepository.getOneByUserIdAndRoleId(userId, roleId);
  }
  /**
   * Update online status for a user profile
   * @param userProfileId User profile ID
   * @param isOnline Online status
   * @returns Updated user profile
   */
  async updateOnlineStatus(
    userProfileId: string,
    isOnline: boolean,
  ): Promise<UserProfile> {
    const existingProfile =
      await this.userProfileRepository.findById(userProfileId);
    if (!existingProfile) {
      throw new NotFoundException(
        `User profile with ID ${userProfileId} not found`,
      );
    }

    return this.userProfileRepository.updateUserProfileById(userProfileId, {
      isOnline,
    });
  }

  /**
   * Update user profile status
   * @param userProfileId User profile ID
   * @param status New status
   * @returns Updated user profile
   */
  async updateUserProfileStatus(
    userProfileId: string,
    status: any,
  ): Promise<UserProfile> {
    const existingProfile =
      await this.userProfileRepository.findById(userProfileId);
    if (!existingProfile) {
      throw new NotFoundException(
        `User profile with ID ${userProfileId} not found`,
      );
    }

    return this.userProfileRepository.updateUserProfileById(userProfileId, {
      status,
    });
  }

  /**
   * Update a user profile with optional email and phone verification
   * @param userId User ID
   * @param roleName Role name (e.g., 'driver', 'rider')
   * @param dto Update profile DTO
   * @returns Updated profile and verification flags
   */
  async updateUserProfile(
    userId: string,
    roleName: string,
    dto: UpdateProfileDto,
  ): Promise<ProfileUpdateResult> {
    const role = await this.roleRepository.findByName(roleName);
    if (!role) {
      throw new NotFoundException(`Role '${roleName}' not found`);
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const result: ProfileUpdateResult = {
      profile: null as any,
      emailVerificationRequired: false,
      phoneVerificationRequired: false,
    };

    if (dto.email && dto.email !== user.email) {
      // Check if email is already in use by another user
      const existingUser = await this.userRepository.findByEmail(dto.email);
      if (existingUser && existingUser.id !== userId) {
        throw new BadRequestException(`Email ${dto.email} is already in use`);
      }

      result.emailVerificationRequired = true;

      this.sendEmailOtp(userId, dto.email);
    }
    // Handle phone verification if phone is provided and different
    if (dto.phone && dto.phone !== user.phoneNumber) {
      const existingUser = await this.userRepository.findByPhoneNumber(
        dto.phone,
      );
      if (existingUser && existingUser.id !== userId) {
        throw new BadRequestException(
          `Phone number ${dto.phone} is already in use`,
        );
      }

      // Phone needs verification
      result.phoneVerificationRequired = true;
      this.sendPhoneOtp(userId, dto.phone);
    }
    this.logger.log('Updating user profile', { userId, roleName, dto });
    const { email, phone, ...profileData } = dto;
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      role.id,
    );

    if (existing) {
      result.profile = await this.userProfileRepository.updateUserProfileById(
        existing.id,
        profileData as any,
      );
    } else {
      result.profile = await this.userProfileRepository.createUserProfile(
        userId,
        role.id,
        profileData as any,
      );
    }

    return result;
  }

  /**
   * Verify email update with OTP
   * @param userId User ID
   * @param email Email to verify
   * @param otp OTP code
   * @returns True if verification was successful
   */
  /**
   * Generate and send OTP for email verification
   * @param userId User ID
   * @param email Email address
   * @returns True if OTP was sent successfully
   */
  async sendEmailOtp(userId: string, email: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    this.logger.debug(`OTP for ${email}: ${otpToken}`);

    // Store OTP data in Redis
    await this.userProfileRedisService.storeProfileOtpData(
      {
        userId,
        email,
        otpSecret: secret,
        createdAt: new Date(),
      },
      900,
    ); // 15 minutes expiration

    // Send OTP notification
    this.notificationService.sendNotification(
      this.configService.emailVerificationCodeWorkflow,
      [{ identifier: user.id, email }],
      { otp: otpToken },
    );

    return true;
  }

  /**
   * Generate and send OTP for phone verification
   * @param userId User ID
   * @param phone Phone number
   * @returns True if OTP was sent successfully
   */
  async sendPhoneOtp(userId: string, phone: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    const secret = speakeasy.generateSecret({ length: 20 }).base32;
    const otpToken = speakeasy.totp({
      secret,
      digits: 4,
      step: 300,
    });
    this.logger.debug(`OTP for ${phone}: ${otpToken}`);

    // Store OTP data in Redis
    await this.userProfileRedisService.storeProfileOtpData(
      {
        userId,
        phone,
        otpSecret: secret,
        createdAt: new Date(),
      },
      900,
    ); // 15 minutes expiration

    // Send OTP notification
    this.notificationService.sendNotification(
      this.configService.verificationCodeLoginWorkflow,
      [{ identifier: user.id, phoneNumber: phone }],
      { otp: otpToken },
    );

    return true;
  }

  async verifyEmailUpdate(
    userId: string,
    email: string,
    otp: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }
    //DEVONLY:
    if (this.configService.isStaging && otp === '2299') {
      await this.userRepository.updateUserEmail(userId, email);
      await this.authCredentialRepository.createOrLinkCredential(
        AuthProvider.EMAIL,
        email,
        user.id,
        null,
      );
      return true;
    }

    let isValid = false;
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.email || otpData.email !== email) {
      throw new UnauthorizedException('No pending email verification found');
    }

    isValid = speakeasy.totp.verify({
      secret: otpData.otpSecret,
      token: otp,
      digits: 4,
      step: 300,
      window: 1,
    });

    if (isValid) {
      await this.userProfileRedisService.deleteProfileOtpData(userId);
    }
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    await this.userRepository.updateUserEmail(userId, email);

    await this.authCredentialRepository.createOrLinkCredential(
      AuthProvider.EMAIL,
      email,
      user.id,
      null,
    );

    return true;
  }

  /**
   * Verify email update with OTP (using BadRequestException instead of UnauthorizedException)
   * @param userId User ID
   * @param email Email address to verify
   * @param otp OTP code
   * @returns True if verification was successful
   */
  async verifyEmailUpdateAdmin(
    userId: string,
    email: string,
    otp: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }
    //DEVONLY:
    if (this.configService.isStaging && otp === '2299') {
      await this.userRepository.updateUserEmail(userId, email);
      await this.authCredentialRepository.createOrLinkCredential(
        AuthProvider.EMAIL,
        email,
        user.id,
        null,
      );
      return true;
    }

    let isValid = false;
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.email || otpData.email !== email) {
      throw new BadRequestException('No pending email verification found');
    }

    isValid = speakeasy.totp.verify({
      secret: otpData.otpSecret,
      token: otp,
      digits: 4,
      step: 300,
      window: 1,
    });

    if (isValid) {
      await this.userProfileRedisService.deleteProfileOtpData(userId);
    }
    if (!isValid) {
      throw new BadRequestException('Invalid OTP');
    }

    await this.userRepository.updateUserEmail(userId, email);

    await this.authCredentialRepository.createOrLinkCredential(
      AuthProvider.EMAIL,
      email,
      user.id,
      null,
    );

    return true;
  }

  /**
   * Verify phone update with OTP
   * @param userId User ID
   * @param phone Phone number to verify
   * @param otp OTP code
   * @returns True if verification was successful
   */
  async verifyPhoneUpdate(
    userId: string,
    phone: string,
    otp: string,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    //DEVONLY:
    if (this.configService.isStaging && otp === '2299') {
      await this.userRepository.updateById(userId, {
        phoneNumber: phone,
        phoneVerifiedAt: new Date(),
      });
      await this.authCredentialRepository.createOrLinkCredential(
        AuthProvider.PHONE,
        phone,
        user.id,
        null,
      );
      return true;
    }

    let isValid = false;
    // Get OTP data from Redis
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.phone || otpData.phone !== phone) {
      throw new UnauthorizedException('No pending phone verification found');
    }

    // Verify OTP
    isValid = speakeasy.totp.verify({
      secret: otpData.otpSecret,
      token: otp,
      digits: 4,
      step: 300, // 5 minutes
      window: 1, // Allow 1 step before/after for clock drift
    });

    if (isValid) {
      // Delete OTP data from Redis after successful verification
      await this.userProfileRedisService.deleteProfileOtpData(userId);
    }
    if (!isValid) {
      throw new UnauthorizedException('Invalid OTP');
    }

    // Update user phone
    await this.userRepository.updateById(userId, {
      phoneNumber: phone,
      phoneVerifiedAt: new Date(),
    });

    // update the AuthCredRepository
    await this.authCredentialRepository.createOrLinkCredential(
      AuthProvider.PHONE,
      phone,
      user.id,
      null, // No metadata needed for phone auth
    );

    return true;
  }

  /**
   * Create or update a user profile by userId and roleId. If new, also update onboarding step.
   */
  async createOrUpdateDriverProfile(
    userId: string,
    dto: any,
  ): Promise<UserProfile> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const { email, ...data } = dto;
    const roleId = driverRole?.id;
    if (email) {
      const userExists = await this.userRepository.findDriverByEmail(
        email,
        roleId,
      );
      if (userExists && userExists.id !== userId) {
        throw new BadRequestException(
          `User with email ${email} already exists`,
        );
      }
      //update user email if it exists
      await this.userRepository.updateUserEmail(userId, email);
    }
    if (data.dob) {
      const dobDate = new Date(data.dob);
      if (isNaN(dobDate.getTime())) {
        throw new BadRequestException('Invalid date of birth format');
      }
      data.dob = dobDate;
    }
    // Check if profile exists
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    let profile: UserProfile;
    if (existing) {
      profile = await this.userProfileRepository.updateUserProfileById(
        existing.id,
        data as any,
      );
    } else {
      profile = await this.userProfileRepository.createUserProfile(
        userId,
        roleId,
        data as any,
      );
    }
    // If new, update onboarding step to PROFILE_SETUP
    await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
      userId,
      roleId,
      OnboardingStep.PROFILE_SETUP,
    );
    return profile;
  }

  async updateDriverLanguage(
    userId: string,
    languageId: string,
  ): Promise<UserProfile> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const roleId = driverRole.id;
    const existing = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    let profile: UserProfile;
    // Check if profile exists
    if (existing) {
      profile = await this.userProfileRepository.updateUserProfileById(
        existing.id,
        { languageId } as any,
      );
    } else {
      profile = await this.userProfileRepository.createUserProfile(
        userId,
        roleId,
        { languageId } as any,
      );
    }
    // If new, update onboarding step to PROFILE_SETUP
    await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
      userId,
      roleId,
      OnboardingStep.LANGUAGE_UPDATE,
    );
    return profile;
  }

  async findDriverProfileByUserId(userId: string): Promise<UserProfile | null> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }
    const roleId = driverRole.id;
    const profile = await this.userProfileRepository.getOneByUserIdAndRoleId(
      userId,
      roleId,
    );
    if (!profile) {
      throw new NotFoundException('Driver profile not found');
    }
    // If not in cache, return the profile from DB
    return profile;
  }

  /**
   * Find user profile by ID
   * @param id User profile ID
   * @returns User profile
   */
  async findUserProfileById(id: string): Promise<UserProfile> {
    const profile = await this.userProfileRepository.findById(id);
    if (!profile) {
      throw new NotFoundException(`User profile with ID ${id} not found`);
    }
    return profile;
  }

  /**
   * Resend OTP for email verification
   * @param userId User ID
   * @param email Email address to resend OTP to
   * @returns True if OTP was resent successfully
   */
  async resendEmailOtp(userId: string, email: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    // Check if there's a pending email verification for this user
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.email) {
      throw new BadRequestException('No pending email verification found');
    }

    // Verify the email matches the pending verification
    if (otpData.email !== email) {
      throw new BadRequestException(
        'Email does not match pending verification',
      );
    }

    // Check if enough time has passed since the last OTP was sent (rate limiting)
    const timeSinceLastOtp = Date.now() - new Date(otpData.createdAt).getTime();
    const minResendInterval = 60 * 1000; // 1 minute minimum between resends

    if (timeSinceLastOtp < minResendInterval) {
      const remainingTime = Math.ceil(
        (minResendInterval - timeSinceLastOtp) / 1000,
      );
      throw new BadRequestException(
        `Please wait ${remainingTime} seconds before requesting another OTP`,
      );
    }

    this.logger.log(`Resending email OTP for user ${userId} to ${email}`);

    // Resend OTP using existing method (this will overwrite the existing OTP in Redis)
    return this.sendEmailOtp(userId, email);
  }

  /**
   * Resend OTP for phone verification
   * @param userId User ID
   * @param phone Phone number to resend OTP to
   * @returns True if OTP was resent successfully
   */
  async resendPhoneOtp(userId: string, phone: string): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    // Check if there's a pending phone verification for this user
    const otpData =
      await this.userProfileRedisService.getProfileOtpData(userId);
    if (!otpData || !otpData.phone) {
      throw new BadRequestException('No pending phone verification found');
    }

    // Verify the phone matches the pending verification
    if (otpData.phone !== phone) {
      throw new BadRequestException(
        'Phone number does not match pending verification',
      );
    }

    // Check if enough time has passed since the last OTP was sent (rate limiting)
    const timeSinceLastOtp = Date.now() - new Date(otpData.createdAt).getTime();
    const minResendInterval = 60 * 1000; // 1 minute minimum between resends

    if (timeSinceLastOtp < minResendInterval) {
      const remainingTime = Math.ceil(
        (minResendInterval - timeSinceLastOtp) / 1000,
      );
      throw new BadRequestException(
        `Please wait ${remainingTime} seconds before requesting another OTP`,
      );
    }

    this.logger.log(`Resending phone OTP for user ${userId} to ${phone}`);

    // Resend OTP using existing method (this will overwrite the existing OTP in Redis)
    return this.sendPhoneOtp(userId, phone);
  }

  /**
   * Update terms and conditions acceptance status
   * @param userId User ID
   * @param accepted Boolean indicating whether terms are accepted
   * @returns True if update was successful
   */
  async updateTermsConditionsAcceptance(
    userId: string,
    accepted: boolean,
  ): Promise<boolean> {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new NotFoundException(`User with ID '${userId}' not found`);
    }

    // Update user's terms and conditions acceptance status
    await this.userRepository.updateTermsAndConditionsAcceptance(
      userId,
      accepted,
    );

    return true;
  }
  // ==================== ADMIN DRIVER MANAGEMENT METHODS ====================

  /**
   * Change driver status (Admin only)
   * @param id Driver profile ID
   * @param newStatus New status to set
   * @returns Status change result
   */
  async changeDriverStatus(
    id: string,
    newStatus: string,
  ): Promise<{
    id: string;
    status: string;
    previousStatus: string;
    message: string;
    updatedAt: Date;
  }> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }

    // Find driver profile
    const driverProfile = await this.userProfileRepository.findOne({
      where: { id, roleId: driverRole.id },
    });

    if (!driverProfile) {
      throw new NotFoundException('Driver not found');
    }

    const previousStatus = driverProfile.status;

    // If trying to set the same status, return current profile
    if (previousStatus === newStatus) {
      return {
        id: driverProfile.id,
        status: previousStatus as string,
        previousStatus: previousStatus as string,
        message: `Driver is already in ${newStatus} status`,
        updatedAt: driverProfile.updatedAt,
      };
    }

    // Validation for activating driver
    if (newStatus === 'active') {
      const validationResult = await this.validateDriverForActivation(id);
      if (!validationResult.isValid) {
        throw new BadRequestException(validationResult.message);
      }
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        driverProfile.userId,
        driverProfile.roleId,
        OnboardingStep.PROFILE_PHOTO_UPLOAD,
      );
    }

    // Update driver status
    const updatedProfile =
      await this.userProfileRepository.updateUserProfileById(id, {
        status: newStatus as any,
      });
    // console.log(updatedProfile);
    return {
      id: updatedProfile.id,
      status: newStatus,
      previousStatus: previousStatus as string,
      message: `Driver status changed from ${previousStatus} to ${newStatus} successfully`,
      updatedAt: updatedProfile.updatedAt,
    };
  }

  /**
   * Validate driver for activation
   * @param driverId Driver profile ID
   * @returns Validation result
   */
  private async validateDriverForActivation(driverId: string): Promise<{
    isValid: boolean;
    message?: string;
  }> {
    // Get user profile to access country information
    const userProfile = await this.userProfileRepository.findOne({
      where: {
        id: driverId,
      },
      include: {
        driverKycs: {
          where: {
            status: 'APPROVED',
            kycDocument: {
              isMandatory: true,
            },
          },
        },
      },
    });
    // console.log(userProfile);
    if (!userProfile) {
      return {
        isValid: false,
        message: 'Driver profile not found',
      };
    }
    // Get total count of mandatory KYC documents for the country
    const totalMandatoryKycDocuments =
      await this.kycDocumentService.findMandatoryKycDocuments();
    console.log(
      `Total mandatory KYC documents: ${totalMandatoryKycDocuments.length}`,
    );

    // // Verify that approved KYC count matches total mandatory KYC documents count
    const approvedKycCount = userProfile.driverKycs
      ? userProfile.driverKycs.length
      : 0;
    console.log(
      `Approved KYC documents count for driver ${driverId}: ${approvedKycCount}`,
    );
    if (approvedKycCount < totalMandatoryKycDocuments.length) {
      return {
        isValid: false,
        message:
          'Cannot activate driver: Missing mandatory approved KYC documents',
      };
    }

    // Check if driver has at least one verified vehicle
    const verifiedVehicleCount = await this.userProfileRepository.count({
      id: driverId,
      driverVehicles: {
        some: {
          status: 'active',
          deletedAt: null,
        },
      },
    });

    if (verifiedVehicleCount === 0) {
      return {
        isValid: false,
        message: 'Cannot activate driver: No verified vehicle found',
      };
    }

    return { isValid: true };
  }

  /**
   * Register driver with phone number (Admin only)
   * @param phoneNumber Phone number in international format
   * @returns Created or existing user
   */
  async registerDriverWithPhone(phoneNumber: string): Promise<any> {
    // Check if phone number is already registered with driver profile
    const existingUser =
      await this.userRepository.findByPhoneNumber(phoneNumber);
    const role = await this.roleRepository.findByName('driver');

    if (existingUser && role) {
      const driverProfile = await this.getProfileByUserIdAndRoleId(
        existingUser.id,
        role.id,
      );
      if (driverProfile) {
        throw new BadRequestException(
          'A driver profile already exists for this mobile number.',
        );
      }
    }
    const user = await this.userRegistrationService.registerWithPhone(
      phoneNumber,
      'driver' as any,
    );
    await this.otpService.sendPhoneOtp(phoneNumber);
    return {
      userId: user.id,
      phoneNumber: user.phoneNumber,
      message: 'OTP sent to phone number',
    };
  }

  /**
   * Create a new driver (Admin only)
   * @param dto Create driver DTO with userId
   * @returns Created driver profile with user data
   */
  async createDriverForAdmin(dto: any): Promise<any> {
    // Get driver role
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }

    // Get user by userId
    const user = await this.userRepository.findById(dto.userId);
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if phone is verified
    if (!user.phoneVerifiedAt) {
      throw new BadRequestException(
        'Phone number must be verified before creating driver profile',
      );
    }

    // Check if user with email already exists (if email is provided and different)
    if (dto.email && dto.email !== user.email) {
      const existingEmailUser = await this.userRepository.findByEmail(
        dto.email,
      );
      if (existingEmailUser) {
        throw new BadRequestException('User with this email already exists');
      }
      // Update user email
      await this.userRepository.updateById(user.id, { email: dto.email });
    }

    // Check if driver profile already exists
    const existingProfile =
      await this.userProfileRepository.getOneByUserIdAndRoleId(
        user.id,
        driverRole.id,
      );

    let driverProfile;
    let languageId = dto.languageId ?? null;
    // if !dto.languageId then find language name english
    if (!languageId) {
      const language = await this.languageService.findLanguageByName('English');
      if (language) {
        languageId = language.id;
      }
    }

    const profileData = {
      firstName: dto.firstName,
      lastName: dto.lastName,
      gender: dto.gender,
      dob: new Date(dto.dob),
      ...(dto.profilePictureUrl && {
        profilePictureUrl: dto.profilePictureUrl,
      }),
      cityId: dto.cityId,
      languageId,
      referralCode: dto.referralCode || null, // Optional referral code
    };

    if (existingProfile) {
      // Update existing profile
      driverProfile = await this.userProfileRepository.updateUserProfileById(
        existingProfile.id,
        profileData,
      );
    } else {
      // Create new profile
      driverProfile = await this.userProfileRepository.createUserProfile(
        user.id,
        driverRole.id,
        profileData,
      );

      // Create user role if not exists
      const existingUserRole = await this.userRoleRepository.findOne({
        where: { userId: user.id, roleId: driverRole.id },
      });

      if (!existingUserRole) {
        await this.userRoleRepository.create({
          userId: user.id,
          roleId: driverRole.id,
        });
      }

      // Update onboarding status to PROFILE_SETUP for new profile
      await this.userOnboardingService.updateOrCreateOnboardingStepByUserAndRole(
        user.id,
        driverRole.id,
        OnboardingStep.PROFILE_SETUP,
      );
    }

    // Get updated driver profile with all related data
    const updatedDriverProfile = await this.userProfileRepository.findOne({
      where: { id: driverProfile.id },
      include: {
        user: {
          include: {
            userOnboardings: {
              where: { roleId: driverRole.id },
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
        city: {
          select: { id: true, name: true },
        },
        language: {
          select: { id: true, name: true },
        },
      },
    });

    return this.formatDriverResponse(
      updatedDriverProfile,
      (updatedDriverProfile as any).user,
      (updatedDriverProfile as any).city,
      (updatedDriverProfile as any).language,
    );
  }

  /**
   * Update driver profile (Admin only)
   * @param id Driver profile ID
   * @param dto Update driver DTO
   * @param _role Admin role (for future role-based logic)
   * @returns Updated driver profile
   */
  async updateDriverForAdmin(id: string, dto: any): Promise<any> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }

    // Find driver profile
    const driverProfile = await this.userProfileRepository.findById(id);
    if (!driverProfile || driverProfile.roleId !== driverRole.id) {
      throw new NotFoundException('Driver not found');
    }

    // Prepare update data
    const updateData: any = {};
    if (dto.firstName) updateData.firstName = dto.firstName;
    if (dto.lastName) updateData.lastName = dto.lastName;
    if (dto.gender) updateData.gender = dto.gender;
    if (dto.dob) updateData.dob = new Date(dto.dob);

    // Handle profilePictureUrl: set to provided value or null if not provided
    updateData.profilePictureUrl = dto.profilePictureUrl || null;

    if (dto.cityId) updateData.cityId = dto.cityId;
    if (dto.languageId) updateData.languageId = dto.languageId;

    // Update profile
    const updatedProfile =
      await this.userProfileRepository.updateUserProfileById(id, updateData);

    // Get updated driver profile with all related data
    const fullDriverProfile = await this.userProfileRepository.findOne({
      where: { id: updatedProfile.id },
      include: {
        user: {
          include: {
            userOnboardings: {
              where: { roleId: driverRole.id },
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
        city: {
          select: { id: true, name: true },
        },
        language: {
          select: { id: true, name: true },
        },
      },
    });

    return this.formatDriverResponse(
      fullDriverProfile,
      (fullDriverProfile as any).user,
      (fullDriverProfile as any).city,
      (fullDriverProfile as any).language,
    );
  }

  /**
   * Send email verification OTP by user profile ID
   * @param userProfileId User profile ID
   * @param email Email address to verify
   * @returns Success message
   */
  async sendEmailVerificationByProfileId(
    userProfileId: string,
    email: string,
  ): Promise<{ message: string }> {
    // Find user profile to get user ID
    const userProfile =
      await this.userProfileRepository.findById(userProfileId);
    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }

    // Send email OTP using the user ID
    await this.sendEmailOtp(userProfile.userId, email);

    return { message: 'Email verification OTP sent successfully' };
  }

  /**
   * Verify email OTP by user profile ID
   * @param userProfileId User profile ID
   * @param email Email address being verified
   * @param otp OTP code
   * @returns Verification result
   */
  async verifyEmailOtpByProfileId(
    userProfileId: string,
    email: string,
    otp: string,
  ): Promise<{ success: boolean; message: string }> {
    // Find user profile to get user ID
    const userProfile =
      await this.userProfileRepository.findById(userProfileId);
    if (!userProfile) {
      throw new NotFoundException('User profile not found');
    }

    // Verify email using the user ID with BadRequestException instead of UnauthorizedException
    const result = await this.verifyEmailUpdateAdmin(
      userProfile.userId,
      email,
      otp,
    );

    if (!result) {
      throw new BadRequestException('Email verification failed');
    }
    //update email verified at in user
    await this.userRepository.updateById(userProfile.userId, {
      emailVerifiedAt: new Date(),
    });

    return {
      success: true,
      message: 'Email verified successfully',
    };
  }

  /**
   * Get paginated list of drivers with filters (Admin only)
   * @param filters Filter and pagination options
   * @param _role Admin role (for future role-based logic)
   * @param apiConsumer User making the request for role-based filtering
   * @returns Paginated driver list
   */
  async listDriversForAdmin(
    filters: any,
    _role: string,
    apiConsumer?: ApiConsumer,
  ): Promise<any> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }

    const {
      page = 1,
      limit = 10,
      cityId,
      name,
      email,
      phoneNumber,
      status,
      vehicleTypeId,
    } = filters;
    // const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      roleId: driverRole.id,
      deletedAt: null,
    };

    if (cityId) {
      where.cityId = cityId;
    }

    if (status) {
      where.status = status;
    }

    if (name) {
      where.OR = [
        { firstName: { contains: name, mode: 'insensitive' } },
        { lastName: { contains: name, mode: 'insensitive' } },
      ];
    }

    if (email || phoneNumber) {
      where.user = {
        is: {
          ...(email && { email: { contains: email, mode: 'insensitive' } }),
          ...(phoneNumber && { phoneNumber: { contains: phoneNumber } }),
        },
      };
    }

    if (vehicleTypeId) {
      where.driverVehicles = {
        some: {
          vehicleTypeId: vehicleTypeId,
          deletedAt: null,
        },
      };
    }

    // Apply role-based filtering if apiConsumer is provided
    if (apiConsumer) {
      const isSuperOrSubAdmin = apiConsumer.roles.some(
        (role) => role === 'super_admin' || role === 'sub_admin',
      );

      if (!isSuperOrSubAdmin) {
        // Check if user is city_admin
        const isCityAdmin = apiConsumer.roles.includes('city_admin');
        if (isCityAdmin) {
          // Get cities where this user is assigned as city admin
          const cityAdmins = await this.cityAdminRepository.findMany({
            where: {
              userProfileId: apiConsumer.profileId,
              deletedAt: null,
              isEnabled: true,
            },
            select: {
              cityId: true,
            },
          });

          const allowedCityIds = cityAdmins.map((ca) => ca.cityId);

          if (allowedCityIds.length === 0) {
            // City admin with no assigned cities - return empty result
            return {
              data: [],
              meta: {
                page: Number(page),
                limit: Number(limit),
                total: 0,
                totalPages: 0,
                hasNextPage: false,
                hasPrevPage: false,
              },
            };
          }

          // Filter drivers by allowed cities
          if (cityId) {
            // If cityId filter is provided, check if it's in allowed cities
            if (!allowedCityIds.includes(cityId)) {
              // Requested city is not allowed for this city admin
              return {
                data: [],
                meta: {
                  page: Number(page),
                  limit: Number(limit),
                  total: 0,
                  totalPages: 0,
                  hasNextPage: false,
                  hasPrevPage: false,
                },
              };
            }
          } else {
            // No cityId filter provided, restrict to allowed cities
            where.cityId = {
              in: allowedCityIds,
            };
          }
        } else {
          // User doesn't have appropriate roles, return empty result
          return {
            data: [],
            meta: {
              page: Number(page),
              limit: Number(limit),
              total: 0,
              totalPages: 0,
              hasNextPage: false,
              hasPrevPage: false,
            },
          };
        }
      }
      // If user is super_admin or sub_admin, no additional filtering is needed
    }

    const include = {
      user: {
        include: {
          userOnboardings: {
            where: { roleId: driverRole.id },
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
        },
      },
      city: {
        select: { id: true, name: true },
      },
      language: {
        select: { id: true, name: true },
      },
    };

    const drivers = await this.userProfileRepository.paginate(page, limit, {
      where,
      include,
      orderBy: { createdAt: 'desc' },
    });
    const formattedDrivers = drivers.data.map((driver: any) =>
      this.formatDriverResponse(
        driver,
        driver.user,
        driver.city,
        driver.language,
      ),
    );
    drivers.data = formattedDrivers;
    return drivers;
  }
  /**
   * Get driver by ID (Admin only)
   * @param id Driver profile ID
   * @param _role Admin role (for future role-based logic)
   * @returns Driver profile data
   */
  async getDriverByIdForAdmin(id: string): Promise<any> {
    const driverRole = await this.roleRepository.findByName('driver');
    if (!driverRole) {
      throw new NotFoundException('Driver role not found');
    }

    const driver = await this.userProfileRepository.findOne({
      where: { id, roleId: driverRole.id },
      include: {
        user: {
          include: {
            userOnboardings: {
              where: { roleId: driverRole.id },
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
        city: {
          select: { id: true, name: true },
        },
        language: {
          select: { id: true, name: true },
        },
      },
    });

    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    return this.formatDriverResponse(
      driver,
      (driver as any).user,
      (driver as any).city,
      (driver as any).language,
    );
  }

  /**
   * Resend OTP for phone verification (Admin only)
   * @param phoneNumber Phone number
   * @param _role Admin role (for future role-based logic)
   * @returns Success message
   */
  async resendOtpForAdmin(phoneNumber: string): Promise<{ message: string }> {
    const user = await this.userRepository.findByPhoneNumber(phoneNumber);
    if (!user) {
      throw new NotFoundException('User with this phone number not found');
    }

    await this.otpService.sendPhoneOtp(phoneNumber);

    return { message: 'OTP sent successfully' };
  }

  /**
   * Verify OTP for phone number (Admin only)
   * @param phoneNumber Phone number
   * @param otp OTP code
   * @param _role Admin role (for future role-based logic)
   * @returns Success message
   */
  async verifyOtpForAdmin(
    phoneNumber: string,
    otp: string,
  ): Promise<{ message: string }> {
    const isValid = await this.otpService.verifyPhoneOtp(phoneNumber, otp);

    if (!isValid) {
      throw new BadRequestException('Invalid OTP');
    }

    return { message: 'Phone number verified successfully' };
  }

  async getDriverProfileById(profileId: string) {
    const profile = await this.userProfileRepository.findById(profileId, {
      include: {
        city: true,
        driverVehicles: true,
        metaData: true,
      },
    });

    if (!profile) throw new NotFoundException('Driver profile not found');

    if (profile.profilePictureUrl) {
      profile['profilePictureUrl'] = await this.fileUploadService.getSignedUrl(
        profile.profilePictureUrl,
        3600,
      );
    }

    return profile;
  }

  /**
   * Format driver response data
   * @param profile Driver profile
   * @param user User data
   * @param city City data (optional)
   * @param language Language data (optional)
   * @returns Formatted driver response
   */
  private formatDriverResponse(
    profile: any,
    user: any,
    city?: any,
    language?: any,
  ): any {
    // Get the latest onboarding data
    const onboarding = user?.userOnboardings?.[0] || null;

    return {
      id: profile.id,
      userId: profile.userId,
      roleId: profile.roleId,
      firstName: profile.firstName,
      lastName: profile.lastName,
      email: user?.email,
      phoneNumber: user?.phoneNumber,
      gender: profile.gender,
      dob: profile.dob,
      profilePictureUrl: profile.profilePictureUrl,
      cityId: profile.cityId,
      cityName: city?.name,
      languageId: profile.languageId,
      languageName: language?.name,
      referralCode: profile.referralCode,
      phoneVerified: !!user?.phoneVerifiedAt,
      emailVerified: !!user?.emailVerifiedAt,
      status: profile.status,
      isOnline: profile.isOnline,
      createdAt: profile.createdAt,
      updatedAt: profile.updatedAt,
      onboarding: onboarding
        ? {
            id: onboarding.id,
            currentStep: onboarding.currentStep,
            lastActiveAt: onboarding.lastActiveAt,
            createdAt: onboarding.createdAt,
            updatedAt: onboarding.updatedAt,
          }
        : null,
    };
  }

  async updateProfile(id: string, dto: any): Promise<UserProfile> {
    return this.userProfileRepository.updateUserProfileById(id, dto);
  }
}
