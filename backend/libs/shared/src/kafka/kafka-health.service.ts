import {
  Injectable,
  Logger,
  OnModuleInit,
  OnModuleDestroy,
} from '@nestjs/common';
import { KafkaProducerService } from './kafka-producer.service';

@Injectable()
export class KafkaHealthService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(KafkaHealthService.name);
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private readonly HEALTH_CHECK_INTERVAL = 600000; // 1 minute

  constructor(private readonly kafkaProducerService: KafkaProducerService) {}

  /**
   * Initialize the health check service when module starts
   */
  async onModuleInit(): Promise<void> {
    // Start health checks after a short delay to allow <PERSON>f<PERSON> to initialize
    setTimeout(() => {
      this.startHealthCheck();
    }, 5000); // 5 second delay
  }

  /**
   * Clean up when module is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    this.stopHealthCheck();
  }

  /**
   * Start periodic health checks for Kafka
   */
  startHealthCheck(): void {
    if (this.healthCheckInterval) {
      this.logger.warn('Health check is already running');
      return;
    }

    this.logger.log('Starting Kafka health check service');
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, this.HEALTH_CHECK_INTERVAL);

    // Perform initial health check
    this.performHealthCheck();
  }

  /**
   * Stop periodic health checks
   */
  stopHealthCheck(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      this.logger.log('Kafka health check service stopped');
    }
  }

  /**
   * Perform a health check and attempt reconnection if needed
   */
  private async performHealthCheck(): Promise<void> {
    const isConnected = this.kafkaProducerService.isKafkaConnected();

    if (!isConnected) {
      this.logger.warn('Kafka is not connected. Attempting to reconnect...');
      const reconnected = await this.kafkaProducerService.reconnect();

      if (reconnected) {
        this.logger.log('Kafka reconnection successful');
      } else {
        this.logger.error(
          'Kafka reconnection failed. Will retry in next health check cycle',
        );
      }
    } else {
      // this.logger.debug('Kafka health check: Connected');
    }
  }

  /**
   * Get current Kafka connection status
   */
  getConnectionStatus(): {
    connected: boolean;
    lastChecked: string;
  } {
    return {
      connected: this.kafkaProducerService.isKafkaConnected(),
      lastChecked: new Date().toISOString(),
    };
  }
}
