import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'status_update';

type Subjects = 'CityAdmin' | 'all';

export type CityAdminAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class CityAdminAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // cityAdmin:list permission
    if (userPermissions.includes('cityAdmin:list')) {
      can('list', 'CityAdmin');
    }

    // cityAdmin:status_update permission
    if (userPermissions.includes('cityAdmin:status_update')) {
      can('status_update', 'CityAdmin');
    }
    if (userPermissions.includes('cityAdmin:edit')) {
      can('edit', 'CityAdmin');
    }
    if (userPermissions.includes('cityAdmin:create')) {
      can('create', 'CityAdmin');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
