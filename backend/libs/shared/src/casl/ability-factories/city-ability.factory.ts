import {
  AbilityBuilder,
  ExtractSubjectType,
  InferSubjects,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { CityService } from '@shared/shared/modules/city/city.service';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';
import { CityAdminRepository } from '@shared/shared/repositories/city-admin.repository';

type Action =
  | 'create'
  | 'list'
  | 'edit'
  | 'delete'
  | 'manage'
  | 'status_update';

type Subjects = InferSubjects<typeof CityService> | 'City' | 'all';

export type CityAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class CityAbilityFactory {
  constructor(private cityAdminRepository: CityAdminRepository) {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions and adminCity relations

    // city:create permission
    if (userPermissions.includes('city:create')) {
      can('create', 'City');
    }

    // city:list permission
    if (userPermissions.includes('city:list')) {
      can('list', 'City');
    }

    // city:edit permission - only for cities where user has adminCity relation
    if (userPermissions.includes('city:edit')) {
      const adminCityIds = await this.getAdminCityIds(apiConsumer.profileId);

      can('edit', 'City', { id: { $in: adminCityIds } });
    }

    // city:manage permission - only for cities where user has adminCity relation
    if (userPermissions.includes('city:manage')) {
      const adminCityIds = await this.getAdminCityIds(apiConsumer.profileId);

      can('manage', 'City', { id: { $in: adminCityIds } });
    }

    // city:status_update permission - only for cities where user has adminCity relation
    if (userPermissions.includes('city:status_update')) {
      const adminCityIds = await this.getAdminCityIds(apiConsumer.profileId);

      can('status_update', 'City', { id: { $in: adminCityIds } });
    }

    return build({
      detectSubjectType: (item) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }

  /**
   * Get city IDs where the user has adminCity relation
   */
  private async getAdminCityIds(profileId: string): Promise<string[]> {
    try {
      console.log('Fetching admin city relations for user:', profileId);
      // This would typically query a user-city relation table
      // For now, we'll return an empty array as the relation logic
      // would need to be implemented based on your specific schema
      // TODO: Implement actual adminCity relation query
      const adminCityIds =
        await this.cityAdminRepository.getCityAdminsByProfileId(profileId);
      return adminCityIds.length > 0
        ? adminCityIds.map((relation) => relation.cityId)
        : [];
    } catch (error) {
      console.error('Error fetching admin city relations:', error);
      return [];
    }
  }
}
