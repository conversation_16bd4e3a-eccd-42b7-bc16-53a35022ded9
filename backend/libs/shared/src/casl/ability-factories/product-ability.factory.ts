import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete' | 'status_update';

type Subjects = 'Product' | 'all';

export type ProductAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class ProductAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // product:create permission
    if (userPermissions.includes('product:create')) {
      can('create', 'Product');
    }

    // product:list permission
    if (userPermissions.includes('product:list')) {
      can('list', 'Product');
    }

    // product:edit permission
    if (userPermissions.includes('product:edit')) {
      can('edit', 'Product');
    }

    // product:status_update permission (for enable/disable operations)
    if (userPermissions.includes('product:status_update')) {
      can('status_update', 'Product');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
