import {
  AbilityBuilder,
  ExtractSubjectType,
  MongoAbility,
  createMongoAbility,
} from '@casl/ability';
import { Injectable } from '@nestjs/common';
import { ApiConsumer } from '@shared/shared/modules/auth/interfaces';

type Action = 'create' | 'list' | 'edit' | 'delete' | 'status_update';

type Subjects = 'ZoneType' | 'all';

export type ZoneTypeAbility = MongoAbility<[Action, Subjects]>;

@Injectable()
export class ZoneTypeAbilityFactory {
  constructor() {}

  async createForAPIConsumer(apiConsumer: ApiConsumer) {
    const { can, build } = new AbilityBuilder(createMongoAbility);

    // Get user permissions from token
    const userPermissions = apiConsumer.permissions || [];

    // Define permissions based on token permissions

    // zone_type:create permission
    if (userPermissions.includes('zone_type:create')) {
      can('create', 'ZoneType');
    }

    // zone_type:list permission
    if (userPermissions.includes('zone_type:list')) {
      can('list', 'ZoneType');
    }

    // zone_type:edit permission
    if (userPermissions.includes('zone_type:edit')) {
      can('edit', 'ZoneType');
    }

    // zone_type:delete permission
    if (userPermissions.includes('zone_type:delete')) {
      can('delete', 'ZoneType');
    }

    // zone_type:status_update permission
    if (userPermissions.includes('zone_type:status_update')) {
      can('status_update', 'ZoneType');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }
}
