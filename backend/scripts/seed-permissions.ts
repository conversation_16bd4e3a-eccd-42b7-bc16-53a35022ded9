#!/usr/bin/env ts-node

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface PermissionData {
  name: string;
  description: string;
  resource: string;
  action: string;
}

// Define all permissions for the system
const permissions: PermissionData[] = [
  // Driver Management
  { name: 'driver:create', description: 'Create driver', resource: 'driver', action: 'create' },
  { name: 'driver:edit', description: 'Edit driver details', resource: 'driver', action: 'edit' },
  { name: 'driver:list', description: 'List all drivers', resource: 'driver', action: 'list' },
  { name: 'driver:status_update', description: 'Update driver status', resource: 'driver', action: 'status_update' },

  // City Management
  { name: 'city:create', description: 'Create city', resource: 'city', action: 'create' },
  { name: 'city:edit', description: 'Edit city details', resource: 'city', action: 'edit' },
  { name: 'city:list', description: 'List all cities', resource: 'city', action: 'list' },
  { name: 'city:manage', description: 'Manage city boundary, products, and zones', resource: 'city', action: 'manage' },
  { name: 'city:status_update', description: 'Update city status', resource: 'city', action: 'status_update' },

  // Admin Language Management
  { name: 'language:create', description: 'Create language', resource: 'language', action: 'create' },
  { name: 'language:edit', description: 'Edit language', resource: 'language', action: 'edit' },
  { name: 'language:list', description: 'List languages', resource: 'language', action: 'list' },
  { name: 'language:delete', description: 'Delete language', resource: 'language', action: 'delete' },

  // Product Management
  { name: 'product:create', description: 'Create product', resource: 'product', action: 'create' },
  { name: 'product:edit', description: 'Edit product', resource: 'product', action: 'edit' },
  { name: 'product:list', description: 'List products', resource: 'product', action: 'list' },
  { name: 'product:status_update', description: 'Update product status', resource: 'product', action: 'status_update' },

  // Product Service Management
  { name: 'product_service:edit', description: 'Edit product service', resource: 'product_service', action: 'edit' },
  { name: 'product_service:list', description: 'List product services', resource: 'product_service', action: 'list' },

  // Vehicle Category Management
  { name: 'vehicle_category:create', description: 'Create vehicle category', resource: 'vehicle_category', action: 'create' },
  { name: 'vehicle_category:list', description: 'List vehicle categories', resource: 'vehicle_category', action: 'list' },
  { name: 'vehicle_category:edit', description: 'Edit vehicle category', resource: 'vehicle_category', action: 'edit' },

  // Zone Type Management
  { name: 'zone_type:create', description: 'Create zone type', resource: 'zone_type', action: 'create' },
  { name: 'zone_type:edit', description: 'Edit zone type', resource: 'zone_type', action: 'edit' },
  { name: 'zone_type:list', description: 'List zone types', resource: 'zone_type', action: 'list' },
  { name: 'zone_type:delete', description: 'Delete zone type', resource: 'zone_type', action: 'delete' },
  { name: 'zone_type:status_update', description: 'Update zone type status', resource: 'zone_type', action: 'status_update' },

  // Role Management
  { name: 'roles:create', description: 'Create roles', resource: 'roles', action: 'create' },
  { name: 'roles:edit', description: 'Edit roles', resource: 'roles', action: 'edit' },
  { name: 'roles:list', description: 'List roles', resource: 'roles', action: 'list' },
  { name: 'roles:manage_permissions', description: 'Manage role permissions', resource: 'roles', action: 'manage_permissions' },

  // Sub Admin Management
  { name: 'sub_admin:create', description: 'Create sub admin', resource: 'sub_admin', action: 'create' },
  { name: 'sub_admin:edit', description: 'Edit sub admin', resource: 'sub_admin', action: 'edit' },
  { name: 'sub_admin:list', description: 'List sub admins', resource: 'sub_admin', action: 'list' },
  { name: 'sub_admin:status_update', description: 'Update sub admin status', resource: 'sub_admin', action: 'status_update' },

  // City Admin Management
  { name: 'city_admin:list', description: 'List city admins', resource: 'city_admin', action: 'list' },
  { name: 'city_admin:status_update', description: 'Update city admin status', resource: 'city_admin', action: 'status_update' },
];

async function seedPermissions(force: boolean = false) {
  console.log('🔐 Permission Seeding Script Started\n');

  try {
    if (force) {
      console.log('🧹 Force mode enabled - clearing existing data...');
      
      // Clear role permissions first (foreign key constraint)
      const deletedRolePermissions = await prisma.rolePermission.deleteMany();
      console.log(`   Deleted ${deletedRolePermissions.count} role-permission associations`);
      
      // Clear permissions
      const deletedPermissions = await prisma.permission.deleteMany();
      console.log(`   Deleted ${deletedPermissions.count} permissions`);
      
      console.log('✅ Existing data cleared\n');
    } else {
      // Check if permissions already exist
      const existingPermissionsCount = await prisma.permission.count();
      if (existingPermissionsCount > 0) {
        console.log(`⏭️  Permissions already exist (${existingPermissionsCount} found).`);
        console.log('   Use --force flag to clear and recreate permissions.');
        return;
      }
    }

    console.log(`🌱 Creating ${permissions.length} permissions...`);

    // Create or update permissions
    let createdCount = 0;
    let updatedCount = 0;

    for (const permissionData of permissions) {
      // Check if permission exists by name
      const existingPermission = await prisma.permission.findFirst({
        where: { name: permissionData.name },
      });

      if (existingPermission) {
        // Update existing permission
        await prisma.permission.update({
          where: { id: existingPermission.id },
          data: {
            description: permissionData.description,
            resource: permissionData.resource,
            action: permissionData.action,
          },
        });
        updatedCount++;
      } else {
        // Create new permission
        await prisma.permission.create({
          data: permissionData,
        });
        createdCount++;
      }
    }

    console.log(`✅ Permissions seeding completed!`);
    console.log(`   Created: ${createdCount} permissions`);
    console.log(`   Updated: ${updatedCount} permissions`);

    // Print summary by resource
    const permissionsByResource = permissions.reduce((acc, permission) => {
      if (!acc[permission.resource]) {
        acc[permission.resource] = 0;
      }
      acc[permission.resource]++;
      return acc;
    }, {} as Record<string, number>);

    console.log('\n📊 Permissions by Resource:');
    Object.entries(permissionsByResource).forEach(([resource, count]) => {
      console.log(`   ${resource}: ${count} permissions`);
    });

    // Final count
    const finalPermissionsCount = await prisma.permission.count();
    console.log(`\n🎯 Total permissions in database: ${finalPermissionsCount}`);

  } catch (error) {
    console.error('❌ Error during permission seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

async function main() {
  const args = process.argv.slice(2);
  const force = args.includes('--force');
  
  await seedPermissions(force);
}

if (require.main === module) {
  main();
}

export { seedPermissions };
