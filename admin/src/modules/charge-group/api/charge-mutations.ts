import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateChargeRequest, ChargeResponse, UpdateChargeRequest, AttachChargeRequest } from '../types/charge';

/**
 * Hook for creating a new charge
 */
export const useCreateCharge = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeGroupId: string;
      charge: CreateChargeRequest;
    }): Promise<ChargeResponse> => {
      return apiClient.post(`/charge-groups/${data.chargeGroupId}/charges`, data.charge);
    },
  });
};

/**
 * Hook for updating a charge
 */
export const useUpdateCharge = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeId: string;
      charge: UpdateChargeRequest;
    }): Promise<ChargeResponse> => {
      return apiClient.put(`/charges/${data.chargeId}`, data.charge);
    },
  });
};

/**
 * Hook for detaching a charge from a charge group
 */
export const useDetachCharge = () => {
  return useMutation({
    mutationFn: async (data: { chargeGroupId: string; chargeId: string }): Promise<void> => {
      return apiClient.delete(`/charge-group-charges/detach/${data.chargeGroupId}/${data.chargeId}`);
    },
  });
};

/**
 * Hook for attaching an existing charge to a charge group
 */
export const useAttachCharge = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeGroupId: string;
      request: AttachChargeRequest;
    }): Promise<ChargeResponse> => {
      return apiClient.post(`/charge-group-charges/attach/${data.chargeGroupId}`, data.request);
    },
  });
};

/**
 * Hook for updating charge priority
 */
export const useUpdateChargePriority = () => {
  return useMutation({
    mutationFn: async (data: {
      chargeGroupChargeId: string;
      priority: number;
    }): Promise<ChargeResponse> => {
      return apiClient.put(`/charge-group-charges/${data.chargeGroupChargeId}/priority`, {
        priority: data.priority,
      });
    },
  });
};