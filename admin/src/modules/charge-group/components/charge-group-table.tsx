'use client';

import { CustomPagination } from '@/components/pagination';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';
import { useDeleteChargeGroup } from '../api/mutations';
import { ListChargeGroupResponse, ChargeGroup } from '../types/charge-group';
import { ChargeGroupModal } from './charge-group-modal';
import { ChargeGroupTableEmpty } from './charge-group-table-empty';
import { ChargeGroupTableLoading } from './charge-group-table-loading';
import { ChargeGroupDeleteModal } from './charge-group-delete-modal';
import {
  USE_ROLE_BASED_ACCESS,
  useRoleBasedAccess,
} from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

const getColumns = ({
  deleteChargeGroupMutation,
  handleManageClick,
  handleEditClick,
  handleDeleteClick,
  chargeGroupToDelete,
  withPermission,
}: {
  handleManageClick: (id: string) => void;
  handleEditClick: (id: string) => void;
  handleDeleteClick: (chargeGroup: ChargeGroup) => void;
  deleteChargeGroupMutation: any;
  chargeGroupToDelete: ChargeGroup | null;
  withPermission: USE_ROLE_BASED_ACCESS['withPermission'];
}): ColumnDef<ChargeGroup>[] => [
  {
    accessorKey: 'name',
    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
    cell: ({ row }) => {
      const chargeGroup = row.original as ChargeGroup;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm font-medium break-words'>{chargeGroup.name}</div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'description',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>Description</div>
    ),
    cell: ({ row }) => {
      const chargeGroup = row.original as ChargeGroup;
      return (
        <div className='text-left max-w-[300px]'>
          <div className='text-sm text-gray-600 break-words'>
            {chargeGroup.description || 'No description'}
          </div>
        </div>
      );
    },
    size: 300,
  },
  {
    accessorKey: 'identifier',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>Identifier</div>
    ),
    cell: ({ row }) => {
      const chargeGroup = row.original as ChargeGroup;
      return (
        <div className='text-left max-w-[200px]'>
          <div className='text-sm text-gray-600 break-words'>
            {chargeGroup.identifier || '-'}
          </div>
        </div>
      );
    },
    size: 200,
  },
  {
    accessorKey: 'createdAt',
    header: () => (
      <div className='text-left font-semibold text-gray-600 text-sm'>Created Date</div>
    ),
    cell: ({ row }) => {
      const chargeGroup = row.original as ChargeGroup;
      const date = new Date(chargeGroup.createdAt);
      return (
        <div className='text-left'>
          <div className='text-sm text-gray-600'>
            {date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
          </div>
        </div>
      );
    },
    size: 120,
  },
  {
    id: 'actions',
    header: () => <div className='text-center font-semibold text-gray-600 text-sm'>Actions</div>,
    cell: ({ row }) => {
      const chargeGroup = row.original as ChargeGroup;
      const isDeleting =
        chargeGroupToDelete?.id === chargeGroup.id && deleteChargeGroupMutation.isPending;

      return (
        <div className='flex justify-center gap-1'>
          <button
            className='text-sm font-medium text-blue-600 hover:text-blue-700 border border-blue-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.CHARGE_GROUPS.MANAGE, () =>
                handleManageClick(chargeGroup.id)
              );
            }}
            disabled={isDeleting}
          >
            Manage
          </button>
          <button
            className='text-sm font-medium text-gray-600 hover:text-blue-600 border border-gray-300 hover:border-blue-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.CHARGE_GROUPS.EDIT, () =>
                handleEditClick(chargeGroup.id)
              );
            }}
            disabled={isDeleting}
          >
            Edit
          </button>
          <button
            className='text-sm font-medium text-red-600 hover:text-red-700 border border-red-300 hover:border-red-400 px-3 py-1 rounded-md transition-colors bg-white cursor-pointer'
            onClick={() => {
              withPermission(RBAC_PERMISSIONS.CHARGE_GROUPS.DELETE, () =>
                handleDeleteClick(chargeGroup)
              );
            }}
            disabled={isDeleting}
          >
            {isDeleting ? '...' : 'Delete'}
          </button>
        </div>
      );
    },
    size: 300,
  },
];

interface ChargeGroupTableProps {
  data: ListChargeGroupResponse | undefined;
  isLoading: boolean;
  currentPage: number;
  onPageChange: (page: number) => void;
  hasFilters?: boolean;
  hasSearch?: boolean;
  onClearFilters?: () => void;
  onManageChargeGroup?: (chargeGroup: ChargeGroup) => void;
}

export function ChargeGroupTable({
  data,
  isLoading,
  currentPage,
  onPageChange,
  hasFilters: _hasFilters,
  hasSearch: _hasSearch,
  onClearFilters: _onClearFilters,
  onManageChargeGroup,
}: ChargeGroupTableProps) {
  const [chargeGroupToEdit, setChargeGroupToEdit] = useState<string | null>(null);
  const [chargeGroupToDelete, setChargeGroupToDelete] = useState<ChargeGroup | null>(null);
  const deleteChargeGroupMutation = useDeleteChargeGroup();
  const queryClient = useQueryClient();
  const { withPermission } = useRoleBasedAccess();

  const handleManageClick = (id: string) => {
    const chargeGroup = data?.data.find((cg) => cg.id === id);
    if (chargeGroup && onManageChargeGroup) {
      onManageChargeGroup(chargeGroup);
    }
  };

  const handleEditClick = (id: string) => {
    setChargeGroupToEdit(id);
  };

  const handleDeleteClick = (chargeGroup: ChargeGroup) => {
    setChargeGroupToDelete(chargeGroup);
  };

  const handleDeleteConfirm = () => {
    if (!chargeGroupToDelete) return;

    deleteChargeGroupMutation.mutate(chargeGroupToDelete.id, {
      onSuccess: () => {
        toast.success('Charge group deleted successfully');
        queryClient.invalidateQueries({ queryKey: ['charge-groups'] });
      },
      onSettled: () => {
        setChargeGroupToDelete(null);
      },
    });
  };

  const columns = getColumns({
    deleteChargeGroupMutation,
    handleManageClick,
    handleEditClick,
    handleDeleteClick,
    chargeGroupToDelete,
    withPermission,
  });

  const table = useReactTable({
    data: data?.data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  if (isLoading) {
    return <ChargeGroupTableLoading />;
  }

  if (!data?.data?.length) {
    return <ChargeGroupTableEmpty />;
  }

  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full table-fixed'>
            <thead>
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id} className='border-b bg-gray-50'>
                  {headerGroup.headers.map(header => (
                    <th
                      key={header.id}
                      className='h-11 px-4 text-left align-middle'
                      style={{ width: header.getSize(), maxWidth: header.getSize() }}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody>
              {table.getRowModel().rows.map(row => (
                <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                  {row.getVisibleCells().map(cell => (
                    <td
                      key={cell.id}
                      className='px-4 py-3 align-middle'
                      style={{
                        width: cell.column.getSize(),
                        maxWidth: cell.column.getSize(),
                      }}
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {data && data.meta && data.meta.totalPages > 1 && (
        <CustomPagination
          currentPage={currentPage}
          totalPages={data.meta.totalPages}
          onPageChange={onPageChange}
          hasNext={data.meta.hasNextPage}
          hasPrev={data.meta.hasPrevPage}
        />
      )}

      {/* Delete Confirmation Modal */}
      <ChargeGroupDeleteModal
        isOpen={!!chargeGroupToDelete}
        onClose={() => setChargeGroupToDelete(null)}
        onConfirm={handleDeleteConfirm}
        isLoading={deleteChargeGroupMutation.isPending}
        chargeGroupName={chargeGroupToDelete?.name || ''}
      />

      {/* Edit Modal */}
      <ChargeGroupModal
        mode='edit'
        chargeGroupId={chargeGroupToEdit}
        isOpen={!!chargeGroupToEdit}
        onClose={() => setChargeGroupToEdit(null)}
      />
    </div>
  );
}