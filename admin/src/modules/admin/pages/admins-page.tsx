'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle } from 'lucide-react';
import { useListRole } from '@/modules/role/api/queries';
import { useGetAdminsForRole } from '../api/queries';
import { AdminTable } from '../components/admin-table';
import { AdminFilters } from '../components/admin-filters';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';

export function AdminsPage() {
   const [activeTab, setActiveTab] = useState<string>('');
   const [pages, setPages] = useState<Record<string, number>>({});
   const [search, setSearch] = useState('');
   const [status, setStatus] = useState<'active' | 'inactive' | 'invited' | undefined>(undefined);
   const { hasPermission } = useRoleBasedAccess();
   const hasCityAdminPermission = hasPermission(RBAC_PERMISSIONS.CITY_ADMIN.LIST);
   const hasSubAdminPermission = hasPermission(RBAC_PERMISSIONS.SUB_ADMIN.LIST);

   const rolesQuery = useListRole({ limit: 100 });
   const roles =
      rolesQuery.data?.data.filter(role => {
         // Case 1: if neither CITY_ADMIN nor SUB_ADMIN permission
         if (!hasCityAdminPermission && !hasSubAdminPermission) {
            return (
               role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN &&
               role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN
            );
         }

         // Case 2: has only CITY_ADMIN permission, so block sub_admins
         if (hasCityAdminPermission && !hasSubAdminPermission) {
            return role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN;
         }

         // Case 3: has only SUB_ADMIN permission, so block city_admins
         if (!hasCityAdminPermission && hasSubAdminPermission) {
            return role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN;
         }

         // Case 4:  has both CITY_ADMIN and SUB_ADMIN permission
         return true;
      }) || [];

   // Set the first role as active tab when roles are loaded
   if (roles.length > 0 && !activeTab) {
      setActiveTab(roles[0].identifier);
   }

   const currentPage = pages[activeTab] || 1;

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPages(prev => ({ ...prev, [activeTab]: 1 }));
   };

   const handleStatusChange = (value: 'active' | 'inactive' | 'invited' | undefined) => {
      setStatus(value);
      setPages(prev => ({ ...prev, [activeTab]: 1 }));
   };

   const adminsQuery = useGetAdminsForRole(activeTab, {
      page: currentPage,
      limit: 10,
      search: search || undefined,
      status: status || undefined,
   });

   const handlePageChange = (roleIdentifier: string, newPage: number) => {
      setPages(prev => ({ ...prev, [roleIdentifier]: newPage }));
   };

   if (rolesQuery.isLoading || adminsQuery.isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='animate-pulse'>
               <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
               <div className='h-12 bg-gray-200 rounded mb-4'></div>
               <div className='h-96 bg-gray-200 rounded'></div>
            </div>
         </div>
      );
   }

   if (rolesQuery.error || !roles.length) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-6'>
            <div className='flex justify-between items-center'>
               <h2 className='text-2xl font-semibold text-gray-900'>Admins</h2>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>No Roles Found</h3>
               <p className='text-gray-600'>
                  No roles are available to display admins. Please create roles first.
               </p>
            </Card>
         </div>
      );
   }

   return (
      <div className='flex flex-1 flex-col gap-4 p-6'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Admins</h2>
         </div>

         <Card>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
               <div className='border-b border-gray-200 px-6 pt-4 pb-0'>
                  <TabsList
                     className='grid w-full bg-gray-50'
                     style={{
                        gridTemplateColumns:
                           roles.length > 4 ? `repeat(${roles.length}, 1fr)` : `repeat(${4}, 1fr)`,
                     }}
                  >
                     {roles.map(role => (
                        <TabsTrigger
                           key={role.identifier}
                           value={role.identifier}
                           className='text-sm'
                        >
                           {role.name.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </TabsTrigger>
                     ))}
                  </TabsList>
               </div>

               <div className='p-0'>
                  {roles.map(role => (
                     <TabsContent key={role.identifier} value={role.identifier} className='mt-0'>
                        <AdminFilters
                           search={search}
                           status={status}
                           onSearchChange={handleSearchChange}
                           onStatusChange={handleStatusChange}
                           isLoading={adminsQuery.isFetching && !adminsQuery.isLoading}
                           activeTab={activeTab}
                        />
                        <div className='p-6'>
                           <AdminTable
                              data={activeTab === role.identifier ? adminsQuery.data : undefined}
                              isLoading={
                                 activeTab === role.identifier ? adminsQuery.isLoading : false
                              }
                              currentPage={pages[role.identifier] || 1}
                              onPageChange={newPage => handlePageChange(role.identifier, newPage)}
                           />
                        </div>
                     </TabsContent>
                  ))}
               </div>
            </Tabs>
         </Card>
      </div>
   );
}
