export interface Admin {
   id: string;
   userId: string;
   roleId: string;
   firstName: string;
   lastName: string;
   cityId: null;
   referralCode: null;
   profilePictureUrl: null;
   languageId: null;
   gender: null;
   status: string;
   dob: null;
   isOnline: boolean;
   rideOtp: null;
   createdAt: string;
   updatedAt: string;
   deletedAt: null;
   role: Role;
   user: User;
}

interface User {
   id: string;
   email: string;
   phoneNumber: null;
}

interface Role {
   id: string;
   name: string;
   identifier: string;
}

export interface AdminListParams {
   page?: number;
   limit?: number;
   search?: string;
   status?: 'active' | 'pending' | 'disabled' | 'inactive' | 'invited';
}

export interface AdminListMeta {
   page: number;
   limit: number;
   total: number;
   totalPages: number;
   hasNextPage: boolean;
   hasPrevPage: boolean;
}

export interface AdminListResponse {
   success: boolean;
   message: string;
   data: Admin[];
   meta: AdminListMeta;
   timestamp: number;
}

export interface AdminResponse {
   success: boolean;
   message: string;
   data: Admin;
   timestamp: number;
}

export interface InviteAdminRequest {
   firstName: string;
   lastName: string;
   email: string;
   roleId: string;
}

export interface InviteCityAdminRequest extends InviteAdminRequest {
   cityIds: string[];
}

export interface InviteAdminResponse {
   success: boolean;
   message: string;
   data: any;
   timestamp: number;
}

export interface ResendInviteResponse {
   success: boolean;
   message: string;
   data: any;
   timestamp: number;
}

export interface ChangeStatusRequest {
   status: 'active' | 'inactive';
}

export interface ChangeStatusResponse {
   success: boolean;
   message: string;
   data: Admin;
   timestamp: number;
}

export interface City {
   id: string;
   name: string;
   code?: string;
}

export interface CityAdmin {
   cityId: string;
   isEnabled: boolean;
   city: {
      name: string;
   };
}

export interface AdminProfileWithCities {
   id: string;
   userId: string;
   roleId: string;
   firstName: string;
   lastName: string;
   status: string;
   createdAt: string;
   updatedAt: string;
   user: User;
   role: Role;
   cityAdmins?: CityAdmin[];
}

export interface AdminProfileResponse {
   success: boolean;
   message: string;
   data: AdminProfileWithCities;
   timestamp: number;
}

export interface RemoveCityAdminsRequest {
   cityAdminIds: string[];
}

export interface RemoveCityAdminRequest {
   userProfileId: string;
}

export interface AddAdminToCityRequest {
   adminIds: string[];
}

export interface RemoveCityAdminsResponse {
   success: boolean;
   message: string;
   data: any;
   timestamp: number;
}

export interface RemoveCityAdminResponse {
   success: boolean;
   message: string;
   data: any;
   timestamp: number;
}

export interface AddAdminToCityResponse {
   success: boolean;
   message: string;
   data: any;
   timestamp: number;
}
