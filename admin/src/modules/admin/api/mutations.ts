import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   InviteAdminRequest,
   InviteCityAdminRequest,
   InviteAdminResponse,
   ResendInviteResponse,
   ChangeStatusResponse,
   RemoveCityAdminsRequest,
   RemoveCityAdminsResponse,
   RemoveCityAdminRequest,
   RemoveCityAdminResponse,
   AddAdminToCityRequest,
   AddAdminToCityResponse,
} from '../types/admin';

/**
 * Hook for inviting a normal sub-admin user
 */
export const useInviteAdmin = () => {
   return useMutation({
      mutationFn: async (data: InviteAdminRequest): Promise<InviteAdminResponse> => {
         return apiClient.post('/admin/sub-admin/invite', data);
      },
   });
};

/**
 * Hook for inviting a city admin user
 */
export const useInviteCityAdmin = () => {
   return useMutation({
      mutationFn: async (data: InviteCityAdminRequest): Promise<InviteAdminResponse> => {
         return apiClient.post(`/admin/sub-admin/cities/invite`, data);
      },
   });
};

/**
 * Hook for resending invitation to admin
 */
export const useResendInvite = () => {
   return useMutation({
      mutationFn: async (profileId: string): Promise<ResendInviteResponse> => {
         return apiClient.post(`/admin/sub-admin/profile/${profileId}/resend-invite`);
      },
   });
};

/**
 * Hook for changing admin status (activate/deactivate)
 */
export const useChangeAdminStatus = () => {
   return useMutation({
      mutationFn: async ({
         profileId,
         status,
      }: {
         profileId: string;
         status: 'active' | 'inactive';
      }): Promise<ChangeStatusResponse> => {
         return apiClient.post(`/admin/sub-admin/profile/${profileId}/change-status`, { status });
      },
   });
};

/**
 * Hook for removing multiple city admins
 */
export const useRemoveCityAdmins = () => {
   return useMutation({
      mutationFn: async (data: RemoveCityAdminsRequest): Promise<RemoveCityAdminsResponse> => {
         return apiClient.post('/admin/sub-admin/cities/remove', data);
      },
   });
};

/**
 * Hook for removing admin from city
 */
export const useRemoveCityAdmin = (cityId: string) => {
   return useMutation({
      mutationFn: async (data: RemoveCityAdminRequest): Promise<RemoveCityAdminResponse> => {
         return apiClient.post(`/admin/sub-admin/cities/${cityId}/remove-admin`, data);
      },
   });
};

/**
 * Hook for adding admin to city
 */
export const useAddAdminToCity = (cityId: string) => {
   return useMutation({
      mutationFn: async (data: AddAdminToCityRequest): Promise<AddAdminToCityResponse> => {
         return apiClient.post(`/admin/sub-admin/cities/${cityId}/add-admin`, data);
      },
   });
};
