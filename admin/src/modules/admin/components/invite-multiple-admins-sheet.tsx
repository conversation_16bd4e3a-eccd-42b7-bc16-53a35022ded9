'use client';

import { ErrorMessage } from '@/components/error-message';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
   Sheet,
   SheetContent,
   SheetDescription,
   SheetFooter,
   SheetHeader,
   SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { UserPlus, X, Plus } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useQueryClient } from '@tanstack/react-query';
import { useListRole } from '@/modules/role/api/queries';
import { useAllCities } from '@/modules/city/api/queries';
import { useInviteAdmin, useInviteCityAdmin } from '../api/mutations';
import { toast } from '@/lib/toast';
import { Role } from '@/modules/role/types/role';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';

const createEmailSchema = (existingEmails: string[], roles: Role[]) =>
   z
      .object({
         firstName: z
            .string()
            .min(1, 'First name is required')
            .max(20, 'First name cannot exceed 20 characters')
            .regex(/^[a-zA-Z\s]+$/, 'First name can only contain letters and spaces'),
         lastName: z
            .string()
            .min(1, 'Last name is required')
            .max(20, 'First name cannot exceed 20 characters')
            .regex(/^[a-zA-Z\s]+$/, 'Last name can only contain letters and spaces'),
         email: z
            .string()
            .min(1, 'Email is required')
            .regex(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, 'Please enter a valid email address')
            .refine(email => !existingEmails.includes(email), 'This email has already been added'),
         roleId: z.string().min(1, 'Please select a role'),
         cityIds: z.array(z.string()).optional(),
      })
      .refine(
         data => {
            const selectedRole = roles.find(role => role.id === data.roleId);
            if (selectedRole?.identifier === 'city_admin') {
               return data.cityIds && data.cityIds.length > 0;
            }
            return true;
         },
         {
            message: 'At least one city must be selected for city admin role',
            path: ['cityIds'],
         }
      );

type EmailFormValues = z.infer<ReturnType<typeof createEmailSchema>>;

interface InviteMember {
   firstName: string;
   lastName: string;
   email: string;
   roleId: string;
   roleName: string;
   cityIds?: string[];
   cityNames?: string[];
}

interface InviteAdminsSheetProps {
   selectedCity?: string;
   label?: string;
}

export const InviteMultipleAdminsSheet = ({ selectedCity, label }: InviteAdminsSheetProps = {}) => {
   const [open, setOpen] = useState(false);
   const [members, setMembers] = useState<InviteMember[]>([]);
   const [addError, setAddError] = useState<string>('');
   const [inviteError, setInviteError] = useState<string>('');
   const [isInviting, setIsInviting] = useState(false);
   const [selectedCityOptions, setSelectedCityOptions] = useState<Option[]>([]);

   const queryClient = useQueryClient();
   const rolesQuery = useListRole({ limit: 100 });
   const citiesQuery = useAllCities();
   const { withPermission, hasPermission } = useRoleBasedAccess();
   const hasCityAdminPermission = hasPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE);
   const hasSubAdminPermission = hasPermission(RBAC_PERMISSIONS.SUB_ADMIN.CREATE);

   const inviteAdminMutation = useInviteAdmin();
   const inviteCityAdminMutation = useInviteCityAdmin();

   const roles = useMemo(() => {
      const allRoles = rolesQuery.data?.data || [];
      // If selectedCity is provided, filter to only show city_admin role
      if (selectedCity) {
         return allRoles.filter(role => role.identifier === ROLE_IDENTIFIERS.CITY_ADMIN);
      } else {
         // Case 1: if neither CITY_ADMIN nor SUB_ADMIN permission
         if (!hasCityAdminPermission && !hasSubAdminPermission) {
            return allRoles.filter(
               role =>
                  role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN &&
                  role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN
            );
         }

         // Case 2: has only CITY_ADMIN permission, so block sub_admins
         if (hasCityAdminPermission && !hasSubAdminPermission) {
            return allRoles.filter(role => role.identifier !== ROLE_IDENTIFIERS.SUB_ADMIN);
         }

         // Case 3: has only SUB_ADMIN permission, so block city_admins
         if (!hasCityAdminPermission && hasSubAdminPermission) {
            return allRoles.filter(role => role.identifier !== ROLE_IDENTIFIERS.CITY_ADMIN);
         }

         // Case 4:  has both CITY_ADMIN and SUB_ADMIN permission
         return allRoles;
      }
   }, [hasCityAdminPermission, hasSubAdminPermission, rolesQuery.data?.data, selectedCity]);

   // Get existing emails from members
   const existingEmails = useMemo(() => members.map(member => member.email), [members]);
   const emailSchema = useMemo(
      () => createEmailSchema(existingEmails, roles),
      [existingEmails, roles]
   );

   const form = useForm<EmailFormValues>({
      resolver: zodResolver(emailSchema),
      defaultValues: {
         firstName: '',
         lastName: '',
         email: '',
         roleId: '',
         cityIds: [],
      },
   });

   const {
      formState: { errors },
      reset: resetForm,
      control,
      watch,
      setValue,
   } = form;

   const watchedFirstName = watch('firstName');
   const watchedLastName = watch('lastName');
   const watchedEmail = watch('email');
   const watchedRoleId = watch('roleId');
   const watchedCityIds = watch('cityIds');

   const cities = useMemo(() => citiesQuery.data?.data || [], [citiesQuery.data?.data]);

   // Convert cities to MultipleSelector Option format
   const cityOptions: Option[] = useMemo(() => {
      return cities.map(city => ({
         value: city.id,
         label: city.name,
      }));
   }, [cities]);

   // Set default role when roles are loaded
   useEffect(() => {
      if (roles.length > 0 && !watchedRoleId) {
         setValue('roleId', roles[0].id);
      }
   }, [roles, watchedRoleId, setValue]);

   // Pre-populate cityIds when selectedCity is provided
   useEffect(() => {
      if (selectedCity && (!watchedCityIds || watchedCityIds.length === 0)) {
         setValue('cityIds', [selectedCity]);
         const selectedCityOption = cityOptions.find(option => option.value === selectedCity);
         if (selectedCityOption) {
            setSelectedCityOptions([selectedCityOption]);
         }
      }
   }, [selectedCity, watchedCityIds, setValue, cityOptions]);

   // Sync cityIds form value with selectedCityOptions
   useEffect(() => {
      if (watchedCityIds) {
         const options = cityOptions.filter(option => watchedCityIds.includes(option.value));
         setSelectedCityOptions(options);
      }
   }, [watchedCityIds, cityOptions]);

   // Handle city selection change
   const handleCitySelectionChange = (options: Option[]) => {
      setSelectedCityOptions(options);
      setValue(
         'cityIds',
         options.map(option => option.value)
      );
   };

   // Clear add error when user starts typing
   useEffect(() => {
      if (
         addError &&
         (watchedFirstName ||
            watchedLastName ||
            watchedEmail ||
            watchedRoleId ||
            (watchedCityIds && watchedCityIds.length > 0))
      ) {
         setAddError('');
      }
   }, [watchedFirstName, watchedLastName, watchedEmail, watchedRoleId, watchedCityIds, addError]);

   // reset all errors
   useEffect(() => {
      setAddError('');
      setInviteError('');
      form.clearErrors();
   }, [form, open]);

   const handleAddMemberValidation = (data: EmailFormValues) => {
      setAddError('');
      setInviteError(''); // Clear invite error when adding members

      const selectedRole = roles.find(role => role.id === data.roleId);
      if (!selectedRole) {
         setAddError('Please select a valid role');
         return;
      }

      // Check if city is required for city_admins (always required when selectedCity is present or role is city_admin)
      if (
         (selectedRole.identifier === 'city_admin' || selectedCity) &&
         (!data.cityIds || data.cityIds.length === 0)
      ) {
         setAddError('At least one city is required for city admins');
         return;
      }

      const selectedCitiesData = cities.filter(city => data.cityIds?.includes(city.id));

      const newMember: InviteMember = {
         firstName: data.firstName,
         lastName: data.lastName,
         email: data.email,
         roleId: data.roleId,
         roleName: selectedRole.name,
         cityIds: data.cityIds,
         cityNames: selectedCitiesData.map(city => city.name),
      };

      setMembers(prev => [...prev, newMember]);
      resetForm();
      setAddError('');
      setSelectedCityOptions([]);
      // Reset to first role again
      if (roles.length > 0) {
         setValue('roleId', roles[0].id);
      }
      setValue('cityIds', []);
   };

   const handleAddMemberError = () => {
      setAddError('Please fix the validation errors above');
   };

   const handleRemoveMember = (index: number) => {
      setMembers(prev => prev.filter((_, i) => i !== index));
   };

   const handleSubmit = async () => {
      setInviteError('');
      setIsInviting(true);

      if (members.length === 0) {
         setInviteError('Please add some emails you want to invite');
         setIsInviting(false);
         return;
      }

      try {
         // Group members by type
         const cityAdmins = members.filter(member => {
            const role = roles.find(r => r.id === member.roleId);
            return role?.identifier === 'city_admin';
         });
         const normalAdmins = members.filter(member => {
            const role = roles.find(r => r.id === member.roleId);
            return role?.identifier !== 'city_admin';
         });

         // Array to track all promises
         const invitePromises: Promise<any>[] = [];

         // Invite normal admins
         for (const member of normalAdmins) {
            const promise = inviteAdminMutation.mutateAsync({
               firstName: member.firstName,
               lastName: member.lastName,
               email: member.email,
               roleId: member.roleId,
            });
            invitePromises.push(promise);
         }

         // Invite city admins
         for (const member of cityAdmins) {
            if (!member.cityIds || member.cityIds.length === 0) {
               throw new Error(
                  `At least one city is required for ${member.firstName} ${member.lastName}`
               );
            }

            const promise = inviteCityAdminMutation.mutateAsync({
               firstName: member.firstName,
               lastName: member.lastName,
               email: member.email,
               roleId: member.roleId,
               cityIds: member.cityIds,
            });
            invitePromises.push(promise);
         }

         // Wait for all invitations to complete
         await Promise.all(invitePromises);

         // Success - invalidate queries to refresh admin lists
         // Invalidate all admin role queries to refresh the admins page
         queryClient.invalidateQueries({ queryKey: ['admins', 'role'] });

         // Also invalidate specific role queries for invited members
         const roleIds = [...new Set(members.map(member => member.roleId))];
         roleIds.forEach(roleId => {
            queryClient.invalidateQueries({ queryKey: ['admins', 'role', roleId] });
         });

         // Invalidate city admins queries for all affected cities
         if (selectedCity) {
            queryClient.invalidateQueries({ queryKey: ['city-admins', selectedCity] });
            queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         }

         // Also invalidate for all cities that were assigned to city admins
         const allAssignedCityIds = cityAdmins.flatMap(member => member.cityIds || []);
         const uniqueCityIds = [...new Set(allAssignedCityIds)];
         uniqueCityIds.forEach(cityId => {
            queryClient.invalidateQueries({ queryKey: ['city-admins', cityId] });
         });

         if (uniqueCityIds.length > 0) {
            queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         }

         // Show success message
         toast.success(
            `Successfully sent ${members.length} invitation${members.length !== 1 ? 's' : ''}`
         );

         // Reset state and close modal
         setMembers([]);
         setInviteError('');
         setOpen(false);
      } catch (error: any) {
         console.error('Error sending invitations:', error);
         setInviteError(
            error?.response?.data?.message ||
               error?.message ||
               'Failed to send invitations. Please try again.'
         );
      } finally {
         setIsInviting(false);
      }
   };

   const handleClose = () => {
      setOpen(false);
      setMembers([]);
      setAddError('');
      setInviteError('');
      setSelectedCityOptions([]);
      resetForm();
      // Reset to first role again
      if (roles.length > 0) {
         setValue('roleId', roles[0].id);
      }
      setValue('cityIds', []);
   };

   return (
      <>
         <Button
            className='cursor-pointer'
            variant='outline'
            onClick={() => {
               if (selectedCity) {
                  withPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE, () => setOpen(true));
               } else {
                  if (hasCityAdminPermission || hasSubAdminPermission) {
                     setOpen(true);
                  } else {
                     toast.error("You don't have permission for this action");
                  }
               }
            }}
         >
            <UserPlus />
            {label ? label : 'Invite Admins'}
         </Button>
         <Sheet open={open} onOpenChange={setOpen}>
            <SheetContent preventOutsideClickClose={true} className='w-[441px] sm:max-w-[441px]'>
               <SheetHeader className='pb-0'>
                  <SheetTitle>Invite Admins</SheetTitle>
                  <SheetDescription>
                     Fill in your staff's details and send them an invite link to join as
                     administrators
                  </SheetDescription>
               </SheetHeader>

               <div className='flex flex-col h-full'>
                  <ScrollArea
                     scrollThumbClassName='bg-gray-400'
                     type='always'
                     className='flex-1 px-6'
                     scrollViewPortClassName='h-[100vh - 160px]'
                  >
                     <div className='space-y-6 pb-4'>
                        <div className='grid grid-cols-2 gap-4'>
                           <div>
                              <Label className='text-sm font-medium'>First Name</Label>
                              <Controller
                                 control={control}
                                 name='firstName'
                                 render={({ field }) => (
                                    <Input placeholder='John' {...field} className='mt-1' />
                                 )}
                              />
                              {errors.firstName && <ErrorMessage error={errors.firstName} />}
                           </div>
                           <div>
                              <Label className='text-sm font-medium'>Last Name</Label>
                              <Controller
                                 control={control}
                                 name='lastName'
                                 render={({ field }) => (
                                    <Input placeholder='Doe' {...field} className='mt-1' />
                                 )}
                              />
                              {errors.lastName && <ErrorMessage error={errors.lastName} />}
                           </div>
                        </div>

                        <div className='space-y-4'>
                           <div>
                              <Label className='text-sm font-medium'>Email address</Label>
                              <Controller
                                 control={control}
                                 name='email'
                                 render={({ field }) => (
                                    <Input
                                       placeholder='<EMAIL>'
                                       {...field}
                                       className='mt-1'
                                    />
                                 )}
                              />
                              {errors.email && <ErrorMessage error={errors.email} />}
                           </div>

                           <div>
                              <Label className='text-sm font-medium'>Type of Admin</Label>
                              <Controller
                                 control={control}
                                 name='roleId'
                                 render={({ field }) => (
                                    <Select
                                       value={field.value}
                                       onValueChange={field.onChange}
                                       disabled={!!selectedCity}
                                    >
                                       <SelectTrigger className='mt-1 w-full'>
                                          <SelectValue placeholder='Select Admin Type' />
                                       </SelectTrigger>
                                       <SelectContent>
                                          {roles.map(role => (
                                             <SelectItem key={role.id} value={role.id}>
                                                {role.name.replaceAll('_', ' ')}
                                             </SelectItem>
                                          ))}
                                       </SelectContent>
                                    </Select>
                                 )}
                              />
                              {errors.roleId && <ErrorMessage error={errors.roleId} />}
                           </div>

                           {(watchedRoleId &&
                              roles.find(role => role.id === watchedRoleId)?.identifier ===
                                 'city_admin') ||
                           selectedCity ? (
                              <div>
                                 <Label className='text-sm font-medium'>Cities</Label>
                                 <MultipleSelector
                                    value={selectedCityOptions}
                                    onChange={handleCitySelectionChange}
                                    defaultOptions={cityOptions}
                                    placeholder='Select cities...'
                                    disabled={!!selectedCity}
                                    className='mt-1'
                                    emptyIndicator={
                                       <p className='text-center text-sm text-gray-500'>
                                          No cities found
                                       </p>
                                    }
                                    commandProps={{
                                       label: 'Select cities',
                                    }}
                                 />
                                 {errors.cityIds && <ErrorMessage error={errors.cityIds} />}
                              </div>
                           ) : null}
                        </div>

                        {/* Add Button */}
                        <div>
                           <Button
                              type='button'
                              onClick={form.handleSubmit(
                                 handleAddMemberValidation,
                                 handleAddMemberError
                              )}
                              variant='outline'
                              className='w-full'
                           >
                              <Plus className='h-4 w-4 mr-2' />
                              Add For Invite
                           </Button>
                        </div>

                        {addError && (
                           <div className='text-sm text-red-600 p-3 bg-red-50 rounded-md border border-red-200'>
                              {addError}
                           </div>
                        )}

                        {/* Members List Section */}
                        <div className='mt-6'>
                           {members.length > 0 && (
                              <div>
                                 <div className='mb-3'>
                                    <Label className='text-sm font-medium text-gray-600'>
                                       Invited Members ({members.length})
                                    </Label>
                                 </div>
                                 <div className='space-y-2'>
                                    {members.map((member, index) => (
                                       <div
                                          key={index}
                                          className='flex items-center justify-between p-3 bg-gray-50 rounded-md border border-gray-200 hover:bg-gray-100 transition-colors'
                                       >
                                          <div className='flex items-center gap-3'>
                                             <div className='w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center'>
                                                <span className='text-xs font-semibold text-orange-600'>
                                                   {member.firstName.charAt(0).toUpperCase()}
                                                   {member.lastName.charAt(0).toUpperCase()}
                                                </span>
                                             </div>
                                             <div>
                                                <div className='flex items-center gap-2 mb-1'>
                                                   <p className='text-sm font-medium text-gray-900'>
                                                      {member.firstName} {member.lastName}
                                                   </p>
                                                   <span className='inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800'>
                                                      {member.roleName}
                                                   </span>
                                                </div>
                                                <p className='text-xs text-gray-600 mb-2'>
                                                   {member.email}
                                                </p>
                                                {member.cityNames &&
                                                   member.cityNames.length > 0 && (
                                                      <div className='flex flex-wrap gap-1'>
                                                         {member.cityNames.map(
                                                            (cityName, cityIndex) => (
                                                               <span
                                                                  key={cityIndex}
                                                                  className='inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800'
                                                               >
                                                                  {cityName}
                                                               </span>
                                                            )
                                                         )}
                                                      </div>
                                                   )}
                                             </div>
                                          </div>
                                          <Button
                                             variant='ghost'
                                             size='icon'
                                             onClick={() => handleRemoveMember(index)}
                                             className='h-6 w-6 text-gray-400 hover:text-red-500'
                                          >
                                             <X className='h-3 w-3' />
                                          </Button>
                                       </div>
                                    ))}
                                 </div>
                              </div>
                           )}

                           {members.length === 0 && (
                              <div className='flex items-center justify-center py-12'>
                                 <div className='text-center'>
                                    <div className='w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4'>
                                       <UserPlus className='h-8 w-8 text-gray-400' />
                                    </div>
                                    <p className='text-gray-500 text-sm'>No admins added yet</p>
                                    <p className='text-gray-400 text-xs mt-1'>
                                       Fill out the form above and click "Add For Invite" to get
                                       started
                                    </p>
                                 </div>
                              </div>
                           )}
                        </div>
                     </div>

                     {inviteError && (
                        <div className='px-0 pb-2'>
                           <div className='text-sm text-red-600 p-3 bg-red-50 rounded-md border border-red-200'>
                              {inviteError}
                           </div>
                        </div>
                     )}
                  </ScrollArea>
               </div>

               <SheetFooter className='border-t pt-4 px-6'>
                  <div className='flex gap-3 w-full'>
                     <Button
                        type='button'
                        variant='outline'
                        onClick={handleClose}
                        disabled={isInviting}
                        className='flex-1'
                     >
                        Cancel
                     </Button>
                     <Button
                        type='button'
                        onClick={handleSubmit}
                        disabled={isInviting}
                        className='flex-1'
                     >
                        {isInviting
                           ? 'Sending...'
                           : `Send ${members.length > 0 ? `${members.length} ` : ''}Invite${
                                members.length !== 1 ? 's' : ''
                             }`}
                     </Button>
                  </div>
               </SheetFooter>
            </SheetContent>
         </Sheet>
      </>
   );
};
