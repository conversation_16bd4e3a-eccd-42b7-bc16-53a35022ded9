'use client';

import {
   <PERSON><PERSON>,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Admin } from '../types/admin';
import { useChangeAdminStatus } from '../api/mutations';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

interface ActivateDeactivateModalProps {
   open: boolean;
   onOpenChange: (open: boolean) => void;
   admin: Admin;
}

export function ActivateDeactivateModal({ open, onOpenChange, admin }: ActivateDeactivateModalProps) {
   const queryClient = useQueryClient();
   const changeStatusMutation = useChangeAdminStatus();

   const isDeactivating = admin.status === 'active';
   const actionText = isDeactivating ? 'Deactivate' : 'Activate';
   const newStatus = isDeactivating ? 'inactive' : 'active';

   const handleSubmit = async () => {
      try {
         await changeStatusMutation.mutateAsync({
            profileId: admin.id,
            status: newStatus,
         });

         toast.success(`Admin ${actionText.toLowerCase()}d successfully`);

         // Invalidate and refetch admin list
         queryClient.invalidateQueries({ queryKey: ['admins'] });

         onOpenChange(false);
      } catch (error: any) {
         toast.error(error?.response?.data?.message || `Failed to ${actionText.toLowerCase()} admin`);
      }
   };

   const handleCancel = () => {
      onOpenChange(false);
   };

   return (
      <Dialog open={open} onOpenChange={onOpenChange}>
         <DialogContent className="sm:max-w-md">
            <DialogHeader>
               <DialogTitle>
                  {actionText} Admin
               </DialogTitle>
               <DialogDescription>
                  Are you sure you want to {actionText.toLowerCase()} {admin.firstName} {admin.lastName}?
                  {isDeactivating && ' This will prevent them from accessing the admin dashboard.'}
               </DialogDescription>
            </DialogHeader>

            <DialogFooter className="gap-2">
               <Button
                  variant="outline"
                  onClick={handleCancel}
                  disabled={changeStatusMutation.isPending}
               >
                  Cancel
               </Button>
               <Button
                  variant={isDeactivating ? 'destructive' : 'default'}
                  className={!isDeactivating ? 'bg-green-600 hover:bg-green-700 text-white' : ''}
                  onClick={handleSubmit}
                  disabled={changeStatusMutation.isPending}
               >
                  {changeStatusMutation.isPending ? 'Processing...' : actionText}
               </Button>
            </DialogFooter>
         </DialogContent>
      </Dialog>
   );
}