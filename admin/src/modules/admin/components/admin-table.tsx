'use client';

import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Admin, AdminListResponse } from '../types/admin';
import { AdminTableEmpty } from './admin-table-empty';
import { AdminTableLoading } from './admin-table-loading';
import { CustomPagination } from '@/components/pagination';
import { AdminActionsCell } from './admin-actions-cell';

const getStatusColor = (status: string) => {
   switch (status) {
      case 'active':
         return 'bg-green-100 text-green-700';
      case 'pending':
         return 'bg-yellow-100 text-yellow-700';
      case 'invited':
         return 'bg-blue-100 text-blue-700';
      case 'disabled':
      case 'inactive':
         return 'bg-red-100 text-red-700';
      default:
         return 'bg-gray-100 text-gray-700';
   }
};

const getColumns = (): ColumnDef<Admin>[] => [
   {
      accessorKey: 'name',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Name</div>,
      cell: ({ row }) => {
         const admin = row.original as Admin;
         const fullName = [admin.firstName, admin.lastName].filter(Boolean).join(' ') || 'N/A';
         return (
            <div className='text-left'>
               <div className='text-sm font-semibold'>{fullName}</div>
               {/* <div className='text-xs text-gray-500'>{admin.user.email}</div> */}
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'email',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Email</div>,
      cell: ({ row }) => {
         const admin = row.original as Admin;
         return (
            <div className='text-left'>
               <div className='text-sm text-gray-600'>{admin.user.email || 'N/A'}</div>
            </div>
         );
      },
      size: 120,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const admin = row.original as Admin;
         return (
            <div className='text-left'>
               <Badge
                  variant='secondary'
                  className={`text-xs capitalize ${getStatusColor(admin.status)}`}
               >
                  {admin.status}
               </Badge>
            </div>
         );
      },
      size: 100,
   },
   {
      id: 'actions',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const admin = row.original as Admin;
         return (
            <div className='text-left'>
               <AdminActionsCell admin={admin} />
            </div>
         );
      },
      size: 120,
   },
   // {
   //    accessorKey: 'role',
   //    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Role</div>,
   //    cell: ({ row }) => {
   //       const admin = row.original as Admin;
   //       return (
   //          <div className='text-left'>
   //             <div className='text-sm text-gray-600'>{admin.role?.name || 'N/A'}</div>
   //          </div>
   //       );
   //    },
   //    size: 120,
   // },
   // {
   //    accessorKey: 'city',
   //    header: () => <div className='text-left font-semibold text-gray-600 text-sm'>City</div>,
   //    cell: ({ row }) => {
   //       const admin = row.original as Admin;
   //       return (
   //          <div className='text-left'>
   //             <div className='text-sm text-gray-600'>{admin.cityId || 'All Cities'}</div>
   //          </div>
   //       );
   //    },
   //    size: 120,
   // },
];

interface AdminTableProps {
   data: AdminListResponse | undefined;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
}

export function AdminTable({ data, isLoading, currentPage, onPageChange }: AdminTableProps) {
   const columns = getColumns();

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <AdminTableLoading />;
   }

   if (!data?.data?.length) {
      return <AdminTableEmpty />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPrevPage}
            />
         )}
      </div>
   );
}
