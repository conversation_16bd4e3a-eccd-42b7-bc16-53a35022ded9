'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Admin } from '../types/admin';
import { ActivateDeactivateModal } from './activate-deactivate-modal';
import { ResendInviteDialog } from './resend-invite-dialog';
import { ConfigureCitiesModal } from './configure-cities-modal';
import { ROLE_IDENTIFIERS } from '../enums/role-identifiers';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

interface AdminActionsCellProps {
   admin: Admin;
}

export function AdminActionsCell({ admin }: AdminActionsCellProps) {
   const [showStatusModal, setShowStatusModal] = useState(false);
   const [showResendDialog, setShowResendDialog] = useState(false);
   const [showConfigureCitiesModal, setShowConfigureCitiesModal] = useState(false);
   const { withPermission } = useRoleBasedAccess();

   const isCityAdmin = admin.role?.name === ROLE_IDENTIFIERS.CITY_ADMIN;

   if (admin.status === 'invited') {
      return (
         <>
            <div className='flex gap-2'>
               <Button
                  size='sm'
                  variant='outline'
                  onClick={() => {
                     const permission = isCityAdmin
                        ? RBAC_PERMISSIONS.CITY_ADMIN.CREATE
                        : RBAC_PERMISSIONS.SUB_ADMIN.CREATE;

                     withPermission(permission, () => setShowResendDialog(true));
                  }}
               >
                  Reinvite
               </Button>
               {isCityAdmin && (
                  <Button
                     size='sm'
                     variant='outline'
                     onClick={() => {
                        withPermission(RBAC_PERMISSIONS.CITY_ADMIN.EDIT, () =>
                           setShowConfigureCitiesModal(true)
                        );
                     }}
                  >
                     Configure cities
                  </Button>
               )}
            </div>
            <ResendInviteDialog
               open={showResendDialog}
               onOpenChange={setShowResendDialog}
               admin={admin}
            />
            {isCityAdmin && (
               <ConfigureCitiesModal
                  admin={admin}
                  open={showConfigureCitiesModal}
                  onOpenChange={setShowConfigureCitiesModal}
               />
            )}
         </>
      );
   }

   if (admin.status === 'active' || admin.status === 'inactive') {
      return (
         <>
            <div className='flex gap-2'>
               <Button
                  size='sm'
                  variant='outline'
                  className={
                     admin.status === 'active'
                        ? 'border-red-300 text-red-600 hover:bg-red-50 hover:text-red-600'
                        : 'border-green-300 text-green-600 hover:bg-green-50 hover:text-green-600'
                  }
                  onClick={() => {
                     const permission = isCityAdmin
                        ? RBAC_PERMISSIONS.CITY_ADMIN.STATUS_UPDATE
                        : RBAC_PERMISSIONS.SUB_ADMIN.STATUS_UPDATE;

                     withPermission(permission, () => setShowStatusModal(true));
                  }}
               >
                  {admin.status === 'active' ? 'Deactivate' : 'Activate'}
               </Button>
               {isCityAdmin && (
                  <Button
                     size='sm'
                     variant='outline'
                     onClick={() => {
                        withPermission(RBAC_PERMISSIONS.CITY_ADMIN.EDIT, () =>
                           setShowConfigureCitiesModal(true)
                        );
                     }}
                  >
                     Configure cities
                  </Button>
               )}
            </div>
            <ActivateDeactivateModal
               open={showStatusModal}
               onOpenChange={setShowStatusModal}
               admin={admin}
            />
            {isCityAdmin && (
               <ConfigureCitiesModal
                  admin={admin}
                  open={showConfigureCitiesModal}
                  onOpenChange={setShowConfigureCitiesModal}
               />
            )}
         </>
      );
   }

   return <span className='text-gray-400 text-sm'>No actions</span>;
}
