export function AdminTableLoading() {
  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-20'></div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-16'></div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-14'></div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-12'></div>
                </th>
                <th className='h-11 px-4 text-left align-middle'>
                  <div className='h-4 bg-gray-200 rounded animate-pulse w-12'></div>
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className='border-b'>
                  <td className='px-4 py-3 align-middle'>
                    <div className='space-y-1'>
                      <div className='h-4 bg-gray-200 rounded animate-pulse w-32'></div>
                      <div className='h-3 bg-gray-200 rounded animate-pulse w-40'></div>
                    </div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='h-4 bg-gray-200 rounded animate-pulse w-24'></div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='h-6 bg-gray-200 rounded-full animate-pulse w-16'></div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='h-4 bg-gray-200 rounded animate-pulse w-20'></div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='h-4 bg-gray-200 rounded animate-pulse w-16'></div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}