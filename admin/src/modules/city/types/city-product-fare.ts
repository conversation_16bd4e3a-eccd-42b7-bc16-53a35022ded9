// City Product Fare TypeScript interfaces

export enum CityProductFareStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export interface CityProductFare {
  id: string;
  cityProductId: string;
  priority: number;
  status: CityProductFareStatus;
  fromZoneId?: string | null;
  toZoneId?: string | null;
  // fromZoneTypeId?: string | null;
  // toZoneTypeId?: string | null;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  // Related data
  cityProduct?: {
    id: string;
    productName: string;
    cityName: string;
  };
  fromZone?: {
    id: string;
    name: string;
    description?: string | null;
  } | null;
  toZone?: {
    id: string;
    name: string;
    description?: string | null;
  } | null;
  // fromZoneType?: {
  //   id: string;
  //   name: string;
  //   description?: string | null;
  // } | null;
  // toZoneType?: {
  //   id: string;
  //   name: string;
  //   description?: string | null;
  // } | null;
  fareChargeGroups?: FareChargeGroupRelation[];
}

export interface FareChargeGroupRelation {
  id: string;
  cityProductFareId: string;
  chargeGroupId: string;
  priority: number;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
  chargeGroup: {
    id: string;
    name: string;
    description?: string | null;
    identifier?: string | null;
  };
}

export interface FareChargeGroup {
  id: string;
  name: string;
  identifier?: string | null;
  priority: number;
}

// Request types
export interface CreateCityProductFareRequest {
  cityProductId: string;
  priority: number;
  status?: CityProductFareStatus;
  // fromZoneId?: string;
  // toZoneId?: string;
  fromZoneTypeId?: string;
  toZoneTypeId?: string;
}

export interface UpdateCityProductFareRequest {
  priority?: number;
  status?: CityProductFareStatus;
  // fromZoneId?: string;
  // toZoneId?: string;
  fromZoneTypeId?: string;
  toZoneTypeId?: string;
}

export interface AttachChargeGroupsRequest {
  chargeGroups: {
    chargeGroupId: string;
    priority?: number;
  }[];
}

export interface UpdateChargeGroupPrioritiesRequest {
  chargeGroups: {
    chargeGroupId: string;
    priority: number;
  }[];
}

// Response types
export interface CityProductFareResponse {
  success: boolean;
  message: string;
  data: CityProductFare;
  timestamp: number;
}

export interface ListCityProductFaresResponse {
  success: boolean;
  message: string;
  data: CityProductFare[];
  timestamp: number;
}

export interface AttachChargeGroupsResponse {
  success: boolean;
  message: string;
  data: FareChargeGroup[];
  timestamp: number;
}

export interface DetachChargeGroupResponse {
  success: boolean;
  message: string;
  data: {
    message: string;
  };
  timestamp: number;
}

export interface UpdatePrioritiesResponse {
  success: boolean;
  message: string;
  data: FareChargeGroup[];
  timestamp: number;
}

export interface GetFareChargeGroupsResponse {
  success: boolean;
  message: string;
  data: FareChargeGroup[];
  timestamp: number;
}

export interface GetFareChargeGroupsRelationsResponse {
  success: boolean;
  message: string;
  data: FareChargeGroupRelation[];
  timestamp: number;
}
