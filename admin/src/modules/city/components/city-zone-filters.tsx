'use client';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Search, X, RefreshCw } from 'lucide-react';
import { useListZoneType } from '../../zone-type/api/queries';

interface CityZoneFiltersProps {
   search: string;
   zoneTypeId?: string;
   onSearchChange: (value: string) => void;
   onZoneTypeChange: (value: string | undefined) => void;
   isLoading?: boolean;
}

export function CityZoneFilters({
   search,
   zoneTypeId,
   onSearchChange,
   onZoneTypeChange,
   isLoading = false,
}: CityZoneFiltersProps) {
   const zoneTypesQuery = useListZoneType({
      page: 1,
      limit: 100,
      isActive: true,
   });

   const hasActiveFilters = search.length > 0 || !!zoneTypeId;

   const clearFilters = () => {
      onSearchChange('');
      onZoneTypeChange(undefined);
   };

   return (
      <div className='flex flex-col gap-4 mb-6'>
         {/* Search and Filters Row */}
         <div className='flex flex-col sm:flex-row gap-4'>
            {/* Search Input */}
            <div className='relative flex-1 max-w-sm'>
               <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
               <Input
                  placeholder='Search zones by name or description...'
                  value={search}
                  onChange={(e) => onSearchChange(e.target.value)}
                  className='pl-10'
               />
            </div>

            {/* Zone Type Filter */}
            <div className='w-full sm:w-48'>
               <Select value={zoneTypeId || 'all'} onValueChange={(value) => onZoneTypeChange(value === 'all' ? undefined : value)}>
                  <SelectTrigger className='w-full'>
                     <SelectValue placeholder='All Zone Types' />
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Zone Types</SelectItem>
                     {zoneTypesQuery.data?.data.data.map((zoneType) => (
                        <SelectItem key={zoneType.id} value={zoneType.id}>
                           <div className='flex items-center gap-2'>
                              <span>{zoneType.name}</span>
                              <span className='text-xs text-gray-500'>({zoneType.algorithm})</span>
                           </div>
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>
            </div>

            {/* Loading Indicator & Clear Filters */}
            <div className='flex items-center gap-2'>
               {isLoading && (
                  <RefreshCw className='h-4 w-4 text-gray-400 animate-spin' />
               )}
               {hasActiveFilters && (
                  <Button
                     variant='outline'
                     size='sm'
                     onClick={clearFilters}
                     className='gap-2'
                  >
                     <X className='h-4 w-4' />
                     Clear
                  </Button>
               )}
            </div>
         </div>

         {/* Active Filters Display */}
         {hasActiveFilters && (
            <div className='flex items-center gap-2 text-sm text-gray-600'>
               <span>Filters:</span>
               <div className='flex items-center gap-2'>
                  {search && (
                     <div className='flex items-center gap-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-md'>
                        <Search className='h-3 w-3' />
                        <span>&quot;{search}&quot;</span>
                        <button
                           onClick={() => onSearchChange('')}
                           className='ml-1 hover:text-blue-900'
                        >
                           <X className='h-3 w-3' />
                        </button>
                     </div>
                  )}
                  {zoneTypeId && (
                     <div className='flex items-center gap-1 px-2 py-1 bg-green-50 text-green-700 rounded-md'>
                        <span>
                           {zoneTypesQuery.data?.data.data.find(zt => zt.id === zoneTypeId)?.name || 'Zone Type'}
                        </span>
                        <button
                           onClick={() => onZoneTypeChange(undefined)}
                           className='ml-1 hover:text-green-900'
                        >
                           <X className='h-3 w-3' />
                        </button>
                     </div>
                  )}
               </div>
            </div>
         )}
      </div>
   );
}