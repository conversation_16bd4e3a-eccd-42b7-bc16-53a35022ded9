'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { Power, PowerOff } from 'lucide-react';
import { useEnableCityProduct, useDisableCityProduct } from '../api/city-product-mutations';
import { CityProduct } from '../types/city-product';

interface CityProductToggleModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cityProduct: CityProduct | null;
}

export function CityProductToggleModal({
  open,
  onOpenChange,
  cityProduct,
}: CityProductToggleModalProps) {
  const enableMutation = useEnableCityProduct();
  const disableMutation = useDisableCityProduct();

  const isEnabled = cityProduct?.isEnabled ?? false;
  const action = isEnabled ? 'disable' : 'enable';
  const actionCapitalized = action.charAt(0).toUpperCase() + action.slice(1);

  const handleToggle = () => {
    if (!cityProduct) return;

    if (isEnabled) {
      disableMutation.mutate(cityProduct.id, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    } else {
      enableMutation.mutate(cityProduct.id, {
        onSuccess: () => {
          onOpenChange(false);
        }
      });
    }
  };

  const isSubmitting = enableMutation.isPending || disableMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[400px]'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
              isEnabled 
                ? 'bg-red-100' 
                : 'bg-green-100'
            }`}>
              {isEnabled ? (
                <PowerOff className='w-5 h-5 text-red-600' />
              ) : (
                <Power className='w-5 h-5 text-green-600' />
              )}
            </div>
            <div>
              <DialogTitle className={
                isEnabled ? 'text-red-900' : 'text-green-900'
              }>
                {actionCapitalized} Product
              </DialogTitle>
              <DialogDescription className={
                isEnabled ? 'text-red-700' : 'text-green-700'
              }>
                Confirm this action to proceed.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='py-4'>
          <p className='text-sm text-gray-600'>
            Are you sure you want to {action}{' '}
            <span className='font-medium text-gray-900'>
              {cityProduct?.product?.name || 'this product'}
            </span>{' '}
            {cityProduct?.vehicleType?.name && (
              <>
                for{' '}
                <span className='font-medium text-gray-900'>
                  {cityProduct.vehicleType.name}
                </span>{' '}
                vehicles{' '}
              </>
            )}
            in this city?
          </p>
          <p className='text-sm text-gray-500 mt-2'>
            {isEnabled 
              ? 'This product will no longer be available for booking in this city.'
              : 'This product will become available for booking in this city.'
            }
          </p>
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant={isEnabled ? 'destructive' : 'default'}
            onClick={handleToggle}
            disabled={isSubmitting}
            className='min-w-[100px]'
          >
            {isSubmitting ? (
              <div className='flex items-center gap-2'>
                <Spinner size='sm' />
                {actionCapitalized}...
              </div>
            ) : (
              <div className='flex items-center gap-2'>
                {isEnabled ? (
                  <PowerOff className='w-4 h-4' />
                ) : (
                  <Power className='w-4 h-4' />
                )}
                {actionCapitalized} Product
              </div>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}