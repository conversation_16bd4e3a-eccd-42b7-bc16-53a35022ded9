'use client';

import { MapPin } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';

interface CityZoneTableEmptyProps {
   hasFilters: boolean;
}

export function CityZoneTableEmpty({ hasFilters }: CityZoneTableEmptyProps) {
   if (hasFilters) {
      return (
         <div className='flex flex-col items-center justify-center py-12 text-center'>
            <MapPin className='w-12 h-12 text-gray-400 mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>No zones found</h3>
            <p className='text-gray-600 max-w-md mb-6'>
               No zones match your current filters. Try adjusting your search or filter criteria.
            </p>
            <Button variant='outline' onClick={() => window.location.reload()}>
               Clear Filters
            </Button>
         </div>
      );
   }

   return (
      <div className='flex flex-col items-center justify-center py-12 text-center'>
         <MapPin className='w-12 h-12 text-gray-400 mb-4' />
         <h3 className='text-lg font-medium text-gray-900 mb-2'>No city zones yet</h3>
         <p className='text-gray-600 max-w-md'>
            Get started by creating your first zone within this city. Zones help organize different areas for operational purposes.
         </p>
      </div>
   );
}