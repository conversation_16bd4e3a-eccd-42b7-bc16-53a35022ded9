import { InfoWindow } from '@react-google-maps/api';
import { LatLng } from '../../types/city';
import { DrawingMode } from '../../types/drawing';
import { calculatePolygonCenter } from './map-utils';

interface MapLabelProps {
  showLabels: boolean;
  zoom: number;
  drawingMode: DrawingMode;
  isZoneMode: boolean;
  // Current polygon
  currentPolygonLabel?: string;
  existingPolygon: LatLng[];
  // City boundary in zone mode
  cityName?: string;
  cityBoundary: LatLng[];
  // Other zones in zone mode
  otherZonePolygons: LatLng[][];
  otherZoneNames: string[];
  // Other cities in city mode
  otherCityPolygons: LatLng[][];
  otherCityNames: string[];
  // Zones in city mode
  showCityZoneNames: boolean;
  zonePolygonsForCityMode: LatLng[][];
  zoneNamesForCityMode: string[];
}

export function MapLabel({
  showLabels,
  zoom,
  drawingMode,
  isZoneMode,
  currentPolygonLabel,
  existingPolygon,
  cityName,
  cityBoundary,
  otherZonePolygons,
  otherZoneNames,
  otherCityPolygons,
  otherCityNames,
  showCityZoneNames,
  zonePolygonsForCityMode,
  zoneNamesForCityMode,
}: MapLabelProps) {
  if (!showLabels || zoom < 11 || drawingMode !== DrawingMode.VIEWING) {
    return null;
  }

  return (
    <>
      {/* Current polygon label */}
      {currentPolygonLabel && existingPolygon.length > 0 && (() => {
        const center = calculatePolygonCenter(existingPolygon);
        if (!center) return null;
        
        return (
          <InfoWindow
            key="current-polygon-label"
            position={center}
            options={{
              disableAutoPan: true,
              pixelOffset: new window.google.maps.Size(0, 0)
            }}
          >
            <div className="text-xs font-medium text-gray-800 bg-white px-0.5 py-0.5 rounded text-center min-w-0">
              {currentPolygonLabel}
            </div>
          </InfoWindow>
        );
      })()}

      {/* City boundary label in zone mode */}
      {isZoneMode && cityName && cityBoundary.length > 0 && (() => {
        const center = calculatePolygonCenter(cityBoundary);
        if (!center) return null;
        
        return (
          <InfoWindow
            key="city-boundary-label"
            position={center}
            options={{
              disableAutoPan: true,
              pixelOffset: new window.google.maps.Size(0, 0)
            }}
          >
            <div className="text-xs font-medium text-red-700 bg-red-50 px-1.5 py-0.5 rounded text-center min-w-0">
              {cityName} (City)
            </div>
          </InfoWindow>
        );
      })()}

      {/* Other zone polygon labels in zone mode */}
      {isZoneMode && otherZonePolygons.map((polygon, index) => {
        if (polygon.length === 0 || !otherZoneNames[index]) return null;
        
        const center = calculatePolygonCenter(polygon);
        if (!center) return null;
        
        return (
          <InfoWindow
            key={`other-zone-label-${index}`}
            position={center}
            options={{
              disableAutoPan: true,
              pixelOffset: new window.google.maps.Size(0, 0)
            }}
          >
            <div className="text-xs font-medium text-purple-700 bg-purple-50 px-1.5 py-0.5 rounded text-center min-w-0">
              {otherZoneNames[index]}
            </div>
          </InfoWindow>
        );
      })}

      {/* Other city polygon labels in city mode */}
      {!isZoneMode && otherCityPolygons.map((polygon, index) => {
        if (polygon.length === 0 || !otherCityNames[index]) return null;
        
        const center = calculatePolygonCenter(polygon);
        if (!center) return null;
        
        return (
          <InfoWindow
            key={`other-city-label-${index}`}
            position={center}
            options={{
              disableAutoPan: true,
              pixelOffset: new window.google.maps.Size(0, 0)
            }}
          >
            <div className="text-xs font-medium text-blue-700 bg-blue-50 px-1.5 py-0.5 rounded text-center min-w-0">
              {otherCityNames[index]}
            </div>
          </InfoWindow>
        );
      })}

      {/* Zone labels in city mode */}
      {!isZoneMode && showCityZoneNames && zonePolygonsForCityMode.map((polygon, index) => {
        if (polygon.length === 0 || !zoneNamesForCityMode[index]) return null;
        
        const center = calculatePolygonCenter(polygon);
        if (!center) return null;
        
        return (
          <InfoWindow
            key={`zone-city-mode-label-${index}`}
            position={center}
            options={{
              disableAutoPan: true,
              pixelOffset: new window.google.maps.Size(0, 0)
            }}
          >
            <div className="text-xs font-medium text-green-700 bg-green-50 px-1.5 py-0.5 rounded text-center min-w-0">
              {zoneNamesForCityMode[index]}
            </div>
          </InfoWindow>
        );
      })}
    </>
  );
}