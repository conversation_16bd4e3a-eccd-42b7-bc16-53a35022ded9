import { useCallback } from 'react';
import { Polygon } from '@react-google-maps/api';
import { LatLng } from '../../types/city';
import { DrawingMode } from '../../types/drawing';
import {
  polygonOptions,
  editablePolygonOptions,
  zonePolygonOptions,
  editableZonePolygonOptions,
  cityBoundaryPolygonOptions,
  otherZonePolygonOptions,
  otherCityPolygonOptions,
  zoneInCityModePolygonOptions,
} from './polygon-constants';

interface MapPolygonsProps {
  // Main polygon
  polygonPaths: google.maps.LatLng[];
  drawingMode: DrawingMode;
  isZoneMode: boolean;
  onPolygonLoad: (polygon: google.maps.Polygon) => void;
  handlePolygonEdit: () => void;
  // City boundary
  cityBoundary: LatLng[];
  // Other zones
  otherZonePolygons: LatLng[][];
  // Other cities
  otherCityPolygons: LatLng[][];
  // Zones for city mode
  zonePolygonsForCityMode: LatLng[][];
}

export function MapPolygons({
  polygonPaths,
  drawingMode,
  isZoneMode,
  onPolygonLoad,
  handlePolygonEdit,
  cityBoundary,
  otherZonePolygons,
  otherCityPolygons,
  zonePolygonsForCityMode,
}: MapPolygonsProps) {
  const convertToGoogleLatLng = useCallback((coords: LatLng[]) => {
    return coords
      .slice(0, -1)
      .map(coord => new google.maps.LatLng(coord.lat, coord.lng));
  }, []);

  return (
    <>
      {/* Existing Polygon - show when viewing, editing, or redrawing (for preview) */}
      {polygonPaths.length > 0 && drawingMode !== DrawingMode.DRAWING && (
        <Polygon
          paths={polygonPaths}
          options={
            drawingMode === DrawingMode.EDITING 
              ? (isZoneMode ? editableZonePolygonOptions : editablePolygonOptions)
              : (isZoneMode ? zonePolygonOptions : polygonOptions)
          }
          onLoad={onPolygonLoad}
          onMouseUp={drawingMode === DrawingMode.EDITING ? handlePolygonEdit : undefined}
        />
      )}

      {/* City Boundary - show in zone mode as reference with transparent red */}
      {isZoneMode && cityBoundary && cityBoundary.length > 0 && (
        (() => {
          const paths = convertToGoogleLatLng(cityBoundary);
          return (
            <Polygon
              key="city-boundary"
              paths={paths}
              options={cityBoundaryPolygonOptions}
            />
          );
        })()
      )}

      {/* Other Zone Polygons - show in zone mode as reference */}
      {isZoneMode && otherZonePolygons.map((polygon, index) => {
        if (polygon.length === 0) return null;

        const paths = convertToGoogleLatLng(polygon);

        return (
          <Polygon
            key={`other-zone-${index}`}
            paths={paths}
            options={otherZonePolygonOptions}
          />
        );
      })}

      {/* Other City Polygons - show as reference in city mode */}
      {!isZoneMode && otherCityPolygons.map((polygon, index) => {
        if (polygon.length === 0) return null;

        const paths = convertToGoogleLatLng(polygon);

        return (
          <Polygon
            key={`other-city-${index}`}
            paths={paths}
            options={otherCityPolygonOptions}
          />
        );
      })}

      {/* Zone Polygons in City Mode - show as dimmed green reference */}
      {!isZoneMode && zonePolygonsForCityMode.map((polygon, index) => {
        if (polygon.length === 0) return null;

        const paths = convertToGoogleLatLng(polygon);

        return (
          <Polygon
            key={`zone-in-city-${index}`}
            paths={paths}
            options={zoneInCityModePolygonOptions}
          />
        );
      })}
    </>
  );
}