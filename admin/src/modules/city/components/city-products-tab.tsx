'use client';

import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useListCityProducts } from '../api/city-product-queries';
import { CityProduct } from '../types/city-product';
import { AddCityProductsModal } from './add-city-products-modal';
import { CityProductFilters } from './city-product-filters';
import { CityProductTable } from './city-product-table';
import { CityProductToggleModal } from './city-product-toggle-modal';
import { RemoveCityProductModal } from './remove-city-product-modal';
import { CityProductFareTable } from './city-product-fare-table';
import { CityProductFareModal } from './city-product-fare-modal';
import { AttachChargeGroupsModal } from './attach-charge-groups-modal';
import { DeleteCityProductFareModal } from './delete-city-product-fare-modal';
import { useListCityProductFares } from '../api/city-product-fare-queries';
import { CityProductFare } from '../types/city-product-fare';
import { ArrowLeft, Plus } from 'lucide-react';
import { toast } from '@/lib/toast';

interface CityProductsTabProps {
   cityId: string;
}

export function CityProductsTab({ cityId }: CityProductsTabProps) {
   const [page, setPage] = useState(1);
   const [limit] = useState(100);
   const [search, setSearch] = useState('');
   const [vehicleTypeId, setVehicleTypeId] = useState<string | undefined>(undefined);
   const [isEnabled, setIsEnabled] = useState<string | undefined>(undefined);

   // Modal states
   const [removeModalOpen, setRemoveModalOpen] = useState(false);
   const [toggleModalOpen, setToggleModalOpen] = useState(false);
   const [selectedCityProduct, setSelectedCityProduct] = useState<CityProduct | null>(null);

   // Fare management states
   const [selectedCityProductForFares, setSelectedCityProductForFares] =
      useState<CityProduct | null>(null);
   const [isFareModalOpen, setIsFareModalOpen] = useState(false);
   const [isAttachChargeGroupsModalOpen, setIsAttachChargeGroupsModalOpen] = useState(false);
   const [isDeleteFareModalOpen, setIsDeleteFareModalOpen] = useState(false);
   const [selectedFare, setSelectedFare] = useState<CityProductFare | null>(null);
   const [fareToEdit, setFareToEdit] = useState<CityProductFare | null>(null);
   const [fareToDelete, setFareToDelete] = useState<CityProductFare | null>(null);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleVehicleTypeChange = (value: string | undefined) => {
      setVehicleTypeId(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setIsEnabled(value);
      setPage(1);
   };

   const cityProductsQuery = useListCityProducts(cityId, {
      page,
      limit,
      search: search || undefined,
      vehicleTypeId: vehicleTypeId || undefined,
   });

   // Apply status filter client-side since the API doesn't have isEnabled filter
   const filteredData = cityProductsQuery.data
      ? {
           ...cityProductsQuery.data,
           data: cityProductsQuery.data.data.filter(product => {
              if (isEnabled === undefined) return true;
              return product.isEnabled.toString() === isEnabled;
           }),
        }
      : undefined;

   // Calculate total after filtering
   const totalCityProducts = filteredData?.data?.length || 0;

   // Query fares for selected city product
   const listFaresQuery = useListCityProductFares(selectedCityProductForFares?.id || null);

   // Handle modal actions
   const handleRemoveClick = (cityProduct: CityProduct) => {
      setSelectedCityProduct(cityProduct);
      setRemoveModalOpen(true);
   };

   const handleToggleClick = (cityProduct: CityProduct) => {
      setSelectedCityProduct(cityProduct);
      setToggleModalOpen(true);
   };

   const handleManageFairsClick = (cityProduct: CityProduct) => {
      if (!cityProduct.isEnabled) {
         toast.error('Fare management is only available for enabled city products');
         return;
      }
      setSelectedCityProductForFares(cityProduct);
   };

   const handleBackToProducts = () => {
      setSelectedCityProductForFares(null);
   };

   const handleEditFare = (fare: CityProductFare) => {
      setFareToEdit(fare);
      setIsFareModalOpen(true);
   };

   const handleDeleteFare = (fare: CityProductFare) => {
      // Check if it's a primary fare (both zones are null)
      const isPrimaryFare = !fare.fromZoneId && !fare.toZoneId;
      if (isPrimaryFare) {
         toast.error(
            'Primary fare cannot be deleted. It serves as the default fare rule for this city product.'
         );
         return;
      }
      setFareToDelete(fare);
      setIsDeleteFareModalOpen(true);
   };

   const handleManageChargeGroups = (fare: CityProductFare) => {
      setSelectedFare(fare);
      setIsAttachChargeGroupsModalOpen(true);
   };

   // Check if any filters are active
   const hasFilters = !!search || !!vehicleTypeId || !!isEnabled;

   // Render fare management view
   if (selectedCityProductForFares) {
      return (
         <div className='space-y-4'>
            <div className='flex items-center justify-between gap-3'>
               <div className='flex items-center gap-3'>
                  <Button
                     variant='ghost'
                     size='sm'
                     onClick={handleBackToProducts}
                     className='cursor-pointer'
                  >
                     <ArrowLeft className='h-4 w-4' />
                  </Button>
                  <h3 className='text-lg font-semibold text-gray-900'>
                     Manage Fairs - {selectedCityProductForFares.product?.name || 'Product'} (
                     {selectedCityProductForFares.vehicleType?.name || 'Vehicle Type'})
                  </h3>
               </div>
               <Button
                  className='cursor-pointer'
                  variant='outline'
                  onClick={() => {
                     setFareToEdit(null);
                     setIsFareModalOpen(true);
                  }}
               >
                  <Plus className='h-4 w-4' />
                  Add Fare
               </Button>
            </div>

            <CityProductFareTable
               data={listFaresQuery.data?.data}
               isLoading={listFaresQuery.isLoading}
               onEditClick={handleEditFare}
               onDeleteClick={handleDeleteFare}
               onManageChargeGroupsClick={handleManageChargeGroups}
            />

            {/* Fare Modal */}
            <CityProductFareModal
               cityProductId={selectedCityProductForFares.id}
               cityId={cityId}
               fare={fareToEdit}
               open={isFareModalOpen}
               onOpenChange={open => {
                  setIsFareModalOpen(open);
                  if (!open) setFareToEdit(null);
               }}
            />

            {/* Add Charge Groups Modal */}
            {selectedFare && (
               <AttachChargeGroupsModal
                  fareId={selectedFare.id}
                  open={isAttachChargeGroupsModalOpen}
                  onOpenChange={open => {
                     setIsAttachChargeGroupsModalOpen(open);
                     if (!open) setSelectedFare(null);
                  }}
               />
            )}

            {/* Delete Fare Modal */}
            <DeleteCityProductFareModal
               open={isDeleteFareModalOpen}
               onOpenChange={open => {
                  setIsDeleteFareModalOpen(open);
                  if (!open) setFareToDelete(null);
               }}
               fare={fareToDelete}
            />
         </div>
      );
   }

   // Render products list view
   return (
      <div className='space-y-4'>
         {/* Header with Add Button */}
         <div className='flex justify-between items-center'>
            <div>
               <h3 className='text-lg font-semibold text-gray-900'>City Products</h3>
               <p className='text-sm text-gray-600'>Manage products available in this city</p>
            </div>
            <div className='flex items-center gap-4'>
               {/* Product Info */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Products</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalCityProducts}
                     </span>
                  </div>
               </div>
               <AddCityProductsModal cityId={cityId} />
            </div>
         </div>

         {/* Table Card */}
         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <CityProductFilters
               search={search}
               vehicleTypeId={vehicleTypeId}
               isEnabled={isEnabled}
               onSearchChange={handleSearchChange}
               onVehicleTypeChange={handleVehicleTypeChange}
               onStatusChange={handleStatusChange}
               isLoading={cityProductsQuery.isFetching && !cityProductsQuery.isLoading}
            />

            <CityProductTable
               data={filteredData}
               isLoading={cityProductsQuery.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={hasFilters}
               onRemoveClick={handleRemoveClick}
               onToggleClick={handleToggleClick}
               onManageFairsClick={handleManageFairsClick}
            />
         </Card>

         {/* Modals */}
         <RemoveCityProductModal
            open={removeModalOpen}
            onOpenChange={setRemoveModalOpen}
            cityProduct={selectedCityProduct}
            cityId={cityId}
         />

         <CityProductToggleModal
            open={toggleModalOpen}
            onOpenChange={setToggleModalOpen}
            cityProduct={selectedCityProduct}
         />
      </div>
   );
}
