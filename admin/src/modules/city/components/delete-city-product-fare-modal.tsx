'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertTriangle } from 'lucide-react';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { useDeleteCityProductFare } from '../api/city-product-fare-mutations';
import { CityProductFare } from '../types/city-product-fare';

interface DeleteCityProductFareModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  fare: CityProductFare | null;
}

export function DeleteCityProductFareModal({
  open,
  onOpenChange,
  fare,
}: DeleteCityProductFareModalProps) {
  const queryClient = useQueryClient();
  const deleteFareMutation = useDeleteCityProductFare();

  const handleDelete = async () => {
    if (!fare) return;

    deleteFareMutation.mutate(fare.id, {
      onSuccess: () => {
        toast.success('Fare deleted successfully');
        queryClient.invalidateQueries({
          queryKey: ['city-product-fares', fare.cityProductId],
        });
        onOpenChange(false);
      },
      onError: (error: any) => {
        toast.error(
          error?.response?.data?.message ||
            error?.message ||
            'Failed to delete fare'
        );
      },
    });
  };

  if (!fare) return null;

  const hasChargeGroups = fare.fareChargeGroups && fare.fareChargeGroups.length > 0;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[400px]'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className='flex-shrink-0'>
              <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
                <AlertTriangle className='w-5 h-5 text-red-600' />
              </div>
            </div>
            <div>
              <DialogTitle>Delete Fare</DialogTitle>
              <DialogDescription className='mt-1'>
                This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='py-4'>
          <p className='text-sm text-gray-600 mb-4'>
            Are you sure you want to delete the fare with priority{' '}
            <strong>{fare.priority}</strong>
            {fare.fromZone && (
              <>
                {' '}
                from <strong>{fare.fromZone.name}</strong>
              </>
            )}
            {fare.toZone && (
              <>
                {' '}
                to <strong>{fare.toZone.name}</strong>
              </>
            )}
            ? This will permanently remove the fare rule.
          </p>

          {hasChargeGroups && (
            <div className='p-3 bg-amber-50 border border-amber-200 rounded-md'>
              <p className='text-sm text-amber-800'>
                <strong>Warning:</strong> This fare has{' '}
                {fare.fareChargeGroups!.length} attached charge group
                {fare.fareChargeGroups!.length > 1 ? 's' : ''} that will be detached.
              </p>
            </div>
          )}
        </div>

        <DialogFooter className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={deleteFareMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={handleDelete}
            disabled={deleteFareMutation.isPending}
          >
            {deleteFareMutation.isPending ? 'Deleting...' : 'Delete Fare'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
