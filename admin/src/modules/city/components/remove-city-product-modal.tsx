'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Spinner } from '@/components/ui/spinner';
import { AlertTriangle } from 'lucide-react';
import { useRemoveProductsFromCity } from '../api/city-product-mutations';
import { CityProduct } from '../types/city-product';

interface RemoveCityProductModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  cityProduct: CityProduct | null;
  cityId: string;
}

export function RemoveCityProductModal({
  open,
  onOpenChange,
  cityProduct,
  cityId,
}: RemoveCityProductModalProps) {
  const removeProductsMutation = useRemoveProductsFromCity(cityId);

  const handleRemove = () => {
    if (!cityProduct) return;

    const products = [
      {
        productId: cityProduct.productId,
        vehicleTypeId: cityProduct.vehicleTypeId,
      },
    ];

    removeProductsMutation.mutate({ products }, {
      onSuccess: () => {
        onOpenChange(false);
      }
    });
  };

  const isSubmitting = removeProductsMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[400px]'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className='w-10 h-10 rounded-full bg-red-100 flex items-center justify-center'>
              <AlertTriangle className='w-5 h-5 text-red-600' />
            </div>
            <div>
              <DialogTitle className='text-red-900'>Remove Product</DialogTitle>
              <DialogDescription className='text-red-700'>
                This action cannot be undone.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='py-4'>
          <p className='text-sm text-gray-600'>
            Are you sure you want to remove{' '}
            <span className='font-medium text-gray-900'>
              {cityProduct?.product?.name || 'this product'}
            </span>{' '}
            {cityProduct?.vehicleType?.name && (
              <>
                for{' '}
                <span className='font-medium text-gray-900'>
                  {cityProduct.vehicleType.name}
                </span>{' '}
                vehicles{' '}
              </>
            )}
            from this city?
          </p>
          <p className='text-sm text-gray-500 mt-2'>
            The product will no longer be available for this city but will remain in the system.
          </p>
        </div>

        <DialogFooter>
          <Button
            variant='outline'
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant='destructive'
            onClick={handleRemove}
            disabled={isSubmitting}
            className='min-w-[100px]'
          >
            {isSubmitting ? (
              <div className='flex items-center gap-2'>
                <Spinner size='sm' />
                Removing...
              </div>
            ) : (
              'Remove Product'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}