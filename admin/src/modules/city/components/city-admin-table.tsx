'use client';

import { CustomPagination } from '@/components/pagination';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { Mail, Phone, Trash2 } from 'lucide-react';
import { CityAdmin, ListCityAdminsResponse } from '../types/city-admin';
import { CityAdminTableEmpty } from './city-admin-table-empty';
import { CityAdminTableLoading } from './city-admin-table-loading';

interface CityAdminTableProps {
   data?: ListCityAdminsResponse;
   isLoading: boolean;
   currentPage: number;
   onPageChange: (page: number) => void;
   hasFilters: boolean;
   onRemoveClick: (cityAdmin: CityAdmin) => void;
}

const getColumns = ({
   onRemoveClick,
}: {
   onRemoveClick: (cityAdmin: CityAdmin) => void;
}): ColumnDef<CityAdmin>[] => [
   {
      accessorKey: 'user',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Admin</div>,
      cell: ({ row }) => {
         const cityAdmin = row.original as CityAdmin;
         const admin = cityAdmin.admin;
         const user = admin.user;
         const fullName = `${admin.firstName ?? ''} ${admin.lastName ?? ''}`;

         return (
            <div className='flex items-center gap-3'>
               <div className='min-w-0 flex-1'>
                  <div className='font-medium text-sm text-gray-900 truncate'>
                     {fullName || user?.email || 'Unknown Admin'}
                  </div>
                  {/* {fullName && user?.email && (
                     <div className='text-xs text-gray-500 truncate flex items-center gap-1'>
                        <Mail className='w-3 h-3' />
                        {user.email}
                     </div>
                  )} */}
               </div>
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'user.email',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Contact</div>,
      cell: ({ row }) => {
         const user = row.original?.admin?.user;
         return (
            <div className='text-left space-y-1'>
               {user?.email && (
                  <div className='text-sm text-gray-600 flex items-center gap-1'>
                     <Mail className='w-3 h-3' />
                     {user.email}
                  </div>
               )}
               {user?.phoneNumber && (
                  <div className='text-sm text-gray-600 flex items-center gap-1'>
                     <Phone className='w-3 h-3' />
                     {user.phoneNumber}
                  </div>
               )}
            </div>
         );
      },
      size: 200,
   },
   {
      accessorKey: 'status',
      header: () => <div className='text-left font-semibold text-gray-600 text-sm'>Status</div>,
      cell: ({ row }) => {
         const cityAdmin = row.original.admin;
         return (
            <Badge
               variant='secondary'
               className={`text-xs ${
                  cityAdmin.status === 'invited'
                     ? 'bg-orange-100 text-orange-700'
                     : cityAdmin.status === 'active'
                     ? 'bg-green-100 text-green-700'
                     : 'bg-red-100 text-red-700'
               }`}
            >
               {cityAdmin.status}
            </Badge>
         );
      },
      size: 100,
   },
   {
      accessorKey: 'actions',
      header: () => <div className='text-right font-semibold text-gray-600 text-sm'>Actions</div>,
      cell: ({ row }) => {
         const cityAdmin = row.original as CityAdmin;
         return (
            <div className='flex items-center justify-end gap-2'>
               <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onRemoveClick(cityAdmin)}
                  className='text-red-600 hover:text-red-700'
               >
                  <Trash2 className='w-3 h-3' />
                  Remove
               </Button>
            </div>
         );
      },
      size: 120,
   },
];

export function CityAdminTable({
   data,
   isLoading,
   currentPage,
   onPageChange,
   hasFilters,
   onRemoveClick,
}: CityAdminTableProps) {
   const columns = getColumns({
      onRemoveClick,
   });

   const table = useReactTable({
      data: data?.data || [],
      columns,
      getCoreRowModel: getCoreRowModel(),
   });

   if (isLoading) {
      return <CityAdminTableLoading />;
   }

   if (!data?.data?.length) {
      return <CityAdminTableEmpty hasFilters={hasFilters} />;
   }

   return (
      <div className='space-y-2'>
         <div className='rounded-md border'>
            <div className='overflow-x-auto'>
               <table className='w-full'>
                  <thead>
                     {table.getHeaderGroups().map(headerGroup => (
                        <tr key={headerGroup.id} className='border-b bg-gray-50'>
                           {headerGroup.headers.map(header => (
                              <th
                                 key={header.id}
                                 className='h-11 px-4 text-left align-middle'
                                 style={{ width: header.getSize() }}
                              >
                                 {header.isPlaceholder
                                    ? null
                                    : flexRender(
                                         header.column.columnDef.header,
                                         header.getContext()
                                      )}
                              </th>
                           ))}
                        </tr>
                     ))}
                  </thead>
                  <tbody>
                     {table.getRowModel().rows.map(row => (
                        <tr key={row.id} className='border-b transition-colors hover:bg-gray-50/30'>
                           {row.getVisibleCells().map(cell => (
                              <td key={cell.id} className='px-4 py-3 align-middle'>
                                 {flexRender(cell.column.columnDef.cell, cell.getContext())}
                              </td>
                           ))}
                        </tr>
                     ))}
                  </tbody>
               </table>
            </div>
         </div>

         {data && data.meta && data.meta.totalPages > 1 && (
            <CustomPagination
               currentPage={currentPage}
               totalPages={data.meta.totalPages}
               onPageChange={onPageChange}
               hasNext={data.meta.hasNextPage}
               hasPrev={data.meta.hasPreviousPage}
            />
         )}
      </div>
   );
}
