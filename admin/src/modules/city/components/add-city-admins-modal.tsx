'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   <PERSON>alog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { Spinner } from '@/components/ui/spinner';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useAvailableAdmins } from '../api/city-admin-queries';
import { useAddAdminsToCity } from '../api/city-admin-mutations';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

interface AddCityAdminsModalProps {
   cityId: string;
}

export function AddCityAdminsModal({ cityId }: AddCityAdminsModalProps) {
   const { withPermission } = useRoleBasedAccess();
   const [open, setOpen] = useState(false);
   const [selectedOptions, setSelectedOptions] = useState<Option[]>([]);

   const availableAdminsQuery = useAvailableAdmins({
      page: 1,
      limit: 100,
      exceptCityId: cityId,
   });

   const addAdminsMutation = useAddAdminsToCity(cityId);

   // Convert Available Admins to MultipleSelector Option format
   const adminOptions: Option[] = (availableAdminsQuery.data?.data || []).map(admin => {
      const fullName = `${admin.firstName} ${admin.lastName}`;
      return {
         value: admin.id,
         label: `${fullName} (${admin.user.email}) [${admin.status}]`,
         adminId: admin.id,
      };
   });

   const handleSubmit = () => {
      if (selectedOptions.length === 0) return;

      const adminIds = selectedOptions.map((option: any) => option.adminId);

      addAdminsMutation.mutate(
         { adminIds },
         {
            onSuccess: () => {
               setOpen(false);
               setSelectedOptions([]);
            },
         }
      );
   };

   const handleOpenChange = (newOpen: boolean) => {
      setOpen(newOpen);
      if (!newOpen) {
         setSelectedOptions([]);
      }
   };

   const isLoading = availableAdminsQuery.isLoading;
   const isSubmitting = addAdminsMutation.isPending;
   const hasAvailableAdmins = adminOptions.length > 0;

   return (
      <>
         <Button
            variant={'outline'}
            className='flex items-center gap-2'
            onClick={() => withPermission(RBAC_PERMISSIONS.CITY_ADMIN.CREATE, () => setOpen(true))}
         >
            <Plus className='w-4 h-4' />
            Add Existing Admins
         </Button>
         <Dialog open={open} onOpenChange={handleOpenChange}>
            <DialogContent className='sm:max-w-[500px]'>
               <DialogHeader>
                  <DialogTitle>Add Admins to City</DialogTitle>
                  <DialogDescription>
                     Select multiple admins to add to this city. These admins will have
                     administrative access to manage city operations.
                  </DialogDescription>
               </DialogHeader>

               <div className='py-4'>
                  {isLoading ? (
                     <div className='flex items-center justify-center py-8'>
                        <Spinner className='mr-2' />
                        <span className='text-sm text-gray-600'>Loading available admins...</span>
                     </div>
                  ) : (
                     <div className='space-y-2'>
                        <Label htmlFor='admins'>Select Admins </Label>
                        <MultipleSelector
                           value={selectedOptions}
                           onChange={setSelectedOptions}
                           defaultOptions={adminOptions}
                           placeholder='Select admins to add...'
                           emptyIndicator={
                              <p className='text-center text-sm text-gray-500'>No admins found</p>
                           }
                           commandProps={{
                              label: 'Select admins',
                           }}
                        />
                        {!hasAvailableAdmins ? (
                           <div className='text-xs text-gray-500 mt-1'>
                              No admins available for assignment.
                           </div>
                        ) : (
                           <div className='text-xs text-gray-500 mt-1'>
                              Selected: {selectedOptions.length} admin
                              {selectedOptions.length !== 1 ? 's' : ''}
                           </div>
                        )}
                     </div>
                  )}
               </div>

               <DialogFooter>
                  <Button
                     variant='outline'
                     onClick={() => handleOpenChange(false)}
                     disabled={isSubmitting}
                  >
                     Cancel
                  </Button>
                  <Button
                     onClick={handleSubmit}
                     disabled={selectedOptions.length === 0 || isSubmitting || !hasAvailableAdmins}
                     className='min-w-[100px]'
                  >
                     {isSubmitting ? (
                        <div className='flex items-center gap-2'>
                           <Spinner size='sm' />
                           Adding...
                        </div>
                     ) : (
                        `Add ${selectedOptions.length || ''} Admin${
                           selectedOptions.length !== 1 ? 's' : ''
                        }`
                     )}
                  </Button>
               </DialogFooter>
            </DialogContent>
         </Dialog>
      </>
   );
}
