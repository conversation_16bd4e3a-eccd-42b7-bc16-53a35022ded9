'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Spinner } from '@/components/ui/spinner';
import { Search, X, Package, Settings } from 'lucide-react';
import { useEffect, useState, useRef } from 'react';
import { useListVehicleCategory } from '@/modules/vehicle-category/api/queries';

export interface CityProductFiltersProps {
   onSearchChange: (search: string) => void;
   onVehicleTypeChange: (vehicleTypeId: string | undefined) => void;
   onStatusChange: (isEnabled: string | undefined) => void;
   search: string;
   vehicleTypeId: string | undefined;
   isEnabled: string | undefined;
}

export function CityProductFilters({
   onSearchChange,
   onVehicleTypeChange,
   onStatusChange,
   search,
   vehicleTypeId,
   isEnabled,
   isLoading,
}: CityProductFiltersProps & { isLoading?: boolean }) {
   const [searchValue, setSearchValue] = useState(search || '');
   const [isSearching, setIsSearching] = useState(false);
   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

   // Fetch vehicle types for the dropdown
   const vehicleTypesQuery = useListVehicleCategory({});

   // Update local search state when props change
   useEffect(() => {
      setSearchValue(search || '');
   }, [search]);

   // Clean up timeouts on unmount
   useEffect(() => {
      return () => {
         if (searchTimeoutRef.current) {
            clearTimeout(searchTimeoutRef.current);
         }
      };
   }, []);

   // Handle search input with debounce
   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setSearchValue(value);

      // Show searching indicator
      setIsSearching(true);

      // Clear any existing timeout
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
      }

      // Set a new timeout
      searchTimeoutRef.current = setTimeout(() => {
         onSearchChange(value);
         searchTimeoutRef.current = null;
         setIsSearching(false);
      }, 500); // 500ms debounce time
   };

   // Clear all filters
   const handleClearFilters = () => {
      // Clear any pending timeouts
      if (searchTimeoutRef.current) {
         clearTimeout(searchTimeoutRef.current);
         searchTimeoutRef.current = null;
      }

      setIsSearching(false);
      setSearchValue('');
      onSearchChange('');
      onVehicleTypeChange(undefined);
      onStatusChange(undefined);
   };

   // Check if any filters are active
   const hasActiveFilters = !!search || !!vehicleTypeId || !!isEnabled;

   return (
      <div className='p-4 bg-gray-50/50 border-b'>
         {/* Main filters grid */}
         <div className='grid grid-cols-1 sm:grid-cols-12 gap-4 items-center'>
            {/* Search Input */}
            <div className='sm:col-span-5 relative'>
               <div className='relative'>
                  <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4' />
                  <Input
                     placeholder='Search by product name...'
                     value={searchValue}
                     onChange={handleSearchChange}
                     className='pl-10 pr-10'
                  />
                  {(isSearching || isLoading) && (
                     <div className='absolute right-8 top-1/2 transform -translate-y-1/2'>
                        <Spinner className='sm' />
                     </div>
                  )}
                  {searchValue && !isSearching && (
                     <button
                        onClick={() => {
                           setSearchValue('');
                           onSearchChange('');
                        }}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                     >
                        <X className='h-4 w-4' />
                     </button>
                  )}
               </div>
            </div>

            {/* Vehicle Type Filter */}
            <div className='sm:col-span-3'>
               <Select
                  value={vehicleTypeId || 'all'}
                  onValueChange={(value) => onVehicleTypeChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-full'>
                     <div className='flex items-center gap-2'>
                        <Package className='h-4 w-4 text-gray-500 flex-shrink-0' />
                        <SelectValue placeholder='All Vehicle Types' />
                     </div>
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Vehicle Types</SelectItem>
                     {vehicleTypesQuery.data?.data?.map((vehicleType) => (
                        <SelectItem key={vehicleType.id} value={vehicleType.id}>
                           {vehicleType.name}
                        </SelectItem>
                     ))}
                  </SelectContent>
               </Select>
            </div>

            {/* Status Filter */}
            <div className='sm:col-span-2'>
               <Select
                  value={isEnabled || 'all'}
                  onValueChange={(value) => onStatusChange(value === 'all' ? undefined : value)}
               >
                  <SelectTrigger className='w-full'>
                     <div className='flex items-center gap-2'>
                        <Settings className='h-4 w-4 text-gray-500 flex-shrink-0' />
                        <SelectValue placeholder='All Status' />
                     </div>
                  </SelectTrigger>
                  <SelectContent>
                     <SelectItem value='all'>All Status</SelectItem>
                     <SelectItem value='true'>Active</SelectItem>
                     <SelectItem value='false'>Inactive</SelectItem>
                  </SelectContent>
               </Select>
            </div>

            {/* Clear Filters Button - Always takes up space */}
            <div className='sm:col-span-2'>
               {hasActiveFilters ? (
                  <Button
                     variant='outline'
                     size='sm'
                     onClick={handleClearFilters}
                     className='flex items-center gap-2 whitespace-nowrap w-full justify-center'
                  >
                     <X className='h-4 w-4' />
                     Clear Filters
                  </Button>
               ) : (
                  <div className='h-9'></div>
               )}
            </div>
         </div>
      </div>
   );
}