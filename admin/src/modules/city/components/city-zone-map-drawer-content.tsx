'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogFooter,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { toast } from '@/lib/toast';
import { useJsApiLoader } from '@react-google-maps/api';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useUpdateZone } from '../api/city-zone-mutations';
import { useGetCity } from '../api/queries';
import { useCityZones } from '../api/city-zone-queries';
import { Zone } from '../types/city-zone';
import { LatLng } from '../types/city';
import { DrawingMode } from '../types/drawing';
import {
   validatePolygon,
   checkZoneWithinCityBoundary,
   checkPolygonIntersections,
} from '../utils/polygon-utils';
import { CitySearch, MapWithDrawing } from './boundary-drawer';
import { ZoneDrawingControls } from './ZoneDrawingControls';

const libraries: ('drawing' | 'places')[] = ['drawing', 'places'];

interface CityZoneMapDrawerContentProps {
   zone: Zone;
   isFullscreen?: boolean;
   onFullscreen?: () => void;
   onClose: () => void;
}

export function CityZoneMapDrawerContent({
   zone,
   isFullscreen: _isFullscreen = false,
   onFullscreen: _onFullscreen,
   onClose,
}: CityZoneMapDrawerContentProps) {
   const existingPolygon = useMemo(() => zone?.polygon || [], [zone?.polygon]);

   const [drawingMode, setDrawingMode] = useState<DrawingMode>(DrawingMode.VIEWING);
   const [activePolygon, setActivePolygon] = useState<google.maps.Polygon | null>(null);
   const [polygonCoordinates, setPolygonCoordinates] = useState<LatLng[]>(existingPolygon);
   const [mapCenter, setMapCenter] = useState<{ lat: number; lng: number }>({
      lat: 10,
      lng: 77,
   });
   const [mapZoom, setMapZoom] = useState(9);
   const [hasAutoZoomed, setHasAutoZoomed] = useState(false);
   const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
   const [hasCompletedRedrawing, setHasCompletedRedrawing] = useState(false);
   const cancelDrawingRef = useRef<boolean>(false);
   const drawingModeRef = useRef<DrawingMode>(drawingMode);
   const mapRef = useRef<google.maps.Map | null>(null);

   // Update the ref whenever drawingMode changes
   useEffect(() => {
      drawingModeRef.current = drawingMode;
   }, [drawingMode]);

   // React Query hooks
   const { data: currentCity, isLoading: cityLoading } = useGetCity(zone.cityId);
   const { data: allCityZones, isLoading: cityZonesLoading } = useCityZones(zone.cityId, {
      includeRelations: false,
   });
   const updateZoneMutation = useUpdateZone();

   const { isLoaded, loadError } = useJsApiLoader({
      id: 'google-map-script',
      googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY as string,
      libraries,
   });

   // Derived data
   const cityBoundary = useMemo(
      () => currentCity?.data?.polygon || [],
      [currentCity?.data?.polygon]
   );
   const otherZonePolygons = useMemo(() => {
      return (
         allCityZones?.data
            ?.filter(z => z.id !== zone.id && z.polygon && z.polygon.length > 0)
            .map(z => z.polygon!) || []
      );
   }, [allCityZones?.data, zone.id]);

   const otherZoneNames = useMemo(() => {
      return (
         allCityZones?.data
            ?.filter(z => z.id !== zone.id && z.polygon && z.polygon.length > 0)
            .map(z => z.name) || []
      );
   }, [allCityZones?.data, zone.id]);

   const handleStartDrawing = useCallback(() => {
      if (activePolygon) {
         activePolygon.setMap(null);
      }
      setActivePolygon(null);

      // Check if we're redrawing an existing polygon
      const hasExistingPolygon = existingPolygon && existingPolygon.length > 0;
      if (hasExistingPolygon) {
         setDrawingMode(DrawingMode.REDRAWING);
         setPolygonCoordinates([]);
         setHasCompletedRedrawing(false);
      } else {
         setDrawingMode(DrawingMode.DRAWING);
         setPolygonCoordinates([]);
      }
   }, [activePolygon, existingPolygon]);

   const handleCancelDrawing = useCallback(() => {
      cancelDrawingRef.current = true;

      if (drawingMode === DrawingMode.DRAWING) {
         if (activePolygon) {
            activePolygon.setMap(null);
         }
         setActivePolygon(null);
         setPolygonCoordinates(zone?.polygon || []);
         setDrawingMode(DrawingMode.VIEWING);
         return;
      } else if (drawingMode === DrawingMode.REDRAWING) {
         if (activePolygon) {
            activePolygon.setMap(null);
         }
         setActivePolygon(null);
         setPolygonCoordinates(zone?.polygon || []);
         setDrawingMode(DrawingMode.VIEWING);
         setHasCompletedRedrawing(false);
         return;
      } else if (drawingMode === DrawingMode.EDITING) {
         setDrawingMode(DrawingMode.VIEWING);
         setPolygonCoordinates(zone?.polygon || []);
      }
   }, [drawingMode, activePolygon, zone?.polygon]);

   useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
         if (
            event.key === 'Escape' &&
            (drawingMode === DrawingMode.DRAWING ||
               drawingMode === DrawingMode.EDITING ||
               drawingMode === DrawingMode.REDRAWING)
         ) {
            event.preventDefault();
            if (drawingMode === DrawingMode.DRAWING || drawingMode === DrawingMode.REDRAWING) {
               cancelDrawingRef.current = true;
            }
            handleCancelDrawing();
         }
      };

      document.addEventListener('keydown', handleKeyDown);
      return () => {
         document.removeEventListener('keydown', handleKeyDown);
      };
   }, [drawingMode, handleCancelDrawing]);

   useEffect(() => {
      if (hasAutoZoomed) return;

      if (existingPolygon.length > 0) {
         // Zone has polygon, zoom to it
         const firstPoint = existingPolygon[0];
         setMapCenter({
            lat: firstPoint.lat,
            lng: firstPoint.lng,
         });

         const savedZoomLevel = zone.meta?.zoomLevel;
         if (savedZoomLevel && !isNaN(Number(savedZoomLevel))) {
            setMapZoom(Number(savedZoomLevel));
         } else {
            setMapZoom(12);
         }
      } else if (cityBoundary.length > 0) {
         // No zone polygon, but city has boundary - zoom to city boundary
         // Use lower zoom for city zones modal to show more area
         const firstPoint = cityBoundary[0];
         setMapCenter({
            lat: firstPoint.lat,
            lng: firstPoint.lng,
         });
         setMapZoom(12);
      }

      setHasAutoZoomed(true);
   }, [existingPolygon, cityBoundary, hasAutoZoomed, zone.meta?.zoomLevel]);

   const handleEditPolygon = useCallback(() => {
      setDrawingMode(DrawingMode.EDITING);
   }, []);

   const validateAndHandlePolygon = useCallback(
      (coordinates: LatLng[], onValidationFailure: () => void) => {
         const validation = validatePolygon(coordinates);
         if (!validation.isValid) {
            toast.error(`Invalid polygon: ${validation.errors.join(', ')}`);
            onValidationFailure();
            return false;
         }

         const boundaryCheck = checkZoneWithinCityBoundary(coordinates, cityBoundary);
         if (!boundaryCheck.isWithin) {
            toast.error(boundaryCheck.error || 'Zone must be within city boundary');
            onValidationFailure();
            return false;
         }

         if (otherZonePolygons.length > 0) {
            const intersectionCheck = checkPolygonIntersections(coordinates, otherZonePolygons);
            if (intersectionCheck.hasIntersection) {
               const intersectingZones =
                  allCityZones?.data
                     ?.filter(
                        (z, index) =>
                           z.id !== zone.id &&
                           z.polygon &&
                           z.polygon.length > 0 &&
                           intersectionCheck.intersectingIndexes.includes(index)
                     )
                     .map(z => z.name)
                     .join(', ') || 'other zones';

               toast.error(
                  `This zone boundary intersects with existing zones: ${intersectingZones}. Please adjust the boundary to avoid overlaps.`
               );
               onValidationFailure();
               return false;
            }
         }

         return true;
      },
      [cityBoundary, otherZonePolygons, allCityZones?.data, zone.id]
   );

   const revertToViewingMode = useCallback(
      (failedPolygon: google.maps.Polygon | null = null) => {
         // Remove the failed polygon from map if provided
         if (failedPolygon) {
            failedPolygon.setMap(null);
         }

         // Clean up active polygon if it's different from the failed one
         if (activePolygon && activePolygon !== failedPolygon) {
            activePolygon.setMap(null);
         }

         setActivePolygon(null);
         setPolygonCoordinates(zone?.polygon || []);
         setDrawingMode(DrawingMode.VIEWING);
         setHasCompletedRedrawing(false);
      },
      [activePolygon, zone?.polygon]
   );

   const handlePolygonComplete = useCallback(
      async (polygon: google.maps.Polygon) => {
         if (cancelDrawingRef.current) {
            cancelDrawingRef.current = false;
            polygon.setMap(null);
            return;
         }

         const path = polygon.getPath();
         const coordinates: LatLng[] = [];

         for (let i = 0; i < path.getLength(); i++) {
            const point = path.getAt(i);
            coordinates.push({
               lat: point.lat(),
               lng: point.lng(),
            });
         }

         if (coordinates.length > 0) {
            coordinates.push(coordinates[0]);
         }

         const originalDrawingMode = drawingModeRef.current;

         /**
          * Only firing on Drawing mode because we do the same check
          * in polygonSave for other modes
          */
         if (
            originalDrawingMode === DrawingMode.DRAWING &&
            !validateAndHandlePolygon(coordinates, () => revertToViewingMode(polygon))
         ) {
            return;
         }

         if (originalDrawingMode === DrawingMode.REDRAWING) {
            setPolygonCoordinates(coordinates);
            setActivePolygon(polygon);
            setHasCompletedRedrawing(true);
         } else if (originalDrawingMode === DrawingMode.DRAWING) {
            setPolygonCoordinates(coordinates);
            setActivePolygon(polygon);
            setDrawingMode(DrawingMode.VIEWING);

            const currentZoom = mapRef.current?.getZoom() || mapZoom;

            updateZoneMutation.mutate(
               {
                  id: zone.id,
                  data: {
                     polygon: coordinates,
                     meta: {
                        ...zone.meta,
                        zoomLevel: currentZoom.toString(),
                     },
                  },
               },
               {
                  onSuccess: () => {
                     // Success message handled by useUpdateZone hook
                  },
               }
            );
         }
      },
      [
         validateAndHandlePolygon,
         revertToViewingMode,
         zone.id,
         zone.meta,
         mapZoom,
         updateZoneMutation,
      ]
   );

   const handleSavePolygon = useCallback(async () => {
      if (polygonCoordinates.length < 4) {
         toast.error('Please draw a polygon with at least 3 points');
         return;
      }

      if (!validateAndHandlePolygon(polygonCoordinates, revertToViewingMode)) {
         return;
      }

      const currentZoom = mapRef.current?.getZoom() || mapZoom;

      updateZoneMutation.mutate(
         {
            id: zone.id,
            data: {
               polygon: polygonCoordinates,
               meta: {
                  ...zone.meta,
                  zoomLevel: currentZoom.toString(),
               },
            },
         },
         {
            onSuccess: () => {
               setDrawingMode(DrawingMode.VIEWING);
               setHasCompletedRedrawing(false);
            },
         }
      );
   }, [
      polygonCoordinates,
      validateAndHandlePolygon,
      revertToViewingMode,
      updateZoneMutation,
      zone.id,
      zone.meta,
      mapZoom,
   ]);

   const handleZoomChange = useCallback((zoom: number) => {
      setMapZoom(zoom);
   }, []);

   const handleMapLoad = useCallback((map: google.maps.Map) => {
      mapRef.current = map;
   }, []);

   const handleCitySelect = useCallback((place: google.maps.places.PlaceResult) => {
      if (place.geometry?.location) {
         setMapCenter({
            lat: place.geometry.location.lat(),
            lng: place.geometry.location.lng(),
         });
         setMapZoom(12);
      }
   }, []);

   const handleDeletePolygon = useCallback(() => {
      setShowDeleteConfirmation(true);
   }, []);

   const handleConfirmDelete = useCallback(async () => {
      if (activePolygon) {
         activePolygon.setMap(null);
      }
      setActivePolygon(null);
      setPolygonCoordinates([]);
      setDrawingMode(DrawingMode.VIEWING);
      setShowDeleteConfirmation(false);

      updateZoneMutation.mutate(
         {
            id: zone.id,
            data: {
               polygon: null,
            },
         },
         {
            onSuccess: () => {
               toast.success('Zone boundary deleted successfully!');
            },
         }
      );
   }, [activePolygon, zone.id, updateZoneMutation]);

   if (loadError) {
      return (
         <div className='h-full w-full bg-gray-100 flex items-center justify-center'>
            <div className='text-center'>
               <p className='text-red-600'>Error loading Google Maps</p>
            </div>
         </div>
      );
   }

   if (!isLoaded || cityZonesLoading || cityLoading) {
      return (
         <div className='h-full w-full bg-gray-100 flex items-center justify-center'>
            <div className='text-center'>
               <p className='text-gray-600'>Loading map...</p>
            </div>
         </div>
      );
   }

   return (
      <div className='h-full w-full relative'>
         {/* City Search */}
         <div className='absolute top-[14px] left-4 z-10 bg-white rounded-lg shadow-lg'>
            <CitySearch onCitySelect={handleCitySelect} />
         </div>

         {/* Zone Drawing Controls */}
         <div className='absolute -top-1 -right-2 z-10 bg-white rounded-lg shadow-lg'>
            <ZoneDrawingControls
               mode={drawingMode}
               onStartDrawing={handleStartDrawing}
               onCancelDrawing={handleCancelDrawing}
               onEditPolygon={handleEditPolygon}
               onSavePolygon={handleSavePolygon}
               onDeletePolygon={handleDeletePolygon}
               onClose={onClose}
               isSaving={updateZoneMutation.isPending}
               hasPolygon={polygonCoordinates.length > 0}
               zoneName={zone.name}
               hasCompletedRedrawing={hasCompletedRedrawing}
            />
         </div>

         {/* Map Component */}
         <MapWithDrawing
            center={mapCenter}
            zoom={mapZoom}
            drawingMode={drawingMode}
            existingPolygon={polygonCoordinates}
            onPolygonComplete={handlePolygonComplete}
            onPolygonEdit={setPolygonCoordinates}
            onZoomChange={handleZoomChange}
            onMapLoad={handleMapLoad}
            disableDrawing={drawingMode === DrawingMode.REDRAWING && hasCompletedRedrawing}
            cancelDrawingRef={cancelDrawingRef}
            isZoneMode={true}
            cityBoundary={cityBoundary}
            otherZonePolygons={otherZonePolygons}
            showLabels={true}
            showCityZoneNames={true}
            currentPolygonLabel={zone.name}
            cityName={currentCity?.data?.name}
            otherZoneNames={otherZoneNames}
         />

         {/* Delete Confirmation Modal */}
         <Dialog open={showDeleteConfirmation} onOpenChange={setShowDeleteConfirmation}>
            <DialogContent>
               <DialogHeader>
                  <DialogTitle>Delete Zone Boundary</DialogTitle>
                  <DialogDescription>
                     Are you sure you want to delete the boundary for &quot;{zone.name}&quot;? This
                     action cannot be undone.
                  </DialogDescription>
               </DialogHeader>
               <DialogFooter>
                  <Button
                     variant='outline'
                     onClick={() => setShowDeleteConfirmation(false)}
                     disabled={updateZoneMutation.isPending}
                  >
                     Cancel
                  </Button>
                  <Button
                     variant='destructive'
                     onClick={handleConfirmDelete}
                     disabled={updateZoneMutation.isPending}
                  >
                     {updateZoneMutation.isPending ? 'Deleting...' : 'Delete'}
                  </Button>
               </DialogFooter>
            </DialogContent>
         </Dialog>
      </div>
   );
}
