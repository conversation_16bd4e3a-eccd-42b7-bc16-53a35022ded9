import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   ListCityProductParams,
   ListCityProductResponse,
} from '../types/city-product';

export const useListCityProducts = (cityId: string, params: ListCityProductParams) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: [
         'city-products',
         cityId,
         params.page,
         params.limit,
         params.search,
         params.productName,
         params.vehicleTypeId,
      ],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListCityProductResponse> => {
         return apiClient.get(`/city-products/cities/${cityId}/paginate`, {
            params: {
               page: params.page,
               limit: params.limit,
               search: params.search,
               productName: params.productName,
               vehicleTypeId: params.vehicleTypeId,
            },
         });
      },
      enabled: !!cityId,
   });
};

// Re-export the existing product query for use in city products
export { useListProduct as useAvailableProducts } from '@/modules/product/api/queries';
