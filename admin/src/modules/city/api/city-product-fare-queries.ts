import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import {
  ListCityProductFaresResponse,
  GetFareChargeGroupsResponse,
} from '../types/city-product-fare';

/**
 * Hook for listing city product fares by city product ID
 */
export const useListCityProductFares = (cityProductId: string | null) => {
  return useQuery({
    queryKey: ['city-product-fares', cityProductId],
    queryFn: (): Promise<ListCityProductFaresResponse> => {
      return apiClient.get(`/city-product-fares/${cityProductId}`);
    },
    enabled: !!cityProductId,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting charge groups attached to a fare
 */
export const useGetFareChargeGroups = (fareId: string | null, enabled = true) => {
  return useQuery({
    queryKey: ['fare-charge-groups', fareId],
    queryFn: (): Promise<GetFareChargeGroupsResponse> => {
      return apiClient.get(`/city-product-fares/${fareId}/charge-groups`);
    },
    enabled: enabled && !!fareId,
    refetchOnWindowFocus: false,
  });
};
