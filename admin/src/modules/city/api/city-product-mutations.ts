import { apiClient } from '@/lib/api-client';
import { toast } from '@/lib/toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  AddProductsToCityRequest,
  RemoveProductsFromCityRequest,
  CityProductResponse
} from '../types/city-product';

export const useAddProductsToCity = (cityId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: AddProductsToCityRequest): Promise<any> => {
      return apiClient.post(`/city-products/cities/${cityId}/add-products`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['city-products', cityId] });
      queryClient.invalidateQueries({ queryKey: ['available-products', cityId] });
      toast.success('Products added to city successfully');
    },
  });
};

export const useRemoveProductsFromCity = (cityId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (data: RemoveProductsFromCityRequest): Promise<any> => {
      return apiClient.post(`/city-products/cities/${cityId}/remove-products`, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['city-products', cityId] });
      queryClient.invalidateQueries({ queryKey: ['available-products', cityId] });
      toast.success('Products removed from city successfully');
    },
  });
};

export const useEnableCityProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string): Promise<CityProductResponse> => {
      return apiClient.patch(`/city-products/${id}/enable`);
    },
    onSuccess: (data) => {
      const cityId = data.data.cityId;
      queryClient.invalidateQueries({ queryKey: ['city-products', cityId] });
      toast.success('City product enabled successfully');
    },
  });
};

export const useDisableCityProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string): Promise<CityProductResponse> => {
      return apiClient.patch(`/city-products/${id}/disable`);
    },
    onSuccess: (data) => {
      const cityId = data.data.cityId;
      queryClient.invalidateQueries({ queryKey: ['city-products', cityId] });
      toast.success('City product disabled successfully');
    },
  });
};