import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
   ListCityAdminsParams,
   ListCityAdminsResponse,
   ListAvailableAdminsResponse,
} from '../types/city-admin';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListCityAdmins = (cityId: string, params: ListCityAdminsParams) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      enabled: hasPermission(RBAC_PERMISSIONS.CITY_ADMIN.LIST),
      placeholderData: keepPreviousData,
      queryKey: ['city-admins', cityId, params.page, params.limit, params.search, params.status],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListCityAdminsResponse> => {
         return apiClient.get(`/admin/sub-admin/cities/${cityId}/list-admins`, {
            params: {
               page: params.page,
               limit: params.limit,
               search: params.search,
               status: params.status,
            },
         });
      },
   });
};

export const useAvailableAdmins = (params: {
   page?: number;
   limit?: number;
   exceptCityId?: string;
   status?: string;
}) => {
   return useQuery({
      placeholderData: keepPreviousData,
      queryKey: ['available-admins', params.page, params.limit, params.exceptCityId],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListAvailableAdminsResponse> => {
         return apiClient.get('/roles/identifier/city_admin/admins', {
            params: {
               page: params.page,
               limit: params.limit,
               exceptCityId: params.exceptCityId,
               status: params.status,
            },
         });
      },
   });
};
