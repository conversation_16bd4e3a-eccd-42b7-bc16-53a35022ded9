import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/lib/toast';
import {
  CreateZoneRequest,
  UpdateZoneRequest,
  ZoneCreateResponse,
  ZoneUpdateResponse,
  ZoneDeleteResponse,
} from '../types/city-zone';

/**
 * Hook for creating a new zone
 */
export const useCreateZone = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateZoneRequest): Promise<ZoneCreateResponse> => {
      return apiClient.post('/zones', data);
    },
    onSuccess: (response, variables) => {
      // Invalidate city zones query for the specific city
      queryClient.invalidateQueries({
        queryKey: ['city-zones', variables.cityId],
      });
      // Invalidate general zones list
      queryClient.invalidateQueries({
        queryKey: ['zones'],
      });
      toast.success('Zone created successfully!');
    },
  });
};

/**
 * Hook for updating a zone
 */
export const useUpdateZone = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: UpdateZoneRequest;
    }): Promise<ZoneUpdateResponse> => {
      return apiClient.put(`/zones/${id}`, data);
    },
    onSuccess: (response, { id }) => {
      // Invalidate specific zone query
      queryClient.invalidateQueries({
        queryKey: ['zone', id],
      });
      // Invalidate city zones if we know the cityId
      if (response.data.cityId) {
        queryClient.invalidateQueries({
          queryKey: ['city-zones', response.data.cityId],
        });
      }
      // Invalidate general zones list
      queryClient.invalidateQueries({
        queryKey: ['zones'],
      });
      toast.success('Zone updated successfully!');
    },
  });
};

/**
 * Hook for deleting a zone (soft delete)
 */
export const useDeleteZone = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string): Promise<ZoneDeleteResponse> => {
      return apiClient.delete(`/zones/${id}`);
    },
    onSuccess: (response, id) => {
      // Invalidate specific zone query
      queryClient.invalidateQueries({
        queryKey: ['zone', id],
      });
      // Invalidate all city zones and zones lists
      queryClient.invalidateQueries({
        queryKey: ['city-zones'],
      });
      queryClient.invalidateQueries({
        queryKey: ['zones'],
      });
      toast.success('Zone deleted successfully!');
    },
  });
};

/**
 * Hook for restoring a soft-deleted zone
 */
export const useRestoreZone = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string): Promise<ZoneUpdateResponse> => {
      return apiClient.put(`/zones/${id}/restore`);
    },
    onSuccess: (response, id) => {
      // Invalidate specific zone query
      queryClient.invalidateQueries({
        queryKey: ['zone', id],
      });
      // Invalidate city zones if we know the cityId
      if (response.data.cityId) {
        queryClient.invalidateQueries({
          queryKey: ['city-zones', response.data.cityId],
        });
      }
      // Invalidate general zones list
      queryClient.invalidateQueries({
        queryKey: ['zones'],
      });
      toast.success('Zone restored successfully!');
    },
  });
};

/**
 * Hook for hard deleting a zone (permanent deletion)
 */
export const useHardDeleteZone = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string): Promise<{ 
      success: boolean; 
      message: string; 
      timestamp: number; 
    }> => {
      return apiClient.delete(`/zones/${id}/hard`);
    },
    onSuccess: (response, id) => {
      // Invalidate specific zone query
      queryClient.invalidateQueries({
        queryKey: ['zone', id],
      });
      // Invalidate all city zones and zones lists
      queryClient.invalidateQueries({
        queryKey: ['city-zones'],
      });
      queryClient.invalidateQueries({
        queryKey: ['zones'],
      });
      toast.success('Zone permanently deleted!');
    },
  });
};