import { apiClient } from '@/lib/api-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  CreateCityProductFareRequest,
  UpdateCityProductFareRequest,
  AttachChargeGroupsRequest,
  UpdateChargeGroupPrioritiesRequest,
  CityProductFareResponse,
  AttachChargeGroupsResponse,
  DetachChargeGroupResponse,
  UpdatePrioritiesResponse,
} from '../types/city-product-fare';

/**
 * Hook for creating a new city product fare
 */
export const useCreateCityProductFare = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      data: CreateCityProductFareRequest
    ): Promise<CityProductFareResponse> => {
      return apiClient.post('/city-product-fares', data);
    },
    onSuccess: (data) => {
      const cityProductId = data.data.cityProductId;
      queryClient.invalidateQueries({
        queryKey: ['city-product-fares', cityProductId],
      });
    },
  });
};

/**
 * Hook for updating a city product fare
 */
export const useUpdateCityProductFare = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      fareId,
      data,
    }: {
      fareId: string;
      data: UpdateCityProductFareRequest;
    }): Promise<CityProductFareResponse> => {
      return apiClient.patch(`/city-product-fares/${fareId}`, data);
    },
    onSuccess: (data) => {
      const cityProductId = data.data.cityProductId;
      queryClient.invalidateQueries({
        queryKey: ['city-product-fares', cityProductId],
      });
    },
  });
};

/**
 * Hook for deleting a city product fare
 */
export const useDeleteCityProductFare = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (fareId: string): Promise<CityProductFareResponse> => {
      return apiClient.delete(`/city-product-fares/${fareId}`);
    },
    onSuccess: (data) => {
      const cityProductId = data.data.cityProductId;
      queryClient.invalidateQueries({
        queryKey: ['city-product-fares', cityProductId],
      });
    },
  });
};

/**
 * Hook for attaching charge groups to a fare
 */
export const useAttachChargeGroups = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      fareId,
      data,
    }: {
      fareId: string;
      data: AttachChargeGroupsRequest;
    }): Promise<AttachChargeGroupsResponse> => {
      return apiClient.post(
        `/city-product-fares/${fareId}/charge-groups`,
        data
      );
    },
    onSuccess: (_, variables) => {
      const fareId = variables.fareId;
      queryClient.invalidateQueries({
        queryKey: ['fare-charge-groups', fareId],
      });
    },
  });
};

/**
 * Hook for detaching a charge group from a fare
 */
export const useDetachChargeGroup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      fareId,
      chargeGroupId,
    }: {
      fareId: string;
      chargeGroupId: string;
    }): Promise<DetachChargeGroupResponse> => {
      return apiClient.delete(
        `/city-product-fares/${fareId}/charge-groups/${chargeGroupId}`
      );
    },
    onSuccess: (_, variables) => {
      const fareId = variables.fareId;
      queryClient.invalidateQueries({
        queryKey: ['fare-charge-groups', fareId],
      });
    },
  });
};

/**
 * Hook for updating charge group priorities
 */
export const useUpdateChargeGroupPriorities = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      fareId,
      data,
    }: {
      fareId: string;
      data: UpdateChargeGroupPrioritiesRequest;
    }): Promise<UpdatePrioritiesResponse> => {
      return apiClient.put(
        `/city-product-fares/${fareId}/charge-groups/priorities`,
        data
      );
    },
    onSuccess: (_, variables) => {
      const fareId = variables.fareId;
      queryClient.invalidateQueries({
        queryKey: ['fare-charge-groups', fareId],
      });
    },
  });
};
