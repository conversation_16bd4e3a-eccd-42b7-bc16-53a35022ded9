import { apiClient } from '@/lib/api-client';
import { toast } from '@/lib/toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { AddAdminToCityRequest, RemoveAdminFromCityRequest } from '../types/city-admin';

export const useAddAdminsToCity = (cityId: string) => {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: AddAdminToCityRequest): Promise<any> => {
         return apiClient.post(`/admin/sub-admin/cities/${cityId}/add-admin`, data);
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ['city-admins', cityId] });
         queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         toast.success('Admins added to city successfully');
      },
   });
};

export const useRemoveAdminFromCity = (cityId: string) => {
   const queryClient = useQueryClient();

   return useMutation({
      mutationFn: async (data: RemoveAdminFromCityRequest): Promise<any> => {
         return apiClient.post(`/admin/sub-admin/cities/${cityId}/remove-admin`, data);
      },
      onSuccess: () => {
         queryClient.invalidateQueries({ queryKey: ['city-admins', cityId] });
         queryClient.invalidateQueries({ queryKey: ['available-admins'] });
         toast.success('Admin removed from city successfully');
      },
   });
};
