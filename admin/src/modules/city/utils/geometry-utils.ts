import wkx from 'wkx';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { LatLng } from '../types/city';

/**
 * Convert buffer to hexadecimal string
 */
export function buf2hex(buffer: ArrayBuffer): string {
  return [...new Uint8Array(buffer)]
    .map((x) => x.toString(16).padStart(2, "0"))
    .join("");
}

/**
 * Convert coordinates array to WKT (Well-Known Text) format
 */
export function coordinatesToWKT(coordinates: LatLng[]): string {
  // Remove the closing coordinate if it duplicates the first one
  const coords = coordinates.length > 0 && 
    coordinates[0].lat === coordinates[coordinates.length - 1].lat &&
    coordinates[0].lng === coordinates[coordinates.length - 1].lng
    ? coordinates.slice(0, -1)
    : coordinates;

  // Convert to WKX format: [lng, lat] pairs
  const wkxCoordinates = coords.map(coord => [coord.lng, coord.lat]);
  
  const geometry = wkx.Geometry.parseGeoJSON({
    type: "Polygon",
    coordinates: [wkxCoordinates]
  });
  
  return geometry.toWkt();
}

/**
 * Convert coordinates array to WKB (Well-Known Binary) format
 */
export function coordinatesToWKB(coordinates: LatLng[]): Uint8Array {
  // Remove the closing coordinate if it duplicates the first one
  const coords = coordinates.length > 0 && 
    coordinates[0].lat === coordinates[coordinates.length - 1].lat &&
    coordinates[0].lng === coordinates[coordinates.length - 1].lng
    ? coordinates.slice(0, -1)
    : coordinates;

  // Convert to WKX format: [lng, lat] pairs
  const wkxCoordinates = coords.map(coord => [coord.lng, coord.lat]);
  
  const geometry = wkx.Geometry.parseGeoJSON({
    type: "Polygon",
    coordinates: [wkxCoordinates]
  });
  
  return geometry.toWkb();
}

/**
 * Convert coordinates array to WKB hex string
 */
export function coordinatesToWKBHex(coordinates: LatLng[]): string {
  const wkb = coordinatesToWKB(coordinates);
  return buf2hex(wkb.buffer as ArrayBuffer);
}

/**
 * Convert WKB hex string back to coordinates
 */
export function wkbHexToCoordinates(hexString: string): LatLng[] {
  try {
    const typedArray = new Uint8Array(
      hexString.match(/[\da-f]{2}/gi)?.map(h => parseInt(h, 16)) || []
    );
    const wkbArray = Object.keys(typedArray).map(
      (data) => typedArray[`${data}` as any] as number
    );
    
    const geometry = wkx.Geometry.parse(Buffer.from(wkbArray));
    const geoJSON = geometry.toGeoJSON() as any;
    
    if (geoJSON.type === 'Polygon' && geoJSON.coordinates && geoJSON.coordinates[0]) {
      const coordinates: LatLng[] = geoJSON.coordinates[0].map((coord: [number, number]) => ({
        lat: coord[1],
        lng: coord[0]
      }));
      
      // Ensure the polygon is closed
      if (coordinates.length > 0) {
        const firstCoord = coordinates[0];
        const lastCoord = coordinates[coordinates.length - 1];
        if (firstCoord.lat !== lastCoord.lat || firstCoord.lng !== lastCoord.lng) {
          coordinates.push(firstCoord);
        }
      }
      
      return coordinates;
    }
  } catch (error) {
    console.error('Error converting WKB hex to coordinates:', error);
  }
  
  return [];
}

/**
 * Process polygon completion with WKT/WKB conversion
 */
export function processPolygonCompletion(coordinates: LatLng[]): {
  coordinates: LatLng[];
  wkt: string;
  wkb: Uint8Array;
  wkbHex: string;
  geoJSON: any;
} {
  const wkt = coordinatesToWKT(coordinates);
  const wkb = coordinatesToWKB(coordinates);
  const wkbHex = coordinatesToWKBHex(coordinates);
  
  // Create GeoJSON representation
  const coords = coordinates.length > 0 && 
    coordinates[0].lat === coordinates[coordinates.length - 1].lat &&
    coordinates[0].lng === coordinates[coordinates.length - 1].lng
    ? coordinates.slice(0, -1)
    : coordinates;

  const geoJSON = {
    type: "Polygon",
    coordinates: [coords.map(coord => [coord.lng, coord.lat])]
  };
  
  return {
    coordinates,
    wkt,
    wkb,
    wkbHex,
    geoJSON
  };
}