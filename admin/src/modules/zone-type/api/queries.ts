import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  ZoneTypeResponse,
  ListZoneTypeParams,
  ListZoneTypeResponse,
  AlgorithmsResponse,
} from '../types/zone-type';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

/**
 * Hook for listing zone types with pagination and filters
 */
export const useListZoneType = ({
  page,
  limit,
  search,
  algorithm,
  isActive,
  includeInactive,
  includeZoneCount,
  sortBy,
}: ListZoneTypeParams) => {
  const { hasPermission } = useRoleBasedAccess();

  return useQuery({
    placeholderData: keepPreviousData,
    enabled: hasPermission(RBAC_PERMISSIONS.ZONE_TYPE.LIST),
    queryKey: [
      'zone-types',
      page,
      limit,
      search,
      algorithm,
      isActive,
      includeInactive,
      includeZoneCount,
      sortBy,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListZoneTypeResponse> => {
      return apiClient.get('/zone-types', {
        params: {
          page,
          limit,
          search,
          algorithm,
          isActive,
          includeInactive,
          includeZoneCount,
          sortBy,
        },
      });
    },
  });
};

/**
 * Hook for getting a single zone type by ID
 */
export const useGetZoneType = (id: string | null) => {
  return useQuery({
    queryKey: ['zone-type', id],
    queryFn: (): Promise<ZoneTypeResponse> => {
      return apiClient.get(`/zone-types/${id || ''}`);
    },
    enabled: !!id,
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook for getting available algorithms
 */
export const useGetAlgorithms = () => {
  return useQuery({
    queryKey: ['zone-type-algorithms'],
    queryFn: (): Promise<AlgorithmsResponse> => {
      return apiClient.get('/zone-types/meta/algorithms');
    },
    refetchOnWindowFocus: false,
    staleTime: 5 * 60 * 1000, // 5 minutes - algorithms don't change often
  });
};