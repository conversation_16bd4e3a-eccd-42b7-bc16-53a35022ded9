'use client';

import { Skeleton } from '@/components/ui/skeleton';

export function ZoneTypeTableLoading() {
  return (
    <div className='space-y-2'>
      <div className='rounded-md border'>
        <div className='overflow-x-auto'>
          <table className='w-full'>
            <thead>
              <tr className='border-b bg-gray-50'>
                <th className='h-11 px-4 text-left align-middle w-[200px]'>
                  <Skeleton className='h-4 w-16' />
                </th>
                <th className='h-11 px-4 text-left align-middle w-[300px]'>
                  <Skeleton className='h-4 w-20' />
                </th>
                <th className='h-11 px-4 text-left align-middle w-[120px]'>
                  <Skeleton className='h-4 w-16' />
                </th>
                <th className='h-11 px-4 text-left align-middle w-[120px]'>
                  <Skeleton className='h-4 w-24' />
                </th>
                <th className='h-11 px-4 text-center align-middle w-[100px]'>
                  <div className='flex justify-center'>
                    <Skeleton className='h-4 w-12' />
                  </div>
                </th>
                <th className='h-11 px-4 text-center align-middle w-[250px]'>
                  <div className='flex justify-center'>
                    <Skeleton className='h-4 w-16' />
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              {Array.from({ length: 5 }).map((_, index) => (
                <tr key={index} className='border-b transition-colors'>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-32' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-48' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-6 w-16 rounded-full' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <Skeleton className='h-4 w-20' />
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center'>
                      <Skeleton className='h-6 w-16 rounded-full' />
                    </div>
                  </td>
                  <td className='px-4 py-3 align-middle'>
                    <div className='flex justify-center gap-1'>
                      <Skeleton className='h-8 w-12' />
                      <Skeleton className='h-8 w-16' />
                      <Skeleton className='h-8 w-14' />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}