'use client';

import { Button } from '@/components/ui/button';
import { Spinner } from '@/components/ui/spinner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ZoneTypeToggleModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading: boolean;
  zoneTypeName: string;
  currentStatus: boolean; // true = active, false = inactive
}

export const ZoneTypeToggleModal = ({
  isOpen,
  onClose,
  onConfirm,
  isLoading,
  zoneTypeName,
  currentStatus,
}: ZoneTypeToggleModalProps) => {
  const action = currentStatus ? 'deactivate' : 'activate';
  const actionCapitalized = currentStatus ? 'Deactivate' : 'Activate';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-sm'>
        <DialogHeader>
          <DialogTitle>{actionCapitalized} Zone Type</DialogTitle>
          <DialogDescription>
            Are you sure you want to {action} the zone type "{zoneTypeName}"?
            {currentStatus 
              ? ' This will make the zone type unavailable for use in zones.'
              : ' This will make the zone type available for use in zones.'
            }
          </DialogDescription>
        </DialogHeader>

        <div className='flex gap-3 pt-4'>
          <Button 
            type='button' 
            variant='outline' 
            onClick={onClose} 
            className='flex-1'
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button 
            type='button' 
            onClick={onConfirm} 
            className={`flex-1 ${
              !currentStatus 
                ? 'bg-green-600 hover:bg-green-700 text-white' 
                : ''
            }`}
            variant={currentStatus ? 'destructive' : 'default'}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                {actionCapitalized}...
                <Spinner className='ml-2 h-4 w-4' />
              </>
            ) : (
              actionCapitalized
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};