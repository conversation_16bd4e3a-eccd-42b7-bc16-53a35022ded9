import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import {
  ListDriverProductParams,
  ListVehicleProductResponse,
} from '../types/driver-product';

export const useListDriverProducts = (vehicleId: string, params: ListDriverProductParams) => {
  return useQuery({
    placeholderData: keepPreviousData,
    queryKey: [
      'vehicle-products',
      vehicleId,
      params.page,
      params.limit,
      params.productName,
    ],
    refetchOnWindowFocus: false,
    queryFn: (): Promise<ListVehicleProductResponse> => {
      return apiClient.get(`/driver-city-products/driver-vehicle/${vehicleId}/city-products`, {
        params: {
          page: params.page,
          limit: params.limit,
          // Note: productName search not yet supported by vehicle-specific endpoint
          // productName: params.productName,
        },
      });
    },
    enabled: !!vehicleId,
  });
};

// Re-export the existing city products query for use in driver products (for add modal)
export { useListCityProducts as useAvailableCityProducts } from '@/modules/city/api/city-product-queries';