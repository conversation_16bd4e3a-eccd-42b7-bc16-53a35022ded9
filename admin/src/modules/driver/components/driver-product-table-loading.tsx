export function DriverProductTableLoading() {
  return (
     <div className='space-y-3'>
        {Array.from({ length: 1 }).map((_, index) => (
           <div key={index} className='flex items-center space-x-4 p-4'>
              <div className='h-10 w-10 rounded bg-gray-200 animate-pulse' />
              <div className='flex-1 space-y-2'>
                 <div className='h-4 w-32 bg-gray-200 rounded animate-pulse' />
                 <div className='h-3 w-24 bg-gray-200 rounded animate-pulse' />
              </div>
              <div className='h-6 w-20 bg-gray-200 rounded animate-pulse' />
              <div className='h-6 w-16 bg-gray-200 rounded animate-pulse' />
              <div className='h-8 w-24 bg-gray-200 rounded animate-pulse' />
              <div className='h-8 w-8 bg-gray-200 rounded animate-pulse' />
           </div>
        ))}
     </div>
  );
}