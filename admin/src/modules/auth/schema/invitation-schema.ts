import { z } from 'zod';
import { passwordSchema } from './password-schema';

// Invitation form validation schema
export const invitationFormSchema = z
  .object({
    password: passwordSchema,
    passwordConfirmation: z.string().min(1, 'Please confirm your password'),
  })
  .refine((data) => data.password === data.passwordConfirmation, {
    message: "Passwords don't match",
    path: ['passwordConfirmation'],
  });

// Type export for form data
export type InvitationFormData = z.infer<typeof invitationFormSchema>;