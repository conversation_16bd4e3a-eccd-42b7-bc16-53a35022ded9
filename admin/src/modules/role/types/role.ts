// Role interface for API responses
export interface Role {
   id: string;
   identifier: string;
   name: string;
   description: string | null;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// API response structure for listing roles
export interface ListRoleResponse {
   success: boolean;
   message: string;
   data: Role[];
   meta?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPrevPage: boolean;
   };
   timestamp: number;
}

// API response structure for single role
export interface RoleResponse {
   success: boolean;
   message: string;
   data: Role;
   timestamp: number;
}

// Request for creating role
export interface CreateRoleRequest {
   name: string;
   description?: string;
}

// Request for updating role
export interface UpdateRoleRequest {
   name?: string;
   description?: string;
}

// Parameters for listing roles with pagination
export interface ListRoleParams {
   page?: number;
   limit?: number;
   sortBy?: string;
   sortOrder?: 'asc' | 'desc';
}

// Permission interface for API responses
export interface Permission {
   id: string;
   name: string;
   description: string;
   resource: string;
   action: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}

// Permission data structure grouped by resource
export interface PermissionData {
   [resourceName: string]: Permission[];
}

// API response structure for permissions
export interface PermissionResponse {
   success: boolean;
   message: string;
   data: PermissionData;
   timestamp: number;
}

// API response structure for role permissions
export interface RolePermissionListResponse {
   success: boolean;
   message: string;
   data: Permission[];
   timestamp: number;
}

// Request for adding/removing permissions to/from a role
export interface AddRemovePermissionsRequest {
   permissionIds: string[];
}

// API response for add/remove permissions
export interface RolePermissionResponse {
   success: boolean;
   message: string;
   timestamp: number;
}