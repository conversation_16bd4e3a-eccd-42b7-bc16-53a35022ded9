'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { CheckboxTree } from '@/components/ui/checkbox-tree';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
   Sheet,
   SheetContent,
   SheetDescription,
   SheetFooter,
   SheetHeader,
   SheetTitle,
} from '@/components/ui/sheet';
import { toast } from '@/lib/toast';
import { useQueryClient } from '@tanstack/react-query';
import { Fragment, useEffect, useId } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useGetPermissions, useGetRolePermissions } from '../api/queries';
import { useAddPermissionsToRole, useRemovePermissionsFromRole } from '../api/mutations';
import { Permission, PermissionData } from '../types/role';

interface TreeNode {
   id: string;
   label: string;
   defaultChecked?: boolean;
   children?: TreeNode[];
}

interface PermissionFormData {
   selectedPermissions: string[];
}

interface RolePermissionSheetProps {
   roleId: string | null;
   isOpen: boolean;
   onClose: () => void;
}

function transformPermissionsToTree(permissions: PermissionData): TreeNode {
   const children: TreeNode[] = Object.entries(permissions).map(
      ([resourceName, resourcePermissions]) => ({
         id: resourceName,
         label: resourceName.charAt(0).toUpperCase() + resourceName.slice(1),
         children: resourcePermissions.map(permission => ({
            id: permission.id,
            label: permission.description,
         })),
      })
   );

   return {
      id: 'root',
      label: 'All Permissions',
      children,
   };
}

export function RolePermissionSheet({ roleId, isOpen, onClose }: RolePermissionSheetProps) {
   const id = useId();
   const queryClient = useQueryClient();

   // API hooks
   const permissionsQuery = useGetPermissions();
   const rolePermissionsQuery = useGetRolePermissions(roleId);
   const addPermissionsMutation = useAddPermissionsToRole();
   const removePermissionsMutation = useRemovePermissionsFromRole();

   const form = useForm<PermissionFormData>({
      defaultValues: {
         selectedPermissions: [],
      },
   });

   const { control, handleSubmit: hookFormSubmit, reset } = form;

   // Load existing permissions when role permissions are fetched
   useEffect(() => {
      if (rolePermissionsQuery.data?.data) {
         const existingPermissionIds = rolePermissionsQuery.data.data.map((p: Permission) => p.id);
         reset({
            selectedPermissions: existingPermissionIds,
         });
      }
   }, [rolePermissionsQuery.data, reset]);

   // Get permissions data
   const permissionsData = permissionsQuery.data?.data || {};
   const permissionTree = transformPermissionsToTree(permissionsData);
   const existingPermissionIds =
      rolePermissionsQuery.data?.data?.map((p: Permission) => p.id) || [];

   const handleSubmit = async (data: PermissionFormData) => {
      if (!roleId) return;

      const currentSelections = new Set(data.selectedPermissions);
      const existingSelections = new Set(existingPermissionIds);

      // Calculate permissions to add and remove
      const toAdd = [...currentSelections].filter(id => !existingSelections.has(id));
      const toRemove = [...existingSelections].filter(id => !currentSelections.has(id));

      try {
         // Add new permissions
         if (toAdd.length > 0) {
            await addPermissionsMutation.mutateAsync({
               roleId,
               permissionIds: toAdd,
            });
         }

         // Remove permissions
         if (toRemove.length > 0) {
            await removePermissionsMutation.mutateAsync({
               roleId,
               permissionIds: toRemove,
            });
         }

         // Invalidate queries to refresh data
         queryClient.invalidateQueries({ queryKey: ['role-permissions', roleId] });
         queryClient.invalidateQueries({ queryKey: ['permissions'] });
         queryClient.invalidateQueries({ queryKey: ['my-permissions'] });

         // try resetting all queries  to clear data
         queryClient.resetQueries();

         toast.success('Permissions updated successfully');
         onClose();
      } catch (error) {
         console.error('Error updating permissions:', error);
         toast.error('Failed to update permissions');
      }
   };

   const handleCancel = () => {
      // Reset form to original state
      if (rolePermissionsQuery.data?.data) {
         const existingPermissionIds = rolePermissionsQuery.data.data.map((p: Permission) => p.id);
         reset({
            selectedPermissions: existingPermissionIds,
         });
      }
      onClose();
   };

   const isLoading = permissionsQuery.isLoading || rolePermissionsQuery.isLoading;
   const isSubmitting = addPermissionsMutation.isPending || removePermissionsMutation.isPending;

   if (!isOpen) return null;

   return (
      <Sheet open={isOpen} onOpenChange={onClose}>
         <SheetContent preventOutsideClickClose={true} className='w-[400px] sm:w-[540px] gap-0'>
            <SheetHeader className='mb-0'>
               <SheetTitle>Role Permissions</SheetTitle>
               <SheetDescription>
                  Select the permissions for this role. Check resources and specific actions to
                  define what this role can access.
               </SheetDescription>
            </SheetHeader>

            {isLoading ? (
               <div className='flex items-center justify-center h-[60vh]'>
                  <div className='text-gray-500'>Loading permissions...</div>
               </div>
            ) : (
               <form onSubmit={hookFormSubmit(handleSubmit)}>
                  <ScrollArea
                     scrollThumbClassName='bg-gray-500 hover:bg-gray-600'
                     type='always'
                     className='w-full py-6'
                     style={{
                        height: 'calc(100vh - 175px)',
                     }}
                  >
                     <div className='space-y-3 pr-4'>
                        <Controller
                           name='selectedPermissions'
                           control={control}
                           render={({ field }) => {
                              // Helper function to get all child permission IDs for a node
                              const getAllChildIds = (node: TreeNode): string[] => {
                                 if (!node.children) {
                                    return [node.id];
                                 }
                                 return node.children.flatMap(child => getAllChildIds(child));
                              };

                              // Helper function to check if all children of a node are selected
                              const areAllChildrenSelected = (node: TreeNode): boolean => {
                                 if (!node.children) {
                                    return field.value.includes(node.id);
                                 }
                                 const childIds = getAllChildIds(node);
                                 const actualPermissionIds = childIds.filter(
                                    id =>
                                       id !== 'root' && !Object.keys(permissionsData).includes(id)
                                 );
                                 return actualPermissionIds.every(id => field.value.includes(id));
                              };

                              // Helper function to check if some children of a node are selected
                              const areSomeChildrenSelected = (node: TreeNode): boolean => {
                                 if (!node.children) {
                                    return field.value.includes(node.id);
                                 }
                                 const childIds = getAllChildIds(node);
                                 const actualPermissionIds = childIds.filter(
                                    id =>
                                       id !== 'root' && !Object.keys(permissionsData).includes(id)
                                 );
                                 return actualPermissionIds.some(id => field.value.includes(id));
                              };

                              // Helper function to get checkbox state
                              const getCheckboxState = (
                                 node: TreeNode
                              ): boolean | 'indeterminate' => {
                                 if (node.id === 'root') {
                                    const allPermissionIds = getAllChildIds(node).filter(
                                       id =>
                                          id !== 'root' &&
                                          !Object.keys(permissionsData).includes(id)
                                    );
                                    const allSelected = allPermissionIds.every(id =>
                                       field.value.includes(id)
                                    );
                                    const someSelected = allPermissionIds.some(id =>
                                       field.value.includes(id)
                                    );

                                    if (allSelected) return true;
                                    if (someSelected) return 'indeterminate';
                                    return false;
                                 }

                                 if (!node.children) {
                                    return field.value.includes(node.id);
                                 }

                                 const allSelected = areAllChildrenSelected(node);
                                 const someSelected = areSomeChildrenSelected(node);

                                 if (allSelected) return true;
                                 if (someSelected) return 'indeterminate';
                                 return false;
                              };

                              // Helper function to handle checkbox change
                              const handleCheckboxChange = (node: TreeNode) => {
                                 if (!node.children) {
                                    // Leaf node - toggle individual permission
                                    if (field.value.includes(node.id)) {
                                       field.onChange(field.value.filter(id => id !== node.id));
                                    } else {
                                       field.onChange([...field.value, node.id]);
                                    }
                                 } else {
                                    // Parent node - toggle all children
                                    const childIds = getAllChildIds(node).filter(
                                       id =>
                                          id !== 'root' &&
                                          !Object.keys(permissionsData).includes(id)
                                    );
                                    const allSelected = areAllChildrenSelected(node);

                                    if (allSelected) {
                                       // Deselect all children
                                       field.onChange(
                                          field.value.filter(id => !childIds.includes(id))
                                       );
                                    } else {
                                       // Select all children
                                       const newSelected = [
                                          ...new Set([...field.value, ...childIds]),
                                       ];
                                       field.onChange(newSelected);
                                    }
                                 }
                              };

                              return (
                                 <CheckboxTree
                                    tree={permissionTree}
                                    renderNode={({ node, children }) => {
                                       const checkboxState = getCheckboxState(node);
                                       const isParentNode = !!node.children;

                                       return (
                                          <Fragment key={`${id}-${node.id}`}>
                                             <div className='flex items-center gap-2 ms-6'>
                                                <Checkbox
                                                   id={`${id}-${node.id}`}
                                                   checked={checkboxState}
                                                   onCheckedChange={() =>
                                                      handleCheckboxChange(node)
                                                   }
                                                   disabled={isSubmitting}
                                                />
                                                <Label
                                                   htmlFor={`${id}-${node.id}`}
                                                   className={
                                                      isParentNode
                                                         ? 'font-medium text-gray-900'
                                                         : 'text-gray-700'
                                                   }
                                                >
                                                   {isParentNode
                                                      ? node.label.replace('_', ' ')
                                                      : node.label}
                                                </Label>
                                             </div>
                                             {children && (
                                                <div className='ms-6 space-y-3'>{children}</div>
                                             )}
                                          </Fragment>
                                       );
                                    }}
                                 />
                              );
                           }}
                        />
                     </div>
                  </ScrollArea>

                  <SheetFooter className='!py-0 px-4 mt-0 flex-row justify-between'>
                     <Button
                        className='w-1/2'
                        type='button'
                        variant='outline'
                        onClick={handleCancel}
                        disabled={isSubmitting}
                     >
                        Cancel
                     </Button>
                     <Button className='w-1/2' type='submit' disabled={isSubmitting}>
                        {isSubmitting ? 'Saving...' : 'Apply Permissions'}
                     </Button>
                  </SheetFooter>
               </form>
            )}
         </SheetContent>
      </Sheet>
   );
}
