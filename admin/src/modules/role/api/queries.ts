import { apiClient } from '@/lib/api-client';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { RoleResponse, ListRoleParams, ListRoleResponse, PermissionResponse, RolePermissionListResponse } from '../types/role';
import { useRoleBasedAccess } from '@/role-based-access/use-role-based-access';
import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';

export const useListRole = ({ page, limit, sortBy, sortOrder }: ListRoleParams = {}) => {
   const { hasPermission } = useRoleBasedAccess();

   return useQuery({
      placeholderData: keepPreviousData,
      enabled: hasPermission(RBAC_PERMISSIONS.ROLES.LIST),
      queryKey: ['roles', page, limit, sortBy, sortOrder],
      refetchOnWindowFocus: false,
      queryFn: (): Promise<ListRoleResponse> => {
         return apiClient.get('/roles', {
            params: {
               page,
               limit,
               sortBy,
               sortOrder,
            },
         });
      },
   });
};

export const useGetRole = (id: string | null) => {
   return useQuery({
      queryKey: ['role', id],
      queryFn: (): Promise<RoleResponse> => {
         return apiClient.get(`/roles/${id || ''}`);
      },
      enabled: !!id,
      refetchOnWindowFocus: false,
   });
};

export const useGetPermissions = (roleId?: string) => {
   return useQuery({
      queryKey: ['permissions', roleId],
      queryFn: (): Promise<PermissionResponse> => {
         return apiClient.get('/permissions', {
            params: roleId ? { roleId } : {},
         });
      },
      refetchOnWindowFocus: false,
   });
};

export const useGetRolePermissions = (roleId: string | null) => {
   return useQuery({
      queryKey: ['role-permissions', roleId],
      queryFn: (): Promise<RolePermissionListResponse> => {
         return apiClient.get(`/roles/${roleId}/list-permissions`);
      },
      enabled: !!roleId,
      refetchOnWindowFocus: false,
   });
};