import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import { CreateRoleRequest, RoleResponse, UpdateRoleRequest, RolePermissionResponse } from '../types/role';

/**
 * Hook for creating a new role
 */
export const useCreateRole = () => {
   return useMutation({
      mutationFn: async (data: CreateRoleRequest): Promise<RoleResponse> => {
         return apiClient.post('/roles', data);
      },
   });
};

/**
 * Hook for updating a role
 */
export const useUpdateRole = () => {
   return useMutation({
      mutationFn: async (
         data: { id: string } & UpdateRoleRequest
      ): Promise<RoleResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/roles/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a role
 */
export const useDeleteRole = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/roles/${id}`);
      },
   });
};

/**
 * Hook for adding permissions to a role
 */
export const useAddPermissionsToRole = () => {
   return useMutation({
      mutationFn: async (data: { roleId: string; permissionIds: string[] }): Promise<RolePermissionResponse> => {
         const { roleId, permissionIds } = data;
         return apiClient.post(`/roles/${roleId}/add-permissions`, { permissionIds });
      },
   });
};

/**
 * Hook for removing permissions from a role
 */
export const useRemovePermissionsFromRole = () => {
   return useMutation({
      mutationFn: async (data: { roleId: string; permissionIds: string[] }): Promise<RolePermissionResponse> => {
         const { roleId, permissionIds } = data;
         return apiClient.post(`/roles/${roleId}/remove-permissions`, { permissionIds });
      },
   });
};