import { RBAC_PERMISSIONS } from '@/role-based-access/permissions';
import { ROUTE_URLS } from '@/data/route-urls';
import {
   BadgeDollarSign,
   Car,
   Languages,
   MapPin,
   Package,
   ShieldUser,
   ShoppingBag,
   Target,
   Truck,
   UserCog,
} from 'lucide-react';

export const SIDEBAR_ROUTES = [
   {
      title: 'Drivers',
      url: ROUTE_URLS.DASHBOARD_DRIVERS,
      icon: Car,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.DRIVER,
   },
   {
      title: 'Cities',
      url: ROUTE_URLS.DASHBOARD_CITIES,
      icon: MapPin,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.CITY,
   },
   {
      title: 'Languages',
      url: ROUTE_URLS.DASHBOARD_LANGUAGES,
      icon: Languages,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.LANGUAGE,
   },
   {
      title: 'Products',
      url: ROUTE_URLS.DASHBOARD_PRODUCTS,
      icon: ShoppingBag,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.PRODUCT,
   },
   {
      title: 'Product Services',
      url: ROUTE_URLS.DASHBOARD_PRODUCT_SERVICES,
      icon: Package,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.PRODUCT_SERVICE,
   },
   {
      title: 'Vehicle Categories',
      url: ROUTE_URLS.DASHBOARD_VEHICLE_CATEGORIES,
      icon: Truck,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.VEHICLE_CATEGORY,
   },
   {
      title: 'Zone Types',
      url: ROUTE_URLS.DASHBOARD_ZONE_TYPES,
      icon: Target,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.ZONE_TYPE,
   },
   {
      title: 'Roles',
      url: ROUTE_URLS.DASHBOARD_ROLES,
      icon: UserCog,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.ROLES,
   },
   {
      title: 'Admins',
      url: ROUTE_URLS.DASHBOARD_ADMINS,
      icon: ShieldUser,
      isActive: false,
      pagePermission: { ...RBAC_PERMISSIONS.SUB_ADMIN, ...RBAC_PERMISSIONS.CITY_ADMIN },
   },
   {
      title: 'Charges',
      url: ROUTE_URLS.DASHBOARD_CHARGE_GROUPS,
      icon: BadgeDollarSign,
      isActive: false,
      pagePermission: RBAC_PERMISSIONS.CHARGE_GROUPS,
   },
];
