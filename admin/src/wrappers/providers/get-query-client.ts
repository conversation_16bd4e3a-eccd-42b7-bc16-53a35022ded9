import { toast } from '@/lib/toast';
// Since QueryClientProvider relies on useContext under the hood, we have to put 'use client' on top
import {
   defaultShouldDehydrateQuery,
   isServer,
   MutationCache,
   QueryClient,
} from '@tanstack/react-query';

function makeQueryClient() {
   return new QueryClient({
      defaultOptions: {
         mutations: {
            networkMode: 'always',
         },
         // networkMode: 'always',
         queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 0,
         },
         dehydrate: {
            // include pending queries in dehydration
            shouldDehydrateQuery: query =>
               defaultShouldDehydrateQuery(query) || query.state.status === 'pending',
         },
      },
      mutationCache: new MutationCache({
         onError: (error, _variables, _context, mutation) => {
            // Check if this mutation should skip global error handling
            if (!mutation.meta?.skipGlobalError) {
               toast.error(error);
            }
         },
      }),
   });
}

let browserQueryClient: QueryClient | undefined = undefined;

export function getQueryClient() {
   if (isServer) {
      // Server: always make a new query client
      return makeQueryClient();
   } else {
      // Browser: make a new query client if we don't already have one
      // This is very important, so we don't re-make a new client if React
      // suspends during the initial render. This may not be needed if we
      // have a suspense boundary BELOW the creation of the query client
      if (!browserQueryClient) browserQueryClient = makeQueryClient();
      return browserQueryClient;
   }
}
