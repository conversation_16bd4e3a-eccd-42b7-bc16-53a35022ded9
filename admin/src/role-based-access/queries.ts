import { apiClient } from '@/lib/api-client';
import { useQuery } from '@tanstack/react-query';
import { minutesToMilliseconds } from 'date-fns';

export const useListMyPermissions = () => {
   return useQuery({
      queryKey: ['my-permissions'],
      refetchOnWindowFocus: false,
      staleTime: minutesToMilliseconds(5), // refetch on a 5 minute interval
      queryFn: (): Promise<MyPermissions> => {
         return apiClient.get('/permissions/my-permissions');
      },
   });
};

export type MyPermissions = {
   success: boolean;
   message: string;
   data: Permission[];
   timestamp: number;
};

export interface Permission {
   id: string;
   name: string;
   description: string;
   resource: string;
   action: string;
   createdAt: string;
   updatedAt: string;
   deletedAt: string | null;
}
