'use client';

import { useParams, usePathname, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { PAGE_ROUTE_PERMISSIONS } from './page-permissions';
import { useRoleBasedAccess } from './use-role-based-access';
import { ROUTE_URLS } from '@/data/route-urls';
import { toast } from '@/lib/toast';

export const usePageKickoutBasedOnPermissions = () => {
   const { hasAnyPermission, isLoading, fallBackRoute } = useRoleBasedAccess();
   const pathName = usePathname();
   const params = useParams();

   const router = useRouter();

   const currentPageRoute = PAGE_ROUTE_PERMISSIONS.find(route => {
      // custom route /dashboard/drivers/:id
      if (route.url.includes('/dashboard/drivers') && params.id) {
         return route.url === ROUTE_URLS.DASHBOARD_DRIVERS_DETAILS;
      }

      if (route.url.includes('/dashboard/cities') && params.id) {
         return route.url === ROUTE_URLS.DASHBOARD_CITIES_DETAILS;
      }

      return route.url === pathName;
   });

   const currentPagePermissionObject = currentPageRoute?.pagePermission;
   const currentPagePermissions = Object.keys(currentPagePermissionObject ?? {}).map(
      permissionKey => (currentPagePermissionObject as any)[permissionKey]
   );
   const hasPermission = hasAnyPermission(currentPagePermissions);

   useEffect(() => {
      if (!isLoading && !hasPermission && fallBackRoute) {
         toast.error("You don't have permission to view this page");
         router.push(fallBackRoute);
      }
   }, [isLoading, hasPermission, router, fallBackRoute]);
};
