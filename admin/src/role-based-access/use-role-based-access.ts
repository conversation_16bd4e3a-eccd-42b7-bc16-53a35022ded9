import { toast } from '@/lib/toast';
import { useCallback, useMemo } from 'react';
import { PAGE_ROUTE_PERMISSIONS } from './page-permissions';
import { useListMyPermissions } from './queries';

export const useRoleBasedAccess = () => {
   const myPermissionsQuery = useListMyPermissions();
   const myPermissions = myPermissionsQuery.data?.data;
   const myPermissionsLoading = myPermissionsQuery.isLoading;
   const myPermissionsList = myPermissions?.map(permission => permission.name);

   const withPermission = useCallback(
      (
         permission: string,
         callback: () => void,
         config: {
            showToast?: boolean;
            toastMessage?: string;
         } = {
            showToast: true,
            toastMessage: 'You do not have permission to do this action',
         }
      ) => {
         const hasPermission = myPermissionsList?.includes(permission);

         if (!hasPermission || myPermissionsLoading === true) {
            if (config.showToast) {
               toast.error(config.toastMessage);
            }

            return;
         }

         return callback();
      },
      [myPermissionsList, myPermissionsLoading]
   );

   const hasPermission = useCallback(
      (permission: string) => {
         const hasPermission = myPermissionsList?.includes(permission);
         return Boolean(hasPermission && myPermissionsLoading === false);
      },
      [myPermissionsList, myPermissionsLoading]
   );

   const hasAnyPermission = useCallback(
      (permissions: string[]) => {
         const hasPermission = myPermissionsList?.some(myPermission =>
            permissions.includes(myPermission)
         );

         return Boolean(hasPermission && myPermissionsLoading === false);
      },
      [myPermissionsList, myPermissionsLoading]
   );

   const availableRoutesWithPermissions = useMemo(
      () =>
         PAGE_ROUTE_PERMISSIONS.filter(menu => {
            const allPagePermissions = Object.keys(menu.pagePermission).map(
               pagePermission => (menu.pagePermission as any)[pagePermission]
            );

            return hasAnyPermission(allPagePermissions);
         }).map(route => route.url),
      [hasAnyPermission]
   );

   const fallBackRoute = useMemo(
      () => availableRoutesWithPermissions?.[0] ?? '',
      [availableRoutesWithPermissions]
   );

   return {
      withPermission,
      hasPermission,
      hasAnyPermission,
      isLoading: myPermissionsLoading,
      availableRoutesWithPermissions,
      fallBackRoute,
   };
};

export type USE_ROLE_BASED_ACCESS = ReturnType<typeof useRoleBasedAccess>;
